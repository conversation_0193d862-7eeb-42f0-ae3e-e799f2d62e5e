import { chromium } from 'playwright-extra'
import stealth from 'puppeteer-extra-plugin-stealth'
import { findChromium } from '#/utils/checkChrome'
import type { BrowserSession, StorageState } from './types'

chromium.use(stealth())

export class BrowserSessionManager {
  private chromePath: string | null = null
  private static instance: BrowserSessionManager

  private constructor() {}

  public static getInstance() {
    if (!BrowserSessionManager.instance) {
      BrowserSessionManager.instance = new BrowserSessionManager()
    }
    return BrowserSessionManager.instance
  }

  public setChromePath(path: string) {
    this.chromePath = path
  }

  private async initChromePath() {
    if (!this.chromePath) {
      this.chromePath = await findChromium()
      if (!this.chromePath) throw new Error('未找到浏览器')
    }
  }

  private async createBrowser(headless = true) {
    await this.initChromePath()
    return chromium.launch({
      headless,
      executablePath: this.chromePath as string,
    })
  }

  public async createSession(
    headless = true,
    storageState?: StorageState,
  ): Promise<BrowserSession> {
    const browser = await this.createBrowser(headless)
    const context = await browser.newContext(
      storageState ? { storageState } : undefined,
    )
    const page = await context.newPage()
    return { browser, context, page }
  }
}
