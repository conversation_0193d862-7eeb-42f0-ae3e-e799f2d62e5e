export interface ModelsByVendor {
  [vendor: string]: string[]
}

export interface VendorInfo {
  name: string
  icon?: string
  color?: string
}

export const VENDOR_INFO: Record<string, VendorInfo> = {
  openai: { name: 'OpenAI', color: 'bg-green-500' },
  anthropic: { name: 'Anthropic', color: 'bg-orange-500' },
  google: { name: 'Google', color: 'bg-blue-500' },
  meta: { name: 'Meta', color: 'bg-blue-600' },
  microsoft: { name: 'Microsoft', color: 'bg-cyan-500' },
  deepseek: { name: 'DeepSeek', color: 'bg-purple-500' },
  qwen: { name: 'Qwen', color: 'bg-red-500' },
  mistral: { name: 'Mistral', color: 'bg-orange-600' },
  cohere: { name: 'Cohere', color: 'bg-green-600' },
  perplexity: { name: 'Perplexity', color: 'bg-indigo-500' },
  x: { name: 'xAI', color: 'bg-gray-800' },
  other: { name: '其他', color: 'bg-gray-500' },
}

/**
 * 根据模型名称推断厂商
 */
export function inferVendorFromModel(modelId: string): string {
  const modelLower = modelId.toLowerCase()
  
  // OpenAI 模型
  if (modelLower.includes('gpt') || 
      modelLower.includes('o1') || 
      modelLower.includes('text-davinci') ||
      modelLower.includes('text-curie') ||
      modelLower.includes('text-babbage') ||
      modelLower.includes('text-ada') ||
      modelLower.includes('davinci') ||
      modelLower.includes('curie') ||
      modelLower.includes('babbage') ||
      modelLower.includes('ada')) {
    return 'openai'
  }
  
  // Anthropic 模型
  if (modelLower.includes('claude')) {
    return 'anthropic'
  }
  
  // Google 模型
  if (modelLower.includes('gemini') || 
      modelLower.includes('palm') ||
      modelLower.includes('bison') ||
      modelLower.includes('gecko') ||
      modelLower.includes('google')) {
    return 'google'
  }
  
  // Meta 模型
  if (modelLower.includes('llama') || 
      modelLower.includes('meta')) {
    return 'meta'
  }
  
  // Microsoft 模型
  if (modelLower.includes('phi') ||
      modelLower.includes('microsoft')) {
    return 'microsoft'
  }
  
  // DeepSeek 模型
  if (modelLower.includes('deepseek')) {
    return 'deepseek'
  }
  
  // Qwen 模型
  if (modelLower.includes('qwen') || 
      modelLower.includes('qwq')) {
    return 'qwen'
  }
  
  // Mistral 模型
  if (modelLower.includes('mistral') ||
      modelLower.includes('mixtral') ||
      modelLower.includes('codestral')) {
    return 'mistral'
  }
  
  // Cohere 模型
  if (modelLower.includes('command') ||
      modelLower.includes('cohere')) {
    return 'cohere'
  }
  
  // Perplexity 模型
  if (modelLower.includes('pplx') ||
      modelLower.includes('perplexity')) {
    return 'perplexity'
  }
  
  // xAI 模型
  if (modelLower.includes('grok')) {
    return 'x'
  }
  
  return 'other'
}

/**
 * 将模型列表按厂商分类
 */
export function classifyModelsByVendor(models: string[]): ModelsByVendor {
  const classified: ModelsByVendor = {}
  
  models.forEach(model => {
    const vendor = inferVendorFromModel(model)
    if (!classified[vendor]) {
      classified[vendor] = []
    }
    classified[vendor].push(model)
  })
  
  // 对每个厂商的模型进行排序
  Object.keys(classified).forEach(vendor => {
    classified[vendor].sort()
  })
  
  return classified
}

/**
 * 获取所有有模型的厂商列表，按优先级排序
 */
export function getVendorsWithModels(modelsByVendor: ModelsByVendor): string[] {
  const vendors = Object.keys(modelsByVendor).filter(vendor => 
    modelsByVendor[vendor].length > 0
  )
  
  // 定义厂商优先级
  const vendorPriority = [
    'openai', 'anthropic', 'google', 'deepseek', 'meta', 
    'qwen', 'mistral', 'microsoft', 'cohere', 'perplexity', 'x', 'other'
  ]
  
  return vendors.sort((a, b) => {
    const priorityA = vendorPriority.indexOf(a)
    const priorityB = vendorPriority.indexOf(b)
    
    // 如果在优先级列表中，按优先级排序
    if (priorityA !== -1 && priorityB !== -1) {
      return priorityA - priorityB
    }
    
    // 如果只有一个在优先级列表中，优先显示在列表中的
    if (priorityA !== -1) return -1
    if (priorityB !== -1) return 1
    
    // 都不在优先级列表中，按字母顺序排序
    return a.localeCompare(b)
  })
}
