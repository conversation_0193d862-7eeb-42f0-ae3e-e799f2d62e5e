/**
 * 播放列表管理器
 * 优化数字人视频播放，支持无缝播放和预加载
 */

export interface PlaylistItem {
  id: string
  videoUrl: string
  text: string
}

export interface PlaylistState {
  items: PlaylistItem[]
  currentIndex: number
  isPlaying: boolean
  currentItem: PlaylistItem | null
  preloadedItems: Set<string> // 已预加载的视频ID
  seamlessMode: boolean // 是否启用无缝播放模式
}

export class PlaylistManager {
  private state: PlaylistState = {
    items: [],
    currentIndex: 0,
    isPlaying: false,
    currentItem: null,
    preloadedItems: new Set<string>(),
    seamlessMode: false
  }

  private listeners: Array<(state: PlaylistState) => void> = []
  private autoPlayNext = true

  /**
   * 添加状态监听器
   */
  addListener(listener: (state: PlaylistState) => void) {
    this.listeners.push(listener)
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  /**
   * 通知状态变化
   */
  private notifyListeners() {
    this.listeners.forEach(listener => listener({ ...this.state }))
  }

  /**
   * 设置播放列表
   */
  setPlaylist(items: PlaylistItem[]) {
    console.log(`设置播放列表，共 ${items.length} 个视频`)
    this.state.items = [...items]
    this.state.currentIndex = 0
    this.state.currentItem = items.length > 0 ? items[0] : null
    this.notifyListeners()
  }

  /**
   * 添加到播放列表
   */
  addToPlaylist(item: PlaylistItem) {
    console.log(`添加视频到播放列表: ${item.text}`)
    this.state.items.push(item)
    
    // 如果当前没有播放项目，设置为当前项目
    if (!this.state.currentItem) {
      this.state.currentItem = item
      this.state.currentIndex = this.state.items.length - 1
    }
    
    this.notifyListeners()
  }

  /**
   * 从播放列表移除
   */
  removeFromPlaylist(id: string) {
    const index = this.state.items.findIndex(item => item.id === id)
    if (index === -1) return

    console.log(`从播放列表移除视频: ${this.state.items[index].text}`)
    this.state.items.splice(index, 1)

    // 调整当前索引
    if (index < this.state.currentIndex) {
      this.state.currentIndex--
    } else if (index === this.state.currentIndex) {
      // 如果移除的是当前项目
      if (this.state.currentIndex >= this.state.items.length) {
        this.state.currentIndex = Math.max(0, this.state.items.length - 1)
      }
      this.state.currentItem = this.state.items[this.state.currentIndex] || null
    }

    this.notifyListeners()
  }

  /**
   * 播放下一个
   */
  playNext(): boolean {
    if (this.state.currentIndex < this.state.items.length - 1) {
      this.state.currentIndex++
      this.state.currentItem = this.state.items[this.state.currentIndex]
      console.log(`播放下一个视频: ${this.state.currentItem.text}`)
      this.notifyListeners()
      return true
    }
    return false
  }

  /**
   * 播放上一个
   */
  playPrevious(): boolean {
    if (this.state.currentIndex > 0) {
      this.state.currentIndex--
      this.state.currentItem = this.state.items[this.state.currentIndex]
      console.log(`播放上一个视频: ${this.state.currentItem.text}`)
      this.notifyListeners()
      return true
    }
    return false
  }

  /**
   * 跳转到指定索引
   */
  playAtIndex(index: number): boolean {
    if (index >= 0 && index < this.state.items.length) {
      this.state.currentIndex = index
      this.state.currentItem = this.state.items[index]
      console.log(`跳转播放视频: ${this.state.currentItem.text}`)
      this.notifyListeners()
      return true
    }
    return false
  }

  /**
   * 设置播放状态
   */
  setPlayingState(isPlaying: boolean) {
    if (this.state.isPlaying !== isPlaying) {
      this.state.isPlaying = isPlaying
      this.notifyListeners()
    }
  }

  /**
   * 当前视频播放完成
   */
  onCurrentVideoEnded() {
    console.log('当前视频播放完成')
    
    if (this.autoPlayNext && this.hasNext()) {
      // 自动播放下一个
      this.playNext()
      return true
    } else {
      // 没有下一个了，停止播放
      this.setPlayingState(false)
      return false
    }
  }

  /**
   * 是否有下一个
   */
  hasNext(): boolean {
    return this.state.currentIndex < this.state.items.length - 1
  }

  /**
   * 是否有上一个
   */
  hasPrevious(): boolean {
    return this.state.currentIndex > 0
  }

  /**
   * 获取当前状态
   */
  getState(): PlaylistState {
    return { ...this.state }
  }

  /**
   * 获取当前项目
   */
  getCurrentItem(): PlaylistItem | null {
    return this.state.currentItem
  }

  /**
   * 获取下一个项目（用于预加载）
   */
  getNextItem(): PlaylistItem | null {
    const nextIndex = this.state.currentIndex + 1
    return nextIndex < this.state.items.length ? this.state.items[nextIndex] : null
  }

  /**
   * 清空播放列表
   */
  clear() {
    console.log('清空播放列表')
    this.state.items = []
    this.state.currentIndex = 0
    this.state.currentItem = null
    this.state.isPlaying = false
    this.state.preloadedItems.clear()
    this.notifyListeners()
  }

  /**
   * 设置自动播放下一个
   */
  setAutoPlayNext(enabled: boolean) {
    this.autoPlayNext = enabled
  }

  /**
   * 启用/禁用无缝播放模式
   */
  setSeamlessMode(enabled: boolean) {
    console.log(`${enabled ? '启用' : '禁用'}无缝播放模式`)
    this.state.seamlessMode = enabled
    this.notifyListeners()
  }

  /**
   * 标记视频为已预加载
   */
  markAsPreloaded(videoId: string) {
    this.state.preloadedItems.add(videoId)
    console.log(`视频已预加载: ${videoId}`)
    this.notifyListeners()
  }

  /**
   * 检查视频是否已预加载
   */
  isPreloaded(videoId: string): boolean {
    return this.state.preloadedItems.has(videoId)
  }

  /**
   * 移除预加载标记
   */
  removePreloadedMark(videoId: string) {
    this.state.preloadedItems.delete(videoId)
    this.notifyListeners()
  }

  /**
   * 获取需要预加载的视频列表
   */
  getVideosToPreload(count: number = 2): PlaylistItem[] {
    const result: PlaylistItem[] = []
    const startIndex = this.state.currentIndex + 1

    for (let i = 0; i < count && startIndex + i < this.state.items.length; i++) {
      const item = this.state.items[startIndex + i]
      if (!this.state.preloadedItems.has(item.id)) {
        result.push(item)
      }
    }

    return result
  }

  /**
   * 获取无缝播放状态
   */
  isSeamlessModeEnabled(): boolean {
    return this.state.seamlessMode
  }

  /**
   * 获取播放进度信息
   */
  getProgress() {
    return {
      current: this.state.currentIndex + 1,
      total: this.state.items.length,
      percentage: this.state.items.length > 0 ? ((this.state.currentIndex + 1) / this.state.items.length) * 100 : 0
    }
  }
}

// 全局单例
export const playlistManager = new PlaylistManager()
