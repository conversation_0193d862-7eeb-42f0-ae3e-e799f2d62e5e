import React from 'react'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/useToast'
import { useAutoReply } from '@/hooks/useAutoReply'
import { useDigitalHumanSettings } from '@/hooks/useDigitalHumanSettings'

const DigitalHumanSettings = () => {
  const { toast } = useToast()
  const { config, updateLiveMode, updateAssistantEnabled, updateAssistantMode } = useAutoReply()
  const { liveMode, assistantEnabled, assistantMode } = config
  const { selectedDigitalHumans } = useDigitalHumanSettings()

  // 处理直播模式变更
  const handleLiveModeChange = (value: 'voice' | 'digital-human') => {
    if (value === 'digital-human') {
      // 检查是否有选中的数字人形象
      if (selectedDigitalHumans.length === 0) {
        toast.error('请先在应用设置的数字人设置中选择至少一个数字人形象')
        return
      }
      // 数字人模式自动开启助理
      if (!assistantEnabled) {
        updateAssistantEnabled(true)
        toast.success('已切换至数字人直播模式，助理功能已自动开启')
      } else {
        toast.success('已切换至数字人直播模式')
      }
    } else {
      toast.success('已切换至语音直播模式')
    }
    updateLiveMode(value)
  }

  // 处理助理开关变更
  const handleAssistantEnabledChange = (checked: boolean) => {
    // 如果是数字人模式，不允许关闭助理
    if (liveMode === 'digital-human' && !checked) {
      toast.error('数字人模式下不能关闭助理功能')
      return
    }
    updateAssistantEnabled(checked)
    toast.success(`助理功能已${checked ? '开启' : '关闭'}`)
  }

  // 处理助理模式变更
  const handleAssistantModeChange = (checked: boolean) => {
    const newMode = checked ? 'digital-human' : 'voice'
    console.log('🔄 助理模式变更:', {
      checked,
      newMode,
      currentMode: assistantMode,
      timestamp: new Date().toLocaleTimeString()
    })

    if (newMode === 'digital-human') {
      // 检查是否有选中的数字人形象
      if (selectedDigitalHumans.length === 0) {
        toast.error('请先在应用设置的数字人设置中选择至少一个数字人形象')
        return
      }
      toast.success('已切换至数字人助理模式')
    } else {
      toast.success('已切换至语音助理模式')
    }

    console.log('🔄 调用 updateAssistantMode:', newMode)
    updateAssistantMode(newMode)

    // 延迟检查配置是否更新
    setTimeout(() => {
      console.log('🔄 配置更新后检查:', {
        assistantMode: config.assistantMode,
        timestamp: new Date().toLocaleTimeString()
      })
    }, 100)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-sm font-medium">直播模式</h3>
        <div className="flex items-center space-x-2">
          <Switch
            id="live-mode"
            checked={liveMode === 'digital-human'}
            onCheckedChange={(checked) => {
              handleLiveModeChange(checked ? 'digital-human' : 'voice')
            }}
          />
          <Label htmlFor="live-mode">
            {liveMode === 'digital-human' ? '数字人' : '语音'}
          </Label>
        </div>
        <p className="text-xs text-muted-foreground">
          {liveMode === 'voice'
            ? '当前为语音直播模式：生成的内容将以语音形式播放'
            : '当前为数字人直播模式：生成的内容将以数字人视频形式播放（需要先在应用设置的数字人设置中选择数字人形象）'}
        </p>
      </div>

      <Separator />

      <div className="space-y-2">
        <h3 className="text-sm font-medium">助理功能</h3>
        <div className="flex items-center space-x-2">
          <Switch
            id="assistant-enabled"
            checked={assistantEnabled}
            onCheckedChange={handleAssistantEnabledChange}
            disabled={liveMode === 'digital-human'} // 数字人模式下不可关闭
          />
          <Label htmlFor="assistant-enabled">
            {assistantEnabled ? '已开启' : '已关闭'}
          </Label>
          {liveMode === 'digital-human' && (
            <span className="text-xs text-muted-foreground">(数字人模式下自动开启)</span>
          )}
        </div>
        <p className="text-xs text-muted-foreground">
          助理功能控制AI自动回复、智能对话等功能。数字人模式下助理功能将自动开启且不可关闭。
        </p>
      </div>

      {assistantEnabled && (
        <>
          <Separator />

          <div className="space-y-2">
            <h3 className="text-sm font-medium">助理模式</h3>
            <div className="flex items-center space-x-2">
              <Switch
                id="assistant-mode"
                checked={assistantMode === 'digital-human'}
                onCheckedChange={handleAssistantModeChange}
              />
              <Label htmlFor="assistant-mode">
                {assistantMode === 'digital-human' ? '数字人' : '语音'}
              </Label>
            </div>
            <p className="text-xs text-muted-foreground">
              {assistantMode === 'voice'
                ? '当前为语音助理模式：AI回复和自动互动将以语音形式输出'
                : '当前为数字人助理模式：AI回复和自动互动将以数字人视频形式输出（需要先在应用设置的数字人设置中选择数字人形象）'}
            </p>
          </div>
        </>
      )}

      <Separator />

      <div className="space-y-2">
        <h3 className="text-sm font-medium">输出说明</h3>
        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>主播输出：</strong>话术列表和手动输入的内容</p>
          <p><strong>助理输出：</strong>AI回复、自动互动、定时报时等自动生成的内容</p>
          <div className="mt-2 p-2 bg-muted rounded-md">
            <p className="font-medium mb-1">当前配置：</p>
            <p>• 直播模式：{liveMode === 'voice' ? '语音直播' : '数字人直播'}</p>
            <p>• 助理功能：{assistantEnabled ? '已开启' : '已关闭'}</p>
            {assistantEnabled && (
              <p>• 助理模式：{assistantMode === 'voice' ? '语音助理' : '数字人助理'}</p>
            )}
          </div>
        </div>
      </div>

      <Separator />

      {/* 数字人形象管理 */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium">数字人形象管理</h3>
        <p className="text-xs text-muted-foreground">
          数字人形象管理已移动到应用设置中，请前往应用设置 → 数字人设置进行配置
        </p>
        <Button
          variant="outline"
          onClick={() => {
            // 打开应用设置页面的数字人设置tab
            window.location.hash = '#/settings?tab=digital-human'
          }}
        >
          前往数字人设置
        </Button>
      </div>
    </div>
  )
}

export default DigitalHumanSettings
