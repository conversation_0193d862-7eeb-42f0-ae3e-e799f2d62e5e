import { BrowserWindow, app, shell } from 'electron'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { accountManager } from '#/managers/AccountManager'
import { typedIpcMainHandle } from '#/utils'
import { createLogger } from '#/logger'

function setupIpcHandlers() {
  typedIpcMainHandle(IPC_CHANNELS.chrome.toggleDevTools, event => {
    const win = BrowserWindow.fromWebContents(event.sender)
    if (win) {
      if (win.webContents.isDevToolsOpened()) {
        win.webContents.closeDevTools()
      } else {
        win.webContents.openDevTools()
      }
    }
  })

  typedIpcMainHandle(IPC_CHANNELS.app.openLogFolder, () => {
    shell.openPath(app.getPath('logs'))
  })

  typedIpcMainHandle(IPC_CHANNELS.account.switch, (_, { account }) => {
    accountManager.switchAccount(account)
  })

  // 处理从渲染进程发送的日志
  typedIpcMainHandle(IPC_CHANNELS.sendLog, (_, { scope, level, message }) => {
    const logger = createLogger(scope)
    switch (level) {
      case 'info':
        logger.info(message)
        break
      case 'warn':
        logger.warn(message)
        break
      case 'error':
        logger.error(message)
        break
      case 'success':
        logger.success(message)
        break
      default:
        logger.info(message)
    }
  })
}

export function setupAppIpcHandlers() {
  setupIpcHandlers()
}
