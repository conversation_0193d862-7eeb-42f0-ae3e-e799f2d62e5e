import { getComfyUIServer } from './voiceUpload'
import { getRandomSelectedDigitalHuman } from '@/hooks/useDigitalHumanSettings'

// 获取ComfyUI视频服务器地址的函数
function getComfyUIVideoServer(): string {
  try {
    // 尝试从localStorage中获取用户配置的ComfyUI视频服务器地址
    const voiceSettings = localStorage.getItem('voice-settings-storage')
    if (voiceSettings) {
      const parsed = JSON.parse(voiceSettings)
      const comfyUIVideoServer = parsed.state?.comfyUIVideoServer
      if (comfyUIVideoServer) {
        return comfyUIVideoServer
      }
    }
  } catch (error) {
    console.warn('获取ComfyUI视频服务器地址失败，使用默认地址:', error)
  }

  // 默认地址
  return 'http://127.0.0.1:8188'
}

// 获取ComfyUI基础路径的函数
function getComfyUIBasePath(): string {
  try {
    // 尝试从localStorage中获取用户配置的ComfyUI路径
    const voiceSettings = localStorage.getItem('voice-settings-storage')
    if (voiceSettings) {
      const parsed = JSON.parse(voiceSettings)
      const comfyUIPath = parsed.state?.comfyUIBasePath
      if (comfyUIPath) {
        return comfyUIPath
      }
    }
  } catch (error) {
    console.warn('获取ComfyUI基础路径失败，使用默认路径:', error)
  }

  // 默认路径
  return 'D:\\SD2\\ComfyUI'
}

// 获取HeyGem服务器地址的函数
function getHeygemServerUrl(): string {
  try {
    // 尝试从localStorage中获取用户配置的HeyGem服务器地址
    const voiceSettings = localStorage.getItem('voice-settings-storage')
    if (voiceSettings) {
      const parsed = JSON.parse(voiceSettings)
      const heygemUrl = parsed.state?.heygemServerUrl
      if (heygemUrl) {
        return heygemUrl
      }
    }
  } catch (error) {
    console.warn('获取HeyGem服务器地址失败，使用默认地址:', error)
  }

  // 默认地址
  return 'http://localhost:8383'
}

// 从HTTP URL提取音频文件名并转换为本地路径
function convertAudioUrlToLocalPath(audioUrl: string, serverUrl: string): string {
  try {
    // 从URL中提取文件名
    // 例如: http://127.0.0.1:8186/view?filename=ComfyUI_01297_.flac&type=output&subfolder=audio
    const url = new URL(audioUrl)
    const params = new URLSearchParams(url.search)
    const filename = params.get('filename')

    if (!filename) {
      throw new Error('无法从音频URL中提取文件名')
    }

    // 获取ComfyUI基础路径
    const comfyUIBasePath = getComfyUIBasePath()

    // 构建完整的本地文件路径
    // 使用双反斜杠确保路径格式正确
    const localPath = `${comfyUIBasePath}\\output\\audio\\${filename}`

    console.log(`音频URL转换: ${audioUrl} -> ${localPath}`)
    return localPath

  } catch (error) {
    console.error('转换音频URL到本地路径失败:', error)
    // 如果转换失败，尝试使用默认路径
    const url = new URL(audioUrl)
    const params = new URLSearchParams(url.search)
    const filename = params.get('filename')
    if (filename) {
      const fallbackPath = `D:\\SD2\\ComfyUI\\output\\audio\\${filename}`
      console.log(`使用fallback路径: ${fallbackPath}`)
      return fallbackPath
    }
    // 最后的fallback，返回原始URL
    return audioUrl
  }
}

// 数字人视频生成结果接口
export interface VideoGenerationResult {
  videoUrl: string
  duration?: number
  digitalHumanFile?: string
}

// 默认的数字人视频生成工作流
const DEFAULT_DIGITAL_HUMAN_WORKFLOW = {
  "11": {
    "inputs": {
      "StatusInfo": "D:\\SD2\\heygemService\\result\\cba22b15-2314-4a2d-90a6-4393df407f93-r.mp4",
      "obj": [
        "25",
        0
      ]
    },
    "class_type": "LamCommonPrint",
    "_meta": {
      "title": "通用打印输出"
    }
  },
  "12": {
    "inputs": {
      "video": "9.mp4",
      "video-preview": ""
    },
    "class_type": "VideoPath",
    "_meta": {
      "title": "获取视频地址"
    }
  },
  "24": {
    "inputs": {
      "StatusInfo": "http://localhost:8383/easy/file/cba22b15-2314-4a2d-90a6-4393df407f93-r.mp4",
      "obj": [
        "25",
        1
      ]
    },
    "class_type": "LamCommonPrint",
    "_meta": {
      "title": "通用打印输出"
    }
  },
  "25": {
    "inputs": {
      "server": "http://localhost:8383",
      "audio_path": [
        "45",
        0
      ],
      "chaofen": false,
      "video_path": [
        "12",
        0
      ]
    },
    "class_type": "LamHeyGemNode",
    "_meta": {
      "title": "HeyGem对口型"
    }
  },
  "45": {
    "inputs": {
      "text": "D:\\SD2\\ComfyUI\\output\\audio\\indextts__00001_.flac",
      "speak_and_recognation": {
        "__value__": [
          false,
          true
        ]
      }
    },
    "class_type": "Text Multiline",
    "_meta": {
      "title": "多行文本"
    }
  }
}

/**
 * 使用 ComfyUI 服务器将文本和音频转换为数字人视频
 * @param text 要转换的文本
 * @param audioPath 音频文件路径
 * @param digitalHumanFile 可选的数字人形象文件名，如果不提供则使用当前选中的形象
 * @param speaker 说话者类型，用于选择对应的数字人形象列表
 * @returns {Promise<VideoGenerationResult>} 包含视频URL、可选时长和使用的数字人形象文件名的Promise
 */
export async function generateDigitalHumanVideo(
  text: string,
  audioPath: string,
  digitalHumanFile?: string,
  speaker?: 'host' | 'assistant'
): Promise<VideoGenerationResult> {
  if (!text.trim()) {
    console.warn('输入文本为空，无法进行数字人视频生成。')
    return { videoUrl: '' }
  }

  if (!audioPath.trim()) {
    console.warn('音频路径为空，无法进行数字人视频生成。')
    return { videoUrl: '' }
  }

  const serverUrl = getComfyUIVideoServer()
  let usedDigitalHumanFile = ''

  try {
    console.log('开始生成数字人视频...')
    console.log('文本:', text)
    console.log('音频路径:', audioPath)
    console.log('ComfyUI 服务器地址:', serverUrl)

    // 深拷贝工作流模板
    const workflow = JSON.parse(JSON.stringify(DEFAULT_DIGITAL_HUMAN_WORKFLOW))

    // 设置音频路径 (ID: 45, 字段: text)
    // 将HTTP URL转换为本地文件路径
    const localAudioPath = convertAudioUrlToLocalPath(audioPath, serverUrl)
    if (workflow['45'] && workflow['45'].inputs && workflow['45'].inputs.text !== undefined) {
      workflow['45'].inputs.text = localAudioPath
      console.log(`使用音频文件: ${localAudioPath}`)
    } else {
      throw new Error("工作流中缺少用于设置音频路径的节点或字段（ID: 45, text）。")
    }

    // 设置数字人形象文件 (ID: 12, 字段: video)
    const selectedDigitalHuman = digitalHumanFile || getRandomSelectedDigitalHuman(speaker)
    usedDigitalHumanFile = selectedDigitalHuman
    if (workflow['12'] && workflow['12'].inputs && workflow['12'].inputs.video !== undefined) {
      workflow['12'].inputs.video = selectedDigitalHuman
      console.log(`使用数字人形象文件: ${selectedDigitalHuman}`)
    } else {
      throw new Error("工作流中缺少用于设置数字人形象的节点或字段（ID: 12, video）。")
    }

    // 设置HeyGem服务器地址 (ID: 25, 字段: server)
    // 从配置中获取HeyGem服务器地址
    const heygemServerUrl = getHeygemServerUrl()
    if (workflow['25'] && workflow['25'].inputs && workflow['25'].inputs.server !== undefined) {
      workflow['25'].inputs.server = heygemServerUrl
      console.log(`使用HeyGem服务器地址: ${heygemServerUrl}`)
    }

    console.log("正在发送数字人视频生成任务到 ComfyUI...")
    const promptData = {
      prompt: workflow,
      client_id: `comfyui_digital_human_client_${Date.now()}`,
    }

    const promptResponse = await fetch(`${serverUrl}/prompt`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(promptData)
    })

    if (!promptResponse.ok) {
      throw new Error(`ComfyUI 服务器响应错误: ${promptResponse.status} ${promptResponse.statusText}`)
    }

    const promptResult = await promptResponse.json()
    const promptId = promptResult.prompt_id

    if (!promptId) {
      throw new Error("ComfyUI 服务器未返回有效的 prompt_id。")
    }

    console.log(`数字人视频生成任务已提交，prompt_id: ${promptId}`)

    // 轮询任务状态
    let attempts = 0
    const maxAttempts = 60*30 // 最多等待30分钟（每次1秒）
    
    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒
      attempts++

      try {
        const historyResponse = await fetch(`${serverUrl}/history/${promptId}`)
        if (!historyResponse.ok) {
          console.warn(`获取任务历史失败: ${historyResponse.status}`)
          continue
        }

        const historyData = await historyResponse.json()
        const taskData = historyData[promptId]

        if (taskData && taskData.status) {
          if (taskData.status.completed) {
            console.log('数字人视频生成任务完成')
            console.log('任务输出数据:', JSON.stringify(taskData.outputs, null, 2))

            // 从输出中获取视频URL
            const outputs = taskData.outputs
            if (outputs) {
              // 查找视频输出节点（节点24包含HTTP URL，节点11包含本地路径）
              let videoUrl = null
              let localPath = null

              // 检查节点24的输出（HTTP URL）
              if (outputs['24'] && outputs['24'].text && outputs['24'].text.length > 0) {
                const url = outputs['24'].text[0]
                if (url && url.startsWith('http')) {
                  videoUrl = url
                }
              }

              // 检查节点11的输出（本地路径）
              if (outputs['11'] && outputs['11'].text && outputs['11'].text.length > 0) {
                localPath = outputs['11'].text[0]
              }

              // 优先使用HTTP URL，如果没有则使用本地路径
              const finalVideoUrl = videoUrl || localPath

              if (finalVideoUrl) {
                console.log('数字人视频生成成功:', finalVideoUrl)
                if (localPath) {
                  console.log('本地路径:', localPath)
                }
                return {
                  videoUrl: finalVideoUrl,
                  digitalHumanFile: usedDigitalHumanFile
                }
              }

              // 如果标准节点没有找到，尝试遍历所有输出节点寻找视频URL
              console.log('标准节点未找到视频URL，尝试遍历所有输出节点...')
              for (const nodeId in outputs) {
                const nodeOutput = outputs[nodeId]
                if (nodeOutput && nodeOutput.text && Array.isArray(nodeOutput.text)) {
                  for (const textItem of nodeOutput.text) {
                    if (typeof textItem === 'string' && (textItem.startsWith('http') || textItem.includes('.mp4'))) {
                      console.log(`在节点${nodeId}中找到视频URL:`, textItem)
                      return {
                        videoUrl: textItem,
                        digitalHumanFile: usedDigitalHumanFile
                      }
                    }
                  }
                }
                // 兼容旧格式的StatusInfo
                if (nodeOutput && nodeOutput.StatusInfo && Array.isArray(nodeOutput.StatusInfo)) {
                  for (const statusItem of nodeOutput.StatusInfo) {
                    if (typeof statusItem === 'string' && (statusItem.startsWith('http') || statusItem.includes('.mp4'))) {
                      console.log(`在节点${nodeId}的StatusInfo中找到视频URL:`, statusItem)
                      return {
                        videoUrl: statusItem,
                        digitalHumanFile: usedDigitalHumanFile
                      }
                    }
                  }
                }
              }
            }

            throw new Error('数字人视频生成完成但未找到有效的视频URL')
          } else if (taskData.status.status_str === 'error') {
            // 任务失败，立即抛出错误结束轮询
            const errorMessage = taskData.status.messages ?
              taskData.status.messages.map((msg: any) => msg[1]?.exception_message || msg[0]).join('; ') :
              '未知错误'
            throw new Error(`数字人视频生成失败: ${errorMessage}`)
          }
        }
      } catch (error) {
        // 如果是任务失败的错误，立即抛出不再重试
        if (error instanceof Error && error.message.includes('数字人视频生成失败:')) {
          throw error
        }
        console.warn(`检查任务状态时出错 (尝试 ${attempts}/${maxAttempts}):`, error)
      }
    }

    throw new Error('数字人视频生成超时，请检查 ComfyUI 服务器状态')

  } catch (error: any) {
    console.error("处理 ComfyUI 数字人视频生成任务时发生错误:", error)

    // 提供更友好的错误信息
    let friendlyMessage = '数字人视频生成失败'
    if (error.message) {
      if (error.message.includes('NoneType')) {
        friendlyMessage = '数字人视频生成失败：音频文件路径无效或文件不存在'
      } else if (error.message.includes('超时')) {
        friendlyMessage = '数字人视频生成超时，请检查 ComfyUI 服务器状态'
      } else if (error.message.includes('网络')) {
        friendlyMessage = '数字人视频生成失败：网络连接错误'
      } else {
        friendlyMessage = `数字人视频生成失败：${error.message}`
      }
    }

    throw new Error(friendlyMessage)
  }
}

/**
 * 使用随机选择的数字人形象生成视频的便捷函数
 * @param text 要转换的文本
 * @param audioPath 音频文件路径
 * @param speaker 说话者类型，用于选择对应的数字人形象列表
 * @returns {Promise<VideoGenerationResult>} 包含视频URL、可选时长和使用的数字人形象文件名的Promise
 */
export async function generateDigitalHumanVideoWithRandomAvatar(
  text: string,
  audioPath: string,
  speaker?: 'host' | 'assistant'
): Promise<VideoGenerationResult> {
  return generateDigitalHumanVideo(text, audioPath, getRandomSelectedDigitalHuman(speaker), speaker)
}
