import { ipcMain } from 'electron'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import log from 'electron-log'
import windowManager from '../windowManager'

export function setupAutoVoiceIpcHandlers() {
  log.info('设置自动语音 IPC 处理程序')

  ipcMain.handle(IPC_CHANNELS.tasks.autoVoice.start, async () => {
    log.info('自动语音任务启动')
    // 在此处添加自动语音任务的启动逻辑
    return true
  })

  ipcMain.handle(IPC_CHANNELS.tasks.autoVoice.stop, async () => {
    log.info('自动语音任务停止')
    // 在此处添加自动语音任务的停止逻辑
    // 发送停止事件给渲染进程
    // win.webContents.send(IPC_CHANNELS.tasks.autoVoice.stoppedEvent);
    return true
  })

  // 处理自动报时添加语音事件
  ipcMain.handle(IPC_CHANNELS.autoVoice.addTimeAnnouncement, async (_, data) => {
    log.info('收到自动报时语音请求:', data)
    // 转发给渲染进程
    windowManager.send(IPC_CHANNELS.autoVoice.addTimeAnnouncement, data)
    return true
  })

  // 处理被动互动添加语音事件
  ipcMain.handle(IPC_CHANNELS.autoVoice.addPassiveInteraction, async (_, data) => {
    log.info('收到被动互动语音请求:', data)
    // 转发给渲染进程
    windowManager.send(IPC_CHANNELS.autoVoice.addPassiveInteraction, data)
    return true
  })

  // 处理评论回复添加语音事件
  ipcMain.handle(IPC_CHANNELS.autoVoice.addCommentReply, async (_, data) => {
    log.info('收到评论回复语音请求:', data)
    // 转发给渲染进程
    windowManager.send(IPC_CHANNELS.autoVoice.addCommentReply, data)
    return true
  })
}