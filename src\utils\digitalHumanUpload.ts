// 获取ComfyUI视频服务器地址的函数
function getComfyUIVideoServer(): string {
  try {
    // 尝试从localStorage中获取用户配置的ComfyUI视频服务器地址
    const voiceSettings = localStorage.getItem('voice-settings-storage')
    if (voiceSettings) {
      const parsed = JSON.parse(voiceSettings)
      const comfyUIVideoServer = parsed.state?.comfyUIVideoServer
      if (comfyUIVideoServer) {
        return comfyUIVideoServer
      }
    }
  } catch (error) {
    console.warn('获取ComfyUI视频服务器地址失败，使用默认地址:', error)
  }

  // 默认地址
  return 'http://127.0.0.1:8188'
}

/**
 * 测试ComfyUI服务器连接
 * @returns Promise<boolean>
 */
export async function testComfyUIConnection(): Promise<boolean> {
  try {
    const serverUrl = getComfyUIVideoServer()

    // 尝试访问服务器的基本端点
    const response = await fetch(`${serverUrl}/`, {
      method: 'GET',
      signal: AbortSignal.timeout(5000) // 5秒超时
    })

    return response.ok
  } catch (error) {
    console.warn('ComfyUI服务器连接测试失败:', error)
    return false
  }
}

// 数字人形象上传响应接口
export interface DigitalHumanUploadResponse {
  name: string;
  subfolder: string;
  type: string;
}

/**
 * 上传数字人形象文件到ComfyUI服务器
 * @param file 要上传的视频文件
 * @returns Promise<DigitalHumanUploadResponse>
 */
export async function uploadDigitalHumanToComfyUI(file: File): Promise<DigitalHumanUploadResponse> {
  // 验证文件类型
  if (!file.type.startsWith('video/')) {
    throw new Error('只支持视频文件格式')
  }

  try {
    // 获取服务器地址
    const serverUrl = getComfyUIVideoServer()

    // 创建 FormData
    const formData = new FormData()
    formData.append('image', file, file.name)

    console.log('正在上传数字人形象到 ComfyUI...', file.name)

    const response = await fetch(`${serverUrl}/api/upload/image`, {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error(`上传失败: ${response.status} ${response.statusText}`)
    }

    const result: DigitalHumanUploadResponse = await response.json()
    console.log('数字人形象上传成功:', result)

    return result
  } catch (error) {
    console.error('数字人形象上传失败:', error)
    throw error
  }
}

/**
 * 验证数字人形象文件是否存在于ComfyUI服务器
 * @param filename 文件名
 * @returns Promise<boolean>
 */
export async function verifyDigitalHumanExists(filename: string): Promise<boolean> {
  try {
    const serverUrl = getComfyUIVideoServer()
    
    // 尝试多个可能的路径
    const possiblePaths = [
      `${serverUrl}/view?filename=${encodeURIComponent(filename)}&type=input&subfolder=`,
      `${serverUrl}/view?filename=${encodeURIComponent(filename)}&type=output&subfolder=`,
      `${serverUrl}/view?filename=${encodeURIComponent(filename)}&type=input&subfolder=digital_human`,
      `${serverUrl}/view?filename=${encodeURIComponent(filename)}&type=input&subfolder=video`,
    ]

    for (const url of possiblePaths) {
      try {
        const response = await fetch(url, { method: 'HEAD' })
        if (response.ok) {
          console.log(`数字人形象文件存在: ${url}`)
          return true
        }
      } catch (error) {
        // 继续尝试下一个路径
        continue
      }
    }

    console.warn(`数字人形象文件不存在: ${filename}`)
    return false
  } catch (error) {
    console.error('验证数字人形象文件失败:', error)
    return false
  }
}

/**
 * 获取数字人形象的资源URL
 * @param filename 文件名
 * @returns 资源URL
 */
export function getDigitalHumanResourceUrl(filename: string): string {
  const serverUrl = getComfyUIVideoServer()

  // 返回ComfyUI服务器的资源访问URL
  return `${serverUrl}/view?filename=${encodeURIComponent(filename)}&type=input&subfolder=`
}

/**
 * 获取数字人形象的多个可能资源URL
 * @param filename 文件名
 * @returns 可能的资源URL数组
 */
export function getDigitalHumanPossibleUrls(filename: string): string[] {
  const serverUrl = getComfyUIVideoServer()

  return [
    // 优先使用ComfyUI服务器的input目录
    `${serverUrl}/view?filename=${encodeURIComponent(filename)}&type=input&subfolder=`,
    `${serverUrl}/view?filename=${encodeURIComponent(filename)}&type=output&subfolder=`,
    `${serverUrl}/view?filename=${encodeURIComponent(filename)}&type=input&subfolder=digital_human`,
    `${serverUrl}/view?filename=${encodeURIComponent(filename)}&type=input&subfolder=video`,

    // 如果是HeyGem服务器的路径（通常是8383端口）
    `http://localhost:8383/easy/file/${filename}`,
    `http://localhost:8383/view?filename=${encodeURIComponent(filename)}&type=input&subfolder=`,

    // 尝试其他常见端口
    `http://localhost:8188/view?filename=${encodeURIComponent(filename)}&type=input&subfolder=`,
  ]
}
