import React, { type SVGProps } from 'react'

export function CarbonContentDeliveryNetwork(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
      {...props}
    >
      <title>Content Delivery Network</title>
      <circle cx="21" cy="21" r="2" fill="currentColor" />
      <circle cx="7" cy="7" r="2" fill="currentColor" />
      <path
        fill="currentColor"
        d="M27 31a4 4 0 1 1 4-4a4.01 4.01 0 0 1-4 4m0-6a2 2 0 1 0 2 2a2.006 2.006 0 0 0-2-2"
      />
      <path
        fill="currentColor"
        d="M30 16A14.04 14.04 0 0 0 16 2a13.04 13.04 0 0 0-6.8 1.8l1.1 1.7a24 24 0 0 1 2.4-1A25.1 25.1 0 0 0 10 15H4a11.15 11.15 0 0 1 1.4-4.7L3.9 9A13.84 13.84 0 0 0 2 16a14 14 0 0 0 14 14a13.4 13.4 0 0 0 5.2-1l-.6-1.9a11.44 11.44 0 0 1-5.2.9A21.07 21.07 0 0 1 12 17h17.9a3.4 3.4 0 0 0 .1-1M12.8 27.6a13 13 0 0 1-5.3-3.1A12.5 12.5 0 0 1 4 17h6a25 25 0 0 0 2.8 10.6M12 15a21.45 21.45 0 0 1 3.3-11h1.4A21.45 21.45 0 0 1 20 15Zm10 0a23.3 23.3 0 0 0-2.8-10.6A12.09 12.09 0 0 1 27.9 15Z"
      />
    </svg>
  )
}

export function CarbonChat(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
      {...props}
    >
      <title>Chat</title>
      <path
        fill="currentColor"
        d="M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z"
      />
      <path fill="currentColor" d="M8 10h16v2H8zm0 6h10v2H8z" />
    </svg>
  )
}

export function CarbonBlockStorage(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
      {...props}
    >
      <title>Block Storage</title>
      <path
        fill="currentColor"
        d="M9 12v-2H4a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-9h-2v9H4V12"
      />
      <path
        fill="currentColor"
        d="M22 12h-8V4h8zM12 4v8a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-8a2 2 0 0 0-2 2m2 14v4h-4v-4zm-6 0v4a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2m20-1v2h-2v-2zm-4-1v4a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1"
      />
    </svg>
  )
}

export function CarbonIbmEventAutomation(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
      {...props}
    >
      <title>IBM Event Automation</title>
      <circle cx="4" cy="22" r="2" fill="currentColor" />
      <path
        fill="currentColor"
        d="M13.5 30a9.53 9.53 0 0 1-7.73-3.977l1.626-1.164A7.52 7.52 0 0 0 13.5 28c3.969 0 7.258-3.1 7.487-7.058l1.997.116C22.694 26.072 18.527 30 13.5 30"
      />
      <circle cx="28" cy="23" r="2" fill="currentColor" />
      <path
        fill="currentColor"
        d="m30.641 19.095l-1.924-.545A7.5 7.5 0 0 0 29 16.5c0-4.135-3.364-7.5-7.5-7.5a7.4 7.4 0 0 0-3.088.663l-.824-1.822A9.4 9.4 0 0 1 21.5 7c5.238 0 9.5 4.262 9.5 9.5c0 .88-.12 1.754-.359 2.595m-10.227-4.51l-3-3C17.037 11.209 16.534 11 16 11s-1.037.208-1.414.586l-3 3c-.39.39-.586.902-.586 1.414s.196 1.024.586 1.414l3 3c.377.378.88.586 1.414.586s1.037-.208 1.414-.586l3-3c.39-.39.586-.902.586-1.414s-.196-1.024-.586-1.414M16 19l-3-3l3-3l3 3z"
      />
      <circle cx="16" cy="3" r="2" fill="currentColor" />
      <path
        fill="currentColor"
        d="M8.025 19.882A9.49 9.49 0 0 1 3 11.5c0-5.027 3.928-9.193 8.942-9.484l.116 1.997a7.502 7.502 0 0 0-3.089 14.105z"
      />
    </svg>
  )
}

export function CarbonIbmWatsonTextToSpeech(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
      {...props}
    >
      <title>IBM Watson Text to Speech</title>
      <path
        fill="currentColor"
        d="M13 26h-2c-3.9 0-7-3.1-7-7v-2h2v2c0 2.8 2.2 5 5 5h2zm5-8h10c1.1 0 2 .9 2 2v6c0 1.1-.9 2-2 2h-2.4l-1.7 3l-1.7-1l2.3-4H28v-6H18v6h3v2h-3c-1.1 0-2-.9-2-2v-6c0-1.1.9-2 2-2m10-4h-2v-2c0-2.8-2.2-5-5-5h-4V5h4c3.9 0 7 3.1 7 7zM2 11h6v2H2zm0-4h12v2H2zm0-4h12v2H2z"
      />
    </svg>
  )
}

export function CarbonSettings(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
      {...props}
    >
      <title>Settings</title>
      <path
        fill="currentColor"
        d="M27 16.76v-1.53l1.92-1.68A2 2 0 0 0 29.3 11l-2.36-4a2 2 0 0 0-1.73-1a2 2 0 0 0-.64.1l-2.43.82a11 11 0 0 0-1.31-.75l-.51-2.52a2 2 0 0 0-2-1.61h-4.68a2 2 0 0 0-2 1.61l-.51 2.52a11.5 11.5 0 0 0-1.32.75l-2.38-.86A2 2 0 0 0 6.79 6a2 2 0 0 0-1.73 1L2.7 11a2 2 0 0 0 .41 2.51L5 15.24v1.53l-1.89 1.68A2 2 0 0 0 2.7 21l2.36 4a2 2 0 0 0 1.73 1a2 2 0 0 0 .64-.1l2.43-.82a11 11 0 0 0 1.31.75l.51 2.52a2 2 0 0 0 2 1.61h4.72a2 2 0 0 0 2-1.61l.51-2.52a11.5 11.5 0 0 0 1.32-.75l2.42.82a2 2 0 0 0 .64.1a2 2 0 0 0 1.73-1l2.28-4a2 2 0 0 0-.41-2.51ZM25.21 24l-3.43-1.16a8.9 8.9 0 0 1-2.71 1.57L18.36 28h-4.72l-.71-3.55a9.4 9.4 0 0 1-2.7-1.57L6.79 24l-2.36-4l2.72-2.4a8.9 8.9 0 0 1 0-3.13L4.43 12l2.36-4l3.43 1.16a8.9 8.9 0 0 1 2.71-1.57L13.64 4h4.72l.71 3.55a9.4 9.4 0 0 1 2.7 1.57L25.21 8l2.36 4l-2.72 2.4a8.9 8.9 0 0 1 0 3.13L27.57 20Z"
      />
      <path
        fill="currentColor"
        d="M16 22a6 6 0 1 1 6-6a5.94 5.94 0 0 1-6 6m0-10a3.91 3.91 0 0 0-4 4a3.91 3.91 0 0 0 4 4a3.91 3.91 0 0 0 4-4a3.91 3.91 0 0 0-4-4"
      />
    </svg>
  )
}

export function CarbonDownload(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
      {...props}
    >
      <title>Download</title>
      <path
        fill="currentColor"
        d="M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10z"
      />
    </svg>
  )
}

export function CarbonRocket(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
      {...props}
    >
      <title>Rocket</title>
      <path
        fill="currentColor"
        d="m7.288 23.292l7.997-7.997l1.414 1.414l-7.997 7.997z"
      />
      <path
        fill="currentColor"
        d="M17 30a1 1 0 0 1-.37-.07a1 1 0 0 1-.62-.79l-1-7l2-.28l.75 5.27L21 24.52V17a1 1 0 0 1 .29-.71l4.07-4.07A8.94 8.94 0 0 0 28 5.86V4h-1.86a8.94 8.94 0 0 0-6.36 2.64l-4.07 4.07A1 1 0 0 1 15 11H7.48l-2.61 3.26l5.27.75l-.28 2l-7-1a1 1 0 0 1-.79-.62a1 1 0 0 1 .15-1l4-5A1 1 0 0 1 7 9h7.59l3.77-3.78A10.92 10.92 0 0 1 26.14 2H28a2 2 0 0 1 2 2v1.86a10.92 10.92 0 0 1-3.22 7.78L23 17.41V25a1 1 0 0 1-.38.78l-5 4A1 1 0 0 1 17 30"
      />
    </svg>
  )
}

export function CarbonRenew(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
      {...props}
    >
      <title>Renew</title>
      <path
        fill="currentColor"
        d="M12 10H6.78A11 11 0 0 1 27 16h2A13 13 0 0 0 6 7.68V4H4v8h8zm8 12h5.22A11 11 0 0 1 5 16H3a13 13 0 0 0 23 8.32V28h2v-8h-8z"
      />
    </svg>
  )
}


export function CarbonPlayFilledAlt(props: SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32" {...props}>
      <path fill="currentColor" d="M7 28a1 1 0 0 1-1-1V5a1 1 0 0 1 1.482-.876l20 11a1 1 0 0 1 0 1.752l-20 11A1 1 0 0 1 7 28"></path></svg>
  )
}


export function CarbonStopFilledAlt(props: SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32" {...props}>
      <path fill="currentColor" d="M24 6H8a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2"></path></svg>
  )
}

export function CarbonAI(props: SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32" {...props}>
      <title>AI</title>
      <path fill="currentColor" d="M26 14h-2.17A5.99 5.99 0 0 0 18 8.17V6a2 2 0 0 0-4 0v2.17A5.99 5.99 0 0 0 8.17 14H6a2 2 0 0 0 0 4h2.17A5.99 5.99 0 0 0 14 23.83V26a2 2 0 0 0 4 0v-2.17A5.99 5.99 0 0 0 23.83 18H26a2 2 0 0 0 0-4M16 20a4 4 0 1 1 4-4a4 4 0 0 1-4 4" />
      <circle cx="16" cy="16" r="2" fill="currentColor" />
    </svg>
  )
}

export function CarbonVideoPlayer(props: SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32" {...props}>
      <title>Video Player</title>
      <path fill="currentColor" d="M21 26H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h17a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2M4 8v16h17V8zm6.5 2L18 14l-7.5 4z" />
      <path fill="currentColor" d="M28 23V9a2 2 0 0 0-2-2v2h2v14h-2v2a2 2 0 0 0 2-2" />
    </svg>
  )
}