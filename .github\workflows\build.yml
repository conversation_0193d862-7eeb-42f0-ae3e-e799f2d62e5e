name: Build

on:
  push:
    branches: [main]
    paths-ignore:
      - "**.md"
      - "**.spec.js"
      - ".idea"
      - ".vscode"
      - ".dockerignore"
      - "Dockerfile"
      - ".gitignore"
      - ".github/**"
      - "!.github/workflows/build.yml"

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        # macos-13 采用 intel 架构，见 https://docs.github.com/en/actions/using-github-hosted-runners/using-github-hosted-runners/about-github-hosted-runners#standard-github-hosted-runners-for-public-repositories
        os: [macos-latest, windows-latest] # os: [macos-latest, windows-latest, macos-13] 

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Install Dependencies
        run: pnpm install

      - name: Build Release Files
        run: pnpm build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with: 
          name: release_on_${{ matrix. os }}
          path: |
            release/*/*.exe
            release/*/*.dmg
          retention-days: 7
