import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAutoVoice } from '@/hooks/useAutoVoice'
import { useToast } from '@/hooks/useToast'
import { useState } from 'react'

const ScriptGeneralizationSettings = () => {
  const { generalizationPrompt, setGeneralizationPrompt } = useAutoVoice()
  const [tempPrompt, setTempPrompt] = useState(generalizationPrompt)
  const { toast } = useToast()

  const handleSave = () => {
    setGeneralizationPrompt(tempPrompt)
    toast.success('泛化提示词已保存')
  }

  const handleReset = () => {
    const defaultPrompt = `你是一个专业的直播话术泛化助手。你的任务是将给定的直播话术进行多样化改写，保持原意的同时增加表达的丰富性。

请遵循以下原则：
1. 保持原话术的核心意思和目的不变
2. 使用不同的表达方式、语气和词汇
3. 适合直播场景，语言要亲切自然
4. 每个变体都应该是完整的、可直接使用的话术
5. 生成5-8个不同的变体

请对以下话术进行泛化改写：
{original_script}

请直接返回变体列表，每行一个变体，不需要编号或其他格式。`

    setTempPrompt(defaultPrompt)
    toast.success('已重置为默认提示词')
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>直播话术</CardTitle>
        <CardDescription>
          泛化提示词设置
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium mb-2">泛化提示词模板</h3>
            <p className="text-sm text-muted-foreground mb-4">
              使用 {'{original_script}'} 作为原始话术的占位符。AI将根据此提示词生成话术变体。
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="prompt-textarea">提示词内容</Label>
            <Textarea
              id="prompt-textarea"
              value={tempPrompt}
              onChange={(e) => setTempPrompt(e.target.value)}
              placeholder="请输入泛化提示词..."
              className="min-h-[400px] resize-none font-mono text-sm"
            />
          </div>

          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={handleReset}
            >
              重置为默认
            </Button>
            <Button onClick={handleSave}>
              保存设置
            </Button>
          </div>
        </div>

        <div className="border rounded-md p-4 bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">使用说明</h4>
          <ul className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
            <li>• 提示词中必须包含 {'{original_script}'} 占位符</li>
            <li>• AI会根据提示词生成多个话术变体</li>
            <li>• 建议在提示词中明确指定生成变体的数量和风格要求</li>
            <li>• 保存后的提示词会应用到所有话术的泛化生成中</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}

export default ScriptGeneralizationSettings
