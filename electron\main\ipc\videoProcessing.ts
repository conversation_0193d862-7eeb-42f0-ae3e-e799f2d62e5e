import { ipcMain, dialog, shell } from 'electron'
import { spawn } from 'child_process'
import { join, dirname } from 'path'
import { existsSync, mkdirSync, statSync, writeFileSync, unlinkSync, writeFile, createWriteStream, copyFile } from 'fs'
import { IPC_CHANNELS } from '../../../shared/ipcChannels'
import { createLogger } from '../logger'
import https from 'https'
import http from 'http'
import { URL } from 'url'

const logger = createLogger('视频处理')

/**
 * 视频切割结果接口
 */
interface VideoCutResult {
  success: boolean
  outputFilename?: string
  outputPath?: string
  error?: string
}

/**
 * 视频切割请求接口
 */
interface VideoCutRequest {
  originalFilename: string
  startTime: number
  endTime: number
  outputFilename: string
}

/**
 * 视频拼接结果接口
 */
interface VideoConcatenateResult {
  success: boolean
  outputPath?: string
  error?: string
}

/**
 * 视频拼接请求接口
 */
interface VideoConcatenateRequest {
  videoUrls: string[]
  outputFilename: string
}

// 全局变量存储ComfyUI路径
let comfyUIBasePath = 'D:\\SD2\\ComfyUI' // 默认路径

/**
 * 设置ComfyUI基础路径
 */
function setComfyUIBasePath(path: string): void {
  comfyUIBasePath = path
  logger.info(`ComfyUI基础路径已设置为: ${comfyUIBasePath}`)
}

/**
 * 获取ComfyUI基础路径
 */
function getComfyUIBasePath(): string {
  return comfyUIBasePath
}

/**
 * 获取ComfyUI输入目录路径
 */
function getComfyUIInputPath(): string {
  const inputPath = join(comfyUIBasePath, 'input')
  logger.info(`获取ComfyUI输入路径: ${inputPath}`)
  return inputPath
}

/**
 * 获取FFmpeg可执行文件路径
 */
function getFFmpegPath(): string {
  // 这里假设FFmpeg在系统PATH中，或者指定具体路径
  return 'ffmpeg'
}

/**
 * 使用FFmpeg切割视频
 */
async function cutVideoWithFFmpeg(
  inputPath: string,
  outputPath: string,
  startTime: number,
  endTime: number
): Promise<boolean> {
  return new Promise((resolve) => {
    const duration = endTime - startTime

    const args = [
      '-i', inputPath,
      '-ss', startTime.toString(),
      '-t', duration.toString(),
      '-c:v', 'libx264', // 重新编码以确保兼容性
      '-c:a', 'aac',     // 音频编码
      '-preset', 'fast', // 快速编码
      '-crf', '23',      // 质量设置
      '-avoid_negative_ts', 'make_zero',
      '-y', // 覆盖输出文件
      outputPath
    ]

    logger.info(`执行FFmpeg命令: ffmpeg ${args.join(' ')}`)

    const ffmpeg = spawn(getFFmpegPath(), args)

    let stderr = ''

    ffmpeg.stderr.on('data', (data) => {
      stderr += data.toString()
    })

    ffmpeg.on('close', (code) => {
      if (code === 0) {
        logger.info('视频切割成功')
        resolve(true)
      } else {
        logger.error(`FFmpeg切割失败，退出码: ${code}`)
        logger.error('FFmpeg错误输出:', stderr)
        resolve(false)
      }
    })

    ffmpeg.on('error', (error) => {
      logger.error('FFmpeg切割进程错误:', error)
      resolve(false)
    })
  })
}

/**
 * 下载HTTP视频文件到本地
 */
async function downloadVideoFile(url: string, outputPath: string): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      const parsedUrl = new URL(url)
      const client = parsedUrl.protocol === 'https:' ? https : http

      logger.info(`开始下载视频: ${url} -> ${outputPath}`)

      const request = client.get(url, (response) => {
        logger.info(`HTTP响应状态码: ${response.statusCode}`)

        if (response.statusCode !== 200) {
          logger.error(`下载失败，状态码: ${response.statusCode}`)
          resolve(false)
          return
        }

        const fileStream = createWriteStream(outputPath)
        let downloadedBytes = 0

        response.on('data', (chunk) => {
          downloadedBytes += chunk.length
        })

        response.pipe(fileStream)

        fileStream.on('finish', () => {
          fileStream.close((err) => {
            if (err) {
              logger.error('关闭文件流时出错:', err)
              resolve(false)
            } else {
              logger.info(`视频下载成功: ${outputPath}, 大小: ${downloadedBytes} bytes`)
              resolve(true)
            }
          })
        })

        fileStream.on('error', (error: Error) => {
          logger.error('文件写入错误:', error)
          fileStream.destroy()
          resolve(false)
        })

        response.on('error', (error: Error) => {
          logger.error('响应流错误:', error)
          fileStream.destroy()
          resolve(false)
        })
      })

      request.on('error', (error: Error) => {
        logger.error('下载请求错误:', error)
        resolve(false)
      })

      request.setTimeout(30000, () => {
        logger.error('下载超时')
        request.destroy()
        resolve(false)
      })

    } catch (error) {
      logger.error('下载视频文件失败:', error)
      resolve(false)
    }
  })
}

/**
 * 使用FFmpeg拼接多个视频
 */
async function concatenateVideosWithFFmpeg(
  videoPaths: string[],
  outputPath: string
): Promise<boolean> {
  return new Promise((resolve) => {
    if (videoPaths.length === 0) {
      logger.error('没有提供视频文件进行拼接')
      resolve(false)
      return
    }

    // 创建临时文件列表
    const tempListPath = join(dirname(outputPath), 'concat_list.txt')
    const fileListContent = videoPaths.map(path => `file '${path.replace(/'/g, "'\"'\"'")}'`).join('\n')

    try {
      writeFileSync(tempListPath, fileListContent, 'utf8')
      logger.info(`创建文件列表: ${tempListPath}`)
      logger.info(`文件列表内容:\n${fileListContent}`)
    } catch (error) {
      logger.error('创建文件列表失败:', error)
      resolve(false)
      return
    }

    const args = [
      '-f', 'concat',
      '-safe', '0',
      '-i', tempListPath,
      '-c', 'copy', // 直接复制流，不重新编码（更快）
      '-y', // 覆盖输出文件
      outputPath
    ]

    logger.info(`执行FFmpeg拼接命令: ffmpeg ${args.join(' ')}`)

    const ffmpeg = spawn(getFFmpegPath(), args)

    let stderr = ''

    ffmpeg.stderr.on('data', (data) => {
      stderr += data.toString()
    })

    ffmpeg.on('close', (code) => {
      // 清理临时文件
      try {
        if (existsSync(tempListPath)) {
          unlinkSync(tempListPath)
        }
      } catch (error) {
        logger.warn('清理临时文件失败:', error)
      }

      if (code === 0) {
        logger.info('视频拼接成功')
        resolve(true)
      } else {
        logger.error(`FFmpeg拼接失败，退出码: ${code}`)
        logger.error('FFmpeg错误输出:', stderr)
        resolve(false)
      }
    })

    ffmpeg.on('error', (error) => {
      logger.error('FFmpeg拼接进程错误:', error)
      // 清理临时文件
      try {
        if (existsSync(tempListPath)) {
          unlinkSync(tempListPath)
        }
      } catch (cleanupError) {
        logger.warn('清理临时文件失败:', cleanupError)
      }
      resolve(false)
    })
  })
}

/**
 * 注册视频处理相关的IPC处理器
 */
export function registerVideoProcessingHandlers() {
  logger.info('注册视频处理 IPC 处理程序')

  // 视频切割处理器
  ipcMain.handle(IPC_CHANNELS.video.cutSegment, async (_event, request: VideoCutRequest): Promise<VideoCutResult> => {
    try {
      logger.info(`收到视频切割请求: ${JSON.stringify(request)}`)

      const { originalFilename, startTime, endTime, outputFilename } = request

      // 验证参数
      if (!originalFilename || !outputFilename || startTime < 0 || endTime <= startTime) {
        return {
          success: false,
          error: '无效的切割参数'
        }
      }

      const inputDir = getComfyUIInputPath()
      const inputPath = join(inputDir, originalFilename)
      const outputPath = join(inputDir, outputFilename)

      logger.info(`当前ComfyUI基础路径: ${comfyUIBasePath}`)
      logger.info(`输入目录: ${inputDir}`)
      logger.info(`输入文件路径: ${inputPath}`)
      logger.info(`输出文件路径: ${outputPath}`)

      // 检查输入文件是否存在
      if (!existsSync(inputPath)) {
        logger.error(`输入视频文件不存在: ${inputPath}`)
        return {
          success: false,
          error: `输入视频文件不存在: ${originalFilename}`
        }
      }

      // 确保输出目录存在
      const outputDir = join(inputDir)
      if (!existsSync(outputDir)) {
        mkdirSync(outputDir, { recursive: true })
      }

      // 执行视频切割
      logger.info(`开始切割视频: ${inputPath} -> ${outputPath}, 时间: ${startTime}s - ${endTime}s`)
      const success = await cutVideoWithFFmpeg(inputPath, outputPath, startTime, endTime)
      logger.info(`FFmpeg切割结果: ${success}`)

      if (success) {
        // 检查输出文件是否真的存在
        if (existsSync(outputPath)) {
          const fileStats = statSync(outputPath)
          logger.info(`视频切割成功，文件大小: ${fileStats.size} bytes`)

          // 验证文件不为空
          if (fileStats.size > 1000) { // 至少1KB
            return {
              success: true,
              outputFilename,
              outputPath // 返回完整路径
            }
          } else {
            logger.error(`切割后文件太小，可能损坏: ${fileStats.size} bytes`)
            return {
              success: false,
              error: '切割后文件太小，可能损坏'
            }
          }
        } else {
          logger.error(`视频切割后文件不存在: ${outputPath}`)
          return {
            success: false,
            error: '切割后文件不存在'
          }
        }
      } else {
        return {
          success: false,
          error: 'FFmpeg切割失败'
        }
      }

    } catch (error) {
      logger.error('视频切割处理失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 检查FFmpeg是否可用
  ipcMain.handle(IPC_CHANNELS.video.checkFFmpeg, async (): Promise<boolean> => {
    return new Promise((resolve) => {
      const ffmpeg = spawn(getFFmpegPath(), ['-version'])
      
      ffmpeg.on('close', (code) => {
        resolve(code === 0)
      })
      
      ffmpeg.on('error', () => {
        resolve(false)
      })
    })
  })

  // 获取视频信息
  ipcMain.handle(IPC_CHANNELS.video.getInfo, async (_event, filename: string): Promise<any> => {
    return new Promise((resolve) => {
      const inputDir = getComfyUIInputPath()
      const inputPath = join(inputDir, filename)

      if (!existsSync(inputPath)) {
        resolve({ error: '文件不存在' })
        return
      }

      const args = [
        '-i', inputPath,
        '-f', 'null',
        '-'
      ]

      const ffmpeg = spawn(getFFmpegPath(), args)
      let stderr = ''

      ffmpeg.stderr.on('data', (data) => {
        stderr += data.toString()
      })

      ffmpeg.on('close', () => {
        // 解析FFmpeg输出获取视频信息
        const durationMatch = stderr.match(/Duration: (\d{2}):(\d{2}):(\d{2}\.\d{2})/)
        if (durationMatch) {
          const hours = parseInt(durationMatch[1])
          const minutes = parseInt(durationMatch[2])
          const seconds = parseFloat(durationMatch[3])
          const totalSeconds = hours * 3600 + minutes * 60 + seconds

          resolve({
            duration: totalSeconds,
            filename
          })
        } else {
          resolve({ error: '无法获取视频信息' })
        }
      })

      ffmpeg.on('error', (error) => {
        resolve({ error: error.message })
      })
    })
  })

  // 拼接多个视频
  ipcMain.handle(IPC_CHANNELS.video.concatenateVideos, async (_event, request: VideoConcatenateRequest): Promise<VideoConcatenateResult> => {
    try {
      logger.info(`开始拼接视频，共 ${request.videoUrls.length} 个文件`)

      const videoPaths: string[] = []
      const tempFiles: string[] = []

      // 处理每个视频URL（HTTP或本地路径）
      for (let i = 0; i < request.videoUrls.length; i++) {
        const videoUrl = request.videoUrls[i]

        if (videoUrl.startsWith('http')) {
          // HTTP URL - 需要下载到本地
          const tempFileName = `temp_concat_${Date.now()}_${i}.mp4`
          const tempPath = join(getComfyUIInputPath(), tempFileName)

          logger.info(`下载HTTP视频: ${videoUrl}`)
          const downloadSuccess = await downloadVideoFile(videoUrl, tempPath)

          if (!downloadSuccess) {
            // 清理已下载的临时文件
            for (const tempFile of tempFiles) {
              try {
                if (existsSync(tempFile)) {
                  unlinkSync(tempFile)
                }
              } catch (error) {
                logger.warn(`清理临时文件失败: ${tempFile}`, error)
              }
            }

            return {
              success: false,
              error: `下载视频失败: ${videoUrl}`
            }
          }

          videoPaths.push(tempPath)
          tempFiles.push(tempPath)
        } else {
          // 本地文件路径
          if (!existsSync(videoUrl)) {
            logger.error(`视频文件不存在: ${videoUrl}`)
            return {
              success: false,
              error: `视频文件不存在: ${videoUrl}`
            }
          }
          videoPaths.push(videoUrl)
        }
      }

      // 确保输出目录存在
      const outputPath = request.outputFilename
      const outputDir = dirname(outputPath)
      if (!existsSync(outputDir)) {
        mkdirSync(outputDir, { recursive: true })
      }

      // 执行视频拼接
      const success = await concatenateVideosWithFFmpeg(videoPaths, outputPath)

      // 清理临时文件
      for (const tempFile of tempFiles) {
        try {
          if (existsSync(tempFile)) {
            unlinkSync(tempFile)
            logger.info(`清理临时文件: ${tempFile}`)
          }
        } catch (error) {
          logger.warn(`清理临时文件失败: ${tempFile}`, error)
        }
      }

      if (success) {
        // 检查输出文件是否存在
        if (existsSync(outputPath)) {
          const fileStats = statSync(outputPath)
          logger.info(`视频拼接成功，文件大小: ${fileStats.size} bytes`)

          // 验证文件不为空
          if (fileStats.size > 1000) { // 至少1KB
            return {
              success: true,
              outputPath
            }
          } else {
            logger.error(`拼接后文件太小，可能损坏: ${fileStats.size} bytes`)
            return {
              success: false,
              error: '拼接后文件太小，可能损坏'
            }
          }
        } else {
          logger.error(`视频拼接后文件不存在: ${outputPath}`)
          return {
            success: false,
            error: '拼接后文件不存在'
          }
        }
      } else {
        return {
          success: false,
          error: 'FFmpeg拼接失败'
        }
      }

    } catch (error) {
      logger.error('视频拼接处理失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 写入文件
  ipcMain.handle(IPC_CHANNELS.video.writeFile, async (_event, fileName: string, data: Uint8Array): Promise<{ success: boolean; error?: string }> => {
    try {
      // 将文件写入到ComfyUI输入目录
      const inputDir = getComfyUIInputPath()
      const fullPath = join(inputDir, fileName)

      logger.info(`写入文件: ${fullPath}`)

      // 确保目录存在
      if (!existsSync(inputDir)) {
        mkdirSync(inputDir, { recursive: true })
      }

      // 写入文件
      await new Promise<void>((resolve, reject) => {
        writeFile(fullPath, data, (error) => {
          if (error) {
            reject(error)
          } else {
            resolve()
          }
        })
      })

      logger.info(`文件写入成功: ${fullPath}`)
      return { success: true }

    } catch (error) {
      logger.error('文件写入失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 显示保存对话框
  ipcMain.handle(IPC_CHANNELS.dialog.showSaveDialog, async (_event, options): Promise<any> => {
    try {
      const result = await dialog.showSaveDialog(options)
      return result
    } catch (error) {
      logger.error('显示保存对话框失败:', error)
      return { canceled: true, error: error instanceof Error ? error.message : '未知错误' }
    }
  })

  // 复制文件
  ipcMain.handle(IPC_CHANNELS.fs.copyFile, async (_event, sourcePath: string, destPath: string): Promise<{ success: boolean; error?: string }> => {
    try {
      logger.info(`复制文件: ${sourcePath} -> ${destPath}`)

      // 确保目标目录存在
      const destDir = dirname(destPath)
      if (!existsSync(destDir)) {
        mkdirSync(destDir, { recursive: true })
      }

      // 复制文件
      await new Promise<void>((resolve, reject) => {
        copyFile(sourcePath, destPath, (error) => {
          if (error) {
            reject(error)
          } else {
            resolve()
          }
        })
      })

      logger.info(`文件复制成功: ${destPath}`)
      return { success: true }

    } catch (error) {
      logger.error('文件复制失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 检查文件是否存在
  ipcMain.handle(IPC_CHANNELS.fs.exists, async (_event, filePath: string): Promise<boolean> => {
    try {
      logger.info(`检查文件是否存在: ${filePath}`)
      return existsSync(filePath)
    } catch (error) {
      logger.error('检查文件存在性失败:', error)
      return false
    }
  })

  // 设置ComfyUI路径
  ipcMain.handle(IPC_CHANNELS.video.setComfyUIPath, async (_event, path: string): Promise<void> => {
    try {
      logger.info(`收到设置ComfyUI路径请求: ${path}`)
      setComfyUIBasePath(path)
      logger.info(`输入目录路径: ${getComfyUIInputPath()}`)
    } catch (error) {
      logger.error('设置ComfyUI路径失败:', error)
    }
  })

  // 在文件管理器中显示文件
  ipcMain.handle(IPC_CHANNELS.shell.showItemInFolder, async (_event, filePath: string): Promise<void> => {
    try {
      logger.info(`在文件管理器中显示文件: ${filePath}`)
      shell.showItemInFolder(filePath)
    } catch (error) {
      logger.error('显示文件失败:', error)
    }
  })
}
