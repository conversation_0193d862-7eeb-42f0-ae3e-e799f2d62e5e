import React, { useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import MediaSourceAdapter, { MediaSourceSeamlessPlayerRef } from '@/components/MediaSourceAdapter'
import { VideoItem } from '@/components/MediaSourceSeamlessPlayer'

/**
 * 流拼接演示页面
 * 展示MediaSource真正无缝的视频流拼接效果
 */
export default function StreamSplicingDemo() {
  const playerRef = useRef<MediaSourceSeamlessPlayerRef>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [logs, setLogs] = useState<string[]>([])

  // 简化的测试视频（使用较短的视频便于测试）
  const testVideos: VideoItem[] = [
    {
      id: 'demo1',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
      text: '演示视频1 - For Bigger Blazes',
      duration: 15
    },
    {
      id: 'demo2',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
      text: '演示视频2 - For Bigger Escapes',
      duration: 15
    },
    {
      id: 'demo3',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
      text: '演示视频3 - For Bigger Fun',
      duration: 60
    }
  ]

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)])
  }

  const handlePlayAll = async () => {
    if (!playerRef.current) return
    
    try {
      addLog('开始MediaSource流拼接演示')
      await playerRef.current.setQueue(testVideos)
      setIsPlaying(true)
      addLog('流拼接开始 - 观察视频间的无缝切换！')
    } catch (error) {
      addLog(`播放失败: ${error}`)
      console.error('播放失败:', error)
    }
  }

  const handleAddVideo = async (video: VideoItem) => {
    if (!playerRef.current) return
    
    try {
      addLog(`动态添加视频: ${video.text}`)
      await playerRef.current.playVideo(video)
      if (!isPlaying) {
        setIsPlaying(true)
      }
    } catch (error) {
      addLog(`添加失败: ${error}`)
    }
  }

  const handleStop = () => {
    if (!playerRef.current) return
    
    playerRef.current.stop()
    setIsPlaying(false)
    addLog('播放已停止')
  }

  const handleVideoEnded = (videoId: string) => {
    addLog(`视频结束: ${videoId}`)
  }

  const handleSegmentAdded = (videoId: string, duration: number) => {
    addLog(`片段已添加: ${videoId} (${Math.round(duration)}s)`)
  }

  const handleError = (error: Error, videoId?: string) => {
    addLog(`错误: ${error.message} ${videoId || ''}`)
    setIsPlaying(false)
  }

  return (
    <div className="h-screen bg-gray-900 text-white flex">
      {/* 左侧控制面板 */}
      <div className="w-80 bg-gray-800 p-4 border-r border-gray-700 flex flex-col">
        <div className="mb-4">
          <h1 className="text-xl font-bold mb-2">MediaSource流拼接演示</h1>
          <p className="text-sm text-gray-400">
            真正无缝的视频流拼接技术 - 零卡顿、零间断
          </p>
        </div>

        {/* 主控制 */}
        <div className="mb-4 space-y-2">
          <Button
            onClick={handlePlayAll}
            disabled={isPlaying}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            播放完整序列（无缝拼接）
          </Button>
          
          <Button
            onClick={handleStop}
            disabled={!isPlaying}
            variant="destructive"
            className="w-full"
          >
            停止播放
          </Button>
        </div>

        {/* 动态添加 */}
        <div className="mb-4">
          <h3 className="text-sm font-semibold mb-2">动态添加到流</h3>
          <div className="space-y-1">
            {testVideos.map((video) => (
              <Button
                key={video.id}
                onClick={() => handleAddVideo(video)}
                variant="outline"
                size="sm"
                className="w-full text-left justify-start text-xs"
              >
                + {video.text}
              </Button>
            ))}
          </div>
        </div>

        {/* 技术说明 */}
        <div className="mb-4 p-3 bg-gray-700 rounded text-xs">
          <h4 className="font-semibold mb-2">技术特点：</h4>
          <ul className="space-y-1 text-gray-300">
            <li>• 视频流级别拼接</li>
            <li>• 零视觉间断</li>
            <li>• 动态添加片段</li>
            <li>• 智能缓冲管理</li>
            <li>• 格式自动适配</li>
          </ul>
        </div>

        {/* 操作日志 */}
        <div className="flex-1">
          <h3 className="text-sm font-semibold mb-2">操作日志</h3>
          <div className="bg-gray-700 rounded p-2 h-full overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index} className="text-xs text-gray-300 mb-1">
                {log}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 右侧播放器 */}
      <div className="flex-1 relative bg-black">
        <MediaSourceAdapter
          ref={playerRef}
          className="w-full h-full"
          autoPlay={true}
          muted={false}
          volume={0.7}
          onVideoEnded={handleVideoEnded}
          onError={handleError}
          onSegmentAdded={handleSegmentAdded}
        />

        {/* 状态指示器 */}
        {isPlaying && (
          <div className="absolute top-4 right-4 bg-green-600 text-white text-sm px-3 py-1 rounded-full">
            MediaSource流播放中
          </div>
        )}

        {/* 说明文字 */}
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white p-4 rounded max-w-md">
          <h3 className="font-semibold mb-2">MediaSource流拼接技术</h3>
          <p className="text-sm text-gray-300">
            这是真正的视频流级别拼接技术。不同于传统的播放器切换，
            MediaSource API将多个视频文件的数据直接拼接成一个连续的流，
            实现完全无缝的播放体验。
          </p>
        </div>

        {/* 浏览器兼容性提示 */}
        <div className="absolute top-4 left-4 bg-blue-600 bg-opacity-90 text-white p-3 rounded">
          <div className="text-sm font-semibold">浏览器要求</div>
          <div className="text-xs mt-1">
            需要支持MediaSource API<br/>
            Chrome 23+, Firefox 42+, Safari 8+
          </div>
        </div>
      </div>
    </div>
  )
}
