import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import {
  Volume2,
  VolumeX,
  Video,
  Monitor,
  Smartphone
} from 'lucide-react'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { FLVPlayer, FLVPlayerRef } from '@/components/FLVPlayer'
import { useOBSPlayerSettings } from '@/hooks/useOBSPlayerSettings'

export default function OBSPlayerPage() {
  const [isMuted, setIsMuted] = useState(false)
  const [volume, setVolume] = useState(1)
  const [flvUrl] = useState('http://localhost:2935/live/stream.flv') // FLV流地址

  // 使用持久化的横竖屏设置
  const { aspectRatio, setAspectRatio } = useOBSPlayerSettings()
  // FLV播放器引用
  const flvPlayerRef = useRef<FLVPlayerRef>(null)



  // 在页面加载时应用保存的比例设置
  useEffect(() => {
    const applyAspectRatio = async () => {
      try {
        if (typeof window !== 'undefined' && window.require) {
          const { ipcRenderer } = window.require('electron')
          await ipcRenderer.invoke(IPC_CHANNELS.obsPlayer.setAspectRatio, aspectRatio)
          console.log('已应用保存的比例设置:', aspectRatio)
        }
      } catch (error) {
        console.error('应用比例设置失败:', error)
      }
    }

    // 延迟一点时间确保窗口已完全加载
    const timer = setTimeout(applyAspectRatio, 500)
    return () => clearTimeout(timer)
  }, []) // 只在组件挂载时执行一次



  // 处理FLV播放错误
  const handleVideoError = (error: Error) => {
    console.error('FLV播放出错:', error)
  }

  // 静音/取消静音
  const toggleMute = () => {
    const newMuted = !isMuted
    setIsMuted(newMuted)

    // 更新FLV播放器
    if (flvPlayerRef.current) {
      flvPlayerRef.current.setMuted(newMuted)
    }
  }

  // 音量控制
  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume)

    // 更新FLV播放器
    if (flvPlayerRef.current) {
      flvPlayerRef.current.setVolume(newVolume)
    }
  }

  // 切换窗口比例
  const toggleAspectRatio = async () => {
    const newRatio = aspectRatio === '16:9' ? '9:16' : '16:9'

    try {
      if (typeof window !== 'undefined' && window.require) {
        const { ipcRenderer } = window.require('electron')
        await ipcRenderer.invoke(IPC_CHANNELS.obsPlayer.setAspectRatio, newRatio)
        // 更新持久化设置
        setAspectRatio(newRatio)
      }
    } catch (error) {
      console.error('切换窗口比例失败:', error)
    }
  }

  return (
    <div className="h-screen bg-black text-white flex flex-col" data-obs-capture="digital-human-obs-player">
      {/* 顶部信息栏 */}
      <div className="bg-gray-900 p-2 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Video className="h-5 w-5" />
          <span className="text-sm font-medium">数字人播放器</span>
          <span className="text-xs px-2 py-1 rounded text-white bg-blue-600">
            FLV直播
          </span>

          {/* 比例切换按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleAspectRatio}
            className="text-white hover:bg-white hover:bg-opacity-20 h-6 px-2"
            title={`当前: ${aspectRatio}, 点击切换`}
          >
            {aspectRatio === '16:9' ? (
              <Monitor className="h-3 w-3" />
            ) : (
              <Smartphone className="h-3 w-3" />
            )}
            <span className="ml-1 text-xs">{aspectRatio}</span>
          </Button>
        </div>
        <div className="text-xs text-gray-400">
          FLV直播流播放器
        </div>
      </div>

      {/* FLV播放区域 */}
      <div className="flex-1 relative bg-black min-h-0">
        <FLVPlayer
          ref={flvPlayerRef}
          src={flvUrl}
          className="w-full h-full"
          autoPlay={true}
          muted={isMuted}
          volume={volume}
          onError={handleVideoError}
        />

        {/* 音量控制覆盖层 */}
        <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-70 rounded-lg p-3 backdrop-blur-sm">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMute}
              className="text-white hover:bg-white hover:bg-opacity-20 flex-shrink-0"
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>

            <div className="flex-1 flex items-center gap-2 min-w-0">
              <span className="text-xs text-gray-300 flex-shrink-0">音量</span>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                className="flex-1 min-w-0 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                style={{
                  background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${volume * 100}%, #4b5563 ${volume * 100}%, #4b5563 100%)`
                }}
              />
              <span className="text-xs text-gray-300 w-10 text-right flex-shrink-0">{Math.round(volume * 100)}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
