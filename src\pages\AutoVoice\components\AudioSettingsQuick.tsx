import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { getAudioOutputDevices, isAudioOutputSupported, type AudioDevice } from '@/utils/audioDevices'
import { Speaker, Volume2, Gauge, Play } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useAutoVoice } from '@/hooks/useAutoVoice'


const AudioSettingsQuick = () => {
  const [audioDevices, setAudioDevices] = useState<AudioDevice[]>([])
  const [isAudioSupported, setIsAudioSupported] = useState(false)

  const {
    hostDeviceId,
    setHostDeviceId,
    hostVolume,
    setHostVolume,
    hostPlaybackSpeed,
    setHostPlaybackSpeed,
    assistantDeviceId,
    setAssistantDeviceId,
    assistantVol<PERSON>,
    setAssistantVolume,
    assistantPlaybackSpeed,
    setAssistantPlaybackSpeed
  } = useAutoVoice()

  // 初始化音频设备
  useEffect(() => {
    const initAudioDevices = async () => {
      setIsAudioSupported(isAudioOutputSupported())
      if (isAudioOutputSupported()) {
        const devices = await getAudioOutputDevices()
        setAudioDevices(devices)
      }
    }

    initAudioDevices()
  }, [])

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          title="音频设置"
        >
          <Volume2 className="h-4 w-4" />
          <span>音频输出</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[650px]" align="center">
        <div className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">音频设置</h4>
            <p className="text-sm text-muted-foreground">
              分别调整主播和助理的音频输出设备、音量和播放速度
            </p>
          </div>

          {/* 左右分栏布局 */}
          <div className="grid grid-cols-2 gap-6">

            {/* 主播音频设置 */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <Label className="text-sm font-medium">主播音频设置</Label>
              </div>

              {/* 主播音频输出设备 */}
              {isAudioSupported && audioDevices.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground flex items-center gap-2">
                    <Speaker className="h-3 w-3" />
                    输出设备
                  </Label>
                  <Select value={hostDeviceId} onValueChange={setHostDeviceId}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择音频输出设备" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">默认设备</SelectItem>
                      {audioDevices.map(device => (
                        <SelectItem key={device.deviceId} value={device.deviceId}>
                          {device.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* 主播音量 */}
              <div className="space-y-2">
                <Label className="text-xs text-muted-foreground">音量</Label>
                <div className="flex items-center gap-3">
                  <Volume2 className="h-4 w-4 text-blue-500" />
                  <div className="flex-1">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={hostVolume}
                      onChange={(e) => setHostVolume(Number(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${hostVolume}%, #e5e7eb ${hostVolume}%, #e5e7eb 100%)`
                      }}
                    />
                  </div>
                  <span className="text-sm text-muted-foreground w-12 text-right">{hostVolume}%</span>
                </div>
              </div>

              {/* 主播播放速度 */}
              <div className="space-y-2">
                <Label className="text-xs text-muted-foreground">播放速度</Label>
                <div className="flex items-center gap-3">
                  <Play className="h-4 w-4 text-blue-500" />
                  <div className="flex-1">
                    <input
                      type="range"
                      min="25"
                      max="200"
                      value={hostPlaybackSpeed * 100}
                      onChange={(e) => setHostPlaybackSpeed(Number(e.target.value) / 100)}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${(hostPlaybackSpeed - 0.25) / (2 - 0.25) * 100}%, #e5e7eb ${(hostPlaybackSpeed - 0.25) / (2 - 0.25) * 100}%, #e5e7eb 100%)`
                      }}
                    />
                  </div>
                  <span className="text-sm text-muted-foreground w-12 text-right">{hostPlaybackSpeed}x</span>
                </div>
              </div>
            </div>

            {/* 右侧：助理音频设置 */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <Label className="text-sm font-medium">助理音频设置</Label>
              </div>

              {/* 助理音频输出设备 */}
              {isAudioSupported && audioDevices.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground flex items-center gap-2">
                    <Speaker className="h-3 w-3" />
                    输出设备
                  </Label>
                  <Select value={assistantDeviceId} onValueChange={setAssistantDeviceId}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择音频输出设备" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">默认设备</SelectItem>
                      {audioDevices.map(device => (
                        <SelectItem key={device.deviceId} value={device.deviceId}>
                          {device.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* 助理音量 */}
              <div className="space-y-2">
                <Label className="text-xs text-muted-foreground">音量</Label>
                <div className="flex items-center gap-3">
                  <Volume2 className="h-4 w-4 text-green-500" />
                  <div className="flex-1">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={assistantVolume}
                      onChange={(e) => setAssistantVolume(Number(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #10b981 0%, #10b981 ${assistantVolume}%, #e5e7eb ${assistantVolume}%, #e5e7eb 100%)`
                      }}
                    />
                  </div>
                  <span className="text-sm text-muted-foreground w-12 text-right">{assistantVolume}%</span>
                </div>
              </div>

              {/* 助理播放速度 */}
              <div className="space-y-2">
                <Label className="text-xs text-muted-foreground">播放速度</Label>
                <div className="flex items-center gap-3">
                  <Play className="h-4 w-4 text-green-500" />
                  <div className="flex-1">
                    <input
                      type="range"
                      min="25"
                      max="200"
                      value={assistantPlaybackSpeed * 100}
                      onChange={(e) => setAssistantPlaybackSpeed(Number(e.target.value) / 100)}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #10b981 0%, #10b981 ${(assistantPlaybackSpeed - 0.25) / (2 - 0.25) * 100}%, #e5e7eb ${(assistantPlaybackSpeed - 0.25) / (2 - 0.25) * 100}%, #e5e7eb 100%)`
                      }}
                    />
                  </div>
                  <span className="text-sm text-muted-foreground w-12 text-right">{assistantPlaybackSpeed}x</span>
                </div>
              </div>
            </div>
          </div>

          {/* 错误提示信息 */}
          {!isAudioSupported && (
            <div className="text-sm text-muted-foreground text-center">
              当前浏览器不支持音频输出设备选择功能
            </div>
          )}

          {isAudioSupported && audioDevices.length === 0 && (
            <div className="text-sm text-muted-foreground text-center">
              未检测到可用的音频输出设备
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}

export default AudioSettingsQuick
