import { useMemoizedFn } from 'ahooks'
import { useEffect, useRef } from 'react'
import { IpcChannels } from 'shared/electron-api'

const api = window.ipcRenderer

export function useIpcListener<Channel extends keyof IpcChannels>(
  channel: Channel,
  listener: (...args: Parameters<IpcChannels[Channel]>) => void,
) {
  const callbackRef = useRef(listener)

  useEffect(() => {
    callbackRef.current = listener
  }, [listener])

  useEffect(() => {
    if (!api) return

    const removeListener = api.on(channel, callbackRef.current)

    return () => {
      removeListener()
    }
  }, [channel])
}

// export function useIpcRenderer() {
//   const ipcInvoke = useMemoizedFn(
//     <Channel extends keyof IpcChannels>(
//       channel: Channel,
//       ...args: Parameters<IpcChannels[Channel]>
//     ) => {
//       return api.invoke(channel, ...args)
//     },
//   )

//   const ipcSend = useMemoizedFn(
//     <Channel extends keyof IpcChannels>(
//       channel: Channel,
//       ...args: Parameters<IpcChannels[Channel]>
//     ) => {
//       return api.send(channel, ...args)
//     },
//   )

//   return { ipcInvoke, ipcSend }
// }
