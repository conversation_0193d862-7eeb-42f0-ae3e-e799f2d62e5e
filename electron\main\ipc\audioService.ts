import { dialog } from 'electron'
import { spawn, type ChildProcess, exec } from 'node:child_process'
import { dirname } from 'node:path'
import { promisify } from 'node:util'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { typedIpcMainHandle } from '#/utils'
import { createLogger } from '#/logger'

const execAsync = promisify(exec)

const logger = createLogger('AudioService')

// 服务进程管理基类
abstract class ServiceManager {
  protected process: ChildProcess | null = null
  protected serviceName: string
  protected logger: ReturnType<typeof createLogger>

  constructor(serviceName: string) {
    this.serviceName = serviceName
    this.logger = createLogger(serviceName)
  }

  public getStatus() {
    return {
      isRunning: this.process !== null && !this.process.killed,
      pid: this.process?.pid
    }
  }

  protected async killApplicationPythonProcesses(): Promise<void> {
    if (process.platform === 'win32') {
      try {
        // 获取当前进程的PID
        const currentPid = process.pid

        // 查找当前应用启动的Python进程（通过父进程关系）
        const { stdout } = await execAsync(`wmic process where "name='python.exe' or name='pythonw.exe'" get ProcessId,ParentProcessId,CommandLine /format:csv`)

        const lines = stdout.split('\n').filter(line => line.trim() && !line.startsWith('Node'))

        for (const line of lines) {
          const parts = line.split(',')
          if (parts.length >= 4) {
            const pid = parts[2]?.trim()
            const parentPid = parts[1]?.trim()
            const commandLine = parts[3]?.trim()

            // 检查是否是我们启动的进程（通过进程树关系或命令行路径）
            if (pid && (
              this.isOurChildProcess(parseInt(parentPid), currentPid) ||
              this.isOurScriptPath(commandLine)
            )) {
              try {
                await execAsync(`taskkill /F /PID ${pid}`)
                this.logger.info(`已终止应用相关的Python进程，PID: ${pid}`)
              } catch (killError) {
                this.logger.debug(`终止进程 ${pid} 失败: ${killError}`)
              }
            }
          }
        }
      } catch (error) {
        this.logger.debug(`查找应用Python进程时出错: ${error}`)
      }
    }
  }

  private isOurChildProcess(parentPid: number, rootPid: number): boolean {
    // 简单检查：如果父进程是当前进程，则认为是我们的子进程
    return parentPid === rootPid
  }

  private isOurScriptPath(commandLine: string): boolean {
    // 检查命令行是否包含我们的脚本路径
    return !!(commandLine && (
      commandLine.includes('D:\\SD2\\') ||
      commandLine.includes('python_4.ps1') ||
      commandLine.includes('heygemService')
    ))
  }
}

// 多音频服务进程管理
class MultiAudioServiceManager {
  private static instance: MultiAudioServiceManager
  private services: Map<string, ServiceManager> = new Map()
  private logger = createLogger('MultiAudioService')

  private constructor() {}

  public static getInstance() {
    if (!MultiAudioServiceManager.instance) {
      MultiAudioServiceManager.instance = new MultiAudioServiceManager()
    }
    return MultiAudioServiceManager.instance
  }

  // 为脚本路径生成唯一的服务ID
  private getServiceId(scriptPath: string): string {
    // 使用URL安全的base64编码
    return Buffer.from(scriptPath).toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  // 从服务ID解码获取原始脚本路径
  private getScriptPathFromServiceId(serviceId: string): string {
    // 反向URL安全的base64解码
    let base64 = serviceId
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    // 补充缺失的填充字符
    while (base64.length % 4) {
      base64 += '='
    }

    return Buffer.from(base64, 'base64').toString()
  }

  // 获取或创建服务实例
  private getOrCreateService(scriptPath: string): ServiceManager {
    const serviceId = this.getServiceId(scriptPath)

    if (!this.services.has(serviceId)) {
      const service = new (class extends ServiceManager {
        constructor() {
          super(`AudioService_${serviceId}`)
        }
      })()
      this.services.set(serviceId, service)
    }

    return this.services.get(serviceId)!
  }

  // 获取所有服务状态
  public getAllStatus(): { [scriptPath: string]: { isRunning: boolean; pid?: number } } {
    const status: { [scriptPath: string]: { isRunning: boolean; pid?: number } } = {}

    for (const [serviceId, service] of this.services) {
      // 从base64解码获取原始脚本路径
      try {
        const scriptPath = this.getScriptPathFromServiceId(serviceId)
        status[scriptPath] = service.getStatus()
      } catch (error) {
        this.logger.warn(`无法解码服务ID: ${serviceId}`)
      }
    }

    return status
  }

  // 获取特定脚本的状态
  public getStatus(scriptPath?: string): { isRunning: boolean; pid?: number } {
    if (!scriptPath) {
      // 如果没有指定脚本路径，返回是否有任何服务在运行
      const allStatus = this.getAllStatus()
      const hasRunning = Object.values(allStatus).some(status => status.isRunning)
      return { isRunning: hasRunning }
    }

    const service = this.getOrCreateService(scriptPath)
    return service.getStatus()
  }

  public async start(scriptPath: string): Promise<{ success: boolean; error?: string; pid?: number }> {
    try {
      const service = this.getOrCreateService(scriptPath)

      // 如果该脚本的服务已在运行，先停止
      if (service.getStatus().isRunning) {
        this.logger.info(`停止现有音频服务进程: ${scriptPath}`)
        await this.stop(scriptPath)
      }

      this.logger.info(`启动音频服务脚本: ${scriptPath}`)

      // 根据文件扩展名选择执行方式
      const isPs1 = scriptPath.toLowerCase().endsWith('.ps1')

      let command: string
      let args: string[]

      if (isPs1) {
        // PowerShell 脚本
        command = 'powershell.exe'
        args = ['-ExecutionPolicy', 'Bypass', '-File', scriptPath]
      } else {
        // 其他脚本，尝试直接执行
        command = scriptPath
        args = []
      }

      // 设置工作目录为脚本所在目录
      const workingDir = dirname(scriptPath)

      // 启动进程
      const process = spawn(command, args, {
        detached: false,
        stdio: ['ignore', 'pipe', 'pipe'],
        cwd: workingDir // 设置工作目录
      })

      // 将进程赋值给服务实例
      ;(service as any).process = process

      const pid = process.pid

      // 监听进程事件
      process.on('error', (error) => {
        this.logger.error(`音频服务进程错误 (${scriptPath}):`, error)
        ;(service as any).process = null
      })

      process.on('exit', (code, signal) => {
        this.logger.info(`音频服务进程退出 (${scriptPath}): code=${code}, signal=${signal}`)
        ;(service as any).process = null
      })

      // 监听输出（可选，用于调试）
      if (process.stdout) {
        process.stdout.on('data', (data) => {
          this.logger.debug(`音频服务输出 (${scriptPath}):`, data.toString())
        })
      }

      if (process.stderr) {
        process.stderr.on('data', (data) => {
          this.logger.info(`音频服务输出 (${scriptPath}):`, data.toString())
        })
      }

      this.logger.info(`音频服务已启动，PID: ${pid}, 脚本: ${scriptPath}`)
      return { success: true, pid }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.logger.error(`启动音频服务失败 (${scriptPath}):`, errorMessage)
      const service = this.getOrCreateService(scriptPath)
      ;(service as any).process = null
      return { success: false, error: errorMessage }
    }
  }

  public async stop(scriptPath?: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!scriptPath) {
        // 停止所有服务
        this.logger.info(`开始停止所有音频服务，共 ${this.services.size} 个服务`)

        const results: { success: boolean; error?: string; serviceId?: string }[] = []

        // 逐个停止服务，而不是并发停止，避免竞争条件
        for (const serviceId of this.services.keys()) {
          try {
            const originalPath = this.getScriptPathFromServiceId(serviceId)
            this.logger.info(`正在停止服务: ${originalPath}`)
            const result = await this.stop(originalPath)
            results.push({ ...result, serviceId })

            if (!result.success) {
              this.logger.error(`停止服务失败 (${serviceId}): ${result.error}`)
            } else {
              this.logger.info(`成功停止服务: ${originalPath}`)
            }
          } catch (error) {
            this.logger.error(`停止服务异常 (${serviceId}):`, error)
            results.push({ success: false, error: String(error), serviceId })
          }
        }

        const successCount = results.filter(r => r.success).length
        const totalCount = results.length
        this.logger.info(`停止所有服务完成: ${successCount}/${totalCount} 成功`)

        const hasError = results.some(result => !result.success)
        return {
          success: !hasError,
          error: hasError ? `部分服务停止失败: ${results.filter(r => !r.success).map(r => r.serviceId).join(', ')}` : undefined
        }
      }

      const service = this.getOrCreateService(scriptPath)
      const serviceProcess = (service as any).process

      if (!serviceProcess || serviceProcess.killed) {
        return { success: true }
      }

      const pid = serviceProcess.pid
      this.logger.info(`停止音频服务进程，PID: ${pid}, 脚本: ${scriptPath}`)

      // 在 Windows 上使用 taskkill 强制终止进程树
      if (process.platform === 'win32' && pid) {
        try {
          await execAsync(`taskkill /F /T /PID ${pid}`)
          this.logger.info(`已强制终止进程树，PID: ${pid}`)

          // 等待一段时间确保进程被终止
          await new Promise(resolve => setTimeout(resolve, 1000))

          // 验证进程是否真的被终止
          try {
            await execAsync(`tasklist /FI "PID eq ${pid}"`)
            this.logger.warn(`进程 ${pid} 可能仍在运行，尝试再次终止`)
            await execAsync(`taskkill /F /PID ${pid}`)
          } catch (checkError) {
            // 如果tasklist失败，说明进程已经不存在了，这是正常的
            this.logger.debug(`进程 ${pid} 已确认终止`)
          }
        } catch (killError) {
          this.logger.warn(`taskkill 失败，尝试其他方法: ${killError}`)
          try {
            serviceProcess.kill('SIGKILL')
            // 等待Node.js kill完成
            await new Promise(resolve => setTimeout(resolve, 1000))
          } catch (nodeKillError) {
            this.logger.error(`Node.js kill 也失败: ${nodeKillError}`)
            return { success: false, error: `无法终止进程 ${pid}` }
          }
        }
      } else {
        serviceProcess.kill('SIGTERM')
        await new Promise(resolve => setTimeout(resolve, 1000))
        if (serviceProcess && !serviceProcess.killed) {
          serviceProcess.kill('SIGKILL')
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      ;(service as any).process = null
      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.logger.error(`停止音频服务失败 (${scriptPath}):`, errorMessage)
      return { success: false, error: errorMessage }
    }
  }
}

// 数字人服务进程管理
class DigitalHumanServiceManager extends ServiceManager {
  private static instance: DigitalHumanServiceManager

  private constructor() {
    super('DigitalHumanService')
  }

  public static getInstance() {
    if (!DigitalHumanServiceManager.instance) {
      DigitalHumanServiceManager.instance = new DigitalHumanServiceManager()
    }
    return DigitalHumanServiceManager.instance
  }

  public async start(scriptPath: string): Promise<{ success: boolean; error?: string; pid?: number }> {
    try {
      // 如果已有进程在运行，先停止
      if (this.process && !this.process.killed) {
        this.logger.info('停止现有数字人服务进程')
        await this.stop()
      }

      this.logger.info(`启动数字人服务脚本: ${scriptPath}`)

      // 设置工作目录为脚本所在目录
      const workingDir = dirname(scriptPath)

      // 根据文件扩展名选择执行方式
      const isBat = scriptPath.toLowerCase().endsWith('.bat')
      const isPs1 = scriptPath.toLowerCase().endsWith('.ps1')

      let command: string
      let args: string[]

      if (isBat) {
        // Batch 脚本
        command = 'cmd.exe'
        args = ['/c', scriptPath]
      } else if (isPs1) {
        // PowerShell 脚本
        command = 'powershell.exe'
        args = ['-ExecutionPolicy', 'Bypass', '-File', scriptPath]
      } else {
        // 其他脚本，尝试直接执行
        command = scriptPath
        args = []
      }

      // 启动进程
      this.process = spawn(command, args, {
        detached: false,
        stdio: ['ignore', 'pipe', 'pipe'],
        cwd: workingDir
      })

      const pid = this.process.pid

      // 监听进程事件
      this.process.on('error', (error) => {
        this.logger.error('数字人服务进程错误:', error)
        this.process = null
      })

      this.process.on('exit', (code, signal) => {
        this.logger.info(`数字人服务进程退出: code=${code}, signal=${signal}`)
        this.process = null
      })

      // 监听输出（可选，用于调试）
      if (this.process.stdout) {
        this.process.stdout.on('data', (data) => {
          this.logger.debug('数字人服务输出:', data.toString())
        })
      }

      if (this.process.stderr) {
        this.process.stderr.on('data', (data) => {
          this.logger.warn('数字人服务输出日志:', data.toString())
        })
      }

      this.logger.info(`数字人服务已启动，PID: ${pid}`)
      return { success: true, pid }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.logger.error('启动数字人服务失败:', errorMessage)
      this.process = null
      return { success: false, error: errorMessage }
    }
  }

  public async stop(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.process || this.process.killed) {
        return { success: true }
      }

      const pid = this.process.pid
      this.logger.info(`停止数字人服务进程，PID: ${pid}`)

      // 在 Windows 上使用 taskkill 强制终止进程树
      if (process.platform === 'win32' && pid) {
        try {
          await execAsync(`taskkill /F /T /PID ${pid}`)
          this.logger.info(`已强制终止数字人服务进程树，PID: ${pid}`)
        } catch (killError) {
          this.logger.warn(`taskkill 失败，尝试其他方法: ${killError}`)
          try {
            this.process.kill('SIGKILL')
          } catch (nodeKillError) {
            this.logger.error(`Node.js kill 也失败: ${nodeKillError}`)
          }
        }
      } else {
        this.process.kill('SIGTERM')
        setTimeout(() => {
          if (this.process && !this.process.killed) {
            this.process.kill('SIGKILL')
          }
        }, 5000)
      }

      // 额外清理：终止相关的Python进程
      await this.killApplicationPythonProcesses()

      this.process = null
      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.logger.error('停止数字人服务失败:', errorMessage)
      return { success: false, error: errorMessage }
    }
  }
}

const audioServiceManager = MultiAudioServiceManager.getInstance()
const digitalHumanServiceManager = DigitalHumanServiceManager.getInstance()

function setupIpcHandlers() {
  // 选择脚本路径
  typedIpcMainHandle(IPC_CHANNELS.audioService.selectScriptPath, async () => {
    try {
      const result = await dialog.showOpenDialog({
        properties: ['openFile'],
        filters: [
          { name: 'PowerShell Scripts', extensions: ['ps1'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        defaultPath: 'D:\\SD2\\python_4.ps1'
      })

      if (result.canceled || result.filePaths.length === 0) {
        return null
      }

      return result.filePaths[0]
    } catch (error) {
      logger.error('选择脚本路径失败:', error)
      return null
    }
  })

  // 启动音频服务
  typedIpcMainHandle(IPC_CHANNELS.audioService.start, async (_, scriptPath: string) => {
    return await audioServiceManager.start(scriptPath)
  })

  // 停止音频服务
  typedIpcMainHandle(IPC_CHANNELS.audioService.stop, async (_, scriptPath?: string) => {
    return await audioServiceManager.stop(scriptPath)
  })

  // 获取服务状态
  typedIpcMainHandle(IPC_CHANNELS.audioService.status, async (_, scriptPath?: string) => {
    return audioServiceManager.getStatus(scriptPath)
  })

  // 数字人服务器相关处理器
  // 选择数字人脚本路径
  typedIpcMainHandle(IPC_CHANNELS.digitalHumanService.selectScriptPath, async () => {
    try {
      const result = await dialog.showOpenDialog({
        properties: ['openFile'],
        filters: [
          { name: 'Batch Scripts', extensions: ['bat'] },
          { name: 'PowerShell Scripts', extensions: ['ps1'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        defaultPath: 'D:\\SD2\\heygemService\\start.bat'
      })

      if (result.canceled || result.filePaths.length === 0) {
        return null
      }

      return result.filePaths[0]
    } catch (error) {
      logger.error('选择数字人脚本路径失败:', error)
      return null
    }
  })

  // 启动数字人服务
  typedIpcMainHandle(IPC_CHANNELS.digitalHumanService.start, async (_, scriptPath: string) => {
    return await digitalHumanServiceManager.start(scriptPath)
  })

  // 停止数字人服务
  typedIpcMainHandle(IPC_CHANNELS.digitalHumanService.stop, async () => {
    return await digitalHumanServiceManager.stop()
  })

  // 获取数字人服务状态
  typedIpcMainHandle(IPC_CHANNELS.digitalHumanService.status, async () => {
    return digitalHumanServiceManager.getStatus()
  })
}

export function setupAudioServiceIpcHandlers() {
  setupIpcHandlers()
}
