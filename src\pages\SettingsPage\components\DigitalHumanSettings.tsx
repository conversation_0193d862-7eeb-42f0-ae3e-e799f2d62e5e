import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import * as DialogPrimitive from '@radix-ui/react-dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

import { useToast } from '@/hooks/useToast'
import { useAutoReply } from '@/hooks/useAutoReply'
import {
  useDigitalHumanList,
  useDigitalHumanLoading,
  useDigitalHumanUploading,
  useSelectedDigitalHumans,
  useHostSelectedDigitalHumans,
  useAssistantSelectedDigitalHumans,
  useDigitalHumanActions
} from '@/hooks/useDigitalHumanSettings'
import { getComfyUIServer } from '@/utils/voiceUpload'
import { getDigitalHumanPossibleUrls } from '@/utils/digitalHumanUpload'

// 获取ComfyUI视频服务器地址的函数
function getComfyUIVideoServer(): string {
  try {
    // 尝试从localStorage中获取用户配置的ComfyUI视频服务器地址
    const voiceSettings = localStorage.getItem('voice-settings-storage')
    if (voiceSettings) {
      const parsed = JSON.parse(voiceSettings)
      const comfyUIVideoServer = parsed.state?.comfyUIVideoServer
      if (comfyUIVideoServer) {
        return comfyUIVideoServer
      }
    }
  } catch (error) {
    console.warn('获取ComfyUI视频服务器地址失败，使用默认地址:', error)
  }

  // 默认地址
  return 'http://127.0.0.1:8188'
}
import {
  Upload,
  Trash2,
  Video,
  X,
  CheckCircle,
  Clock,
  AlertCircle,
  CheckSquare,
  Square,
  MinusSquare,
  Download,
  Plus,
  Play,
  RefreshCw
} from 'lucide-react'
import { useEffect, useRef, useState, useCallback } from 'react'

// 上传队列项接口
interface UploadQueueItem {
  id: string
  file: File
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  error?: string
}

const DigitalHumanSettings = () => {
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [uploadQueue, setUploadQueue] = useState<UploadQueueItem[]>([])
  const [isProcessingQueue, setIsProcessingQueue] = useState(false)
  const [activeDigitalHumanTab, setActiveDigitalHumanTab] = useState<'host' | 'assistant'>('host')

  // 手动添加数字人形象状态
  const [manualFileName, setManualFileName] = useState('')

  // 拖拽上传状态
  const [isDragOver, setIsDragOver] = useState(false)
  const [dragCounter, setDragCounter] = useState(0)

  // 弹窗预览状态
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false)
  const [previewingDialogVideo, setPreviewingDialogVideo] = useState<string | null>(null)

  // 数字人形象相关状态
  const digitalHumanList = useDigitalHumanList()
  const isLoading = useDigitalHumanLoading()
  const isUploading = useDigitalHumanUploading()
  const selectedDigitalHumans = useSelectedDigitalHumans()
  const hostSelectedDigitalHumans = useHostSelectedDigitalHumans()
  const assistantSelectedDigitalHumans = useAssistantSelectedDigitalHumans()
  const {
    loadDigitalHumanList,
    addDigitalHumanToList,
    uploadDigitalHuman,
    deleteDigitalHuman,
    toggleDigitalHumanSelection,
    setSelectedDigitalHumans,
    setHostSelectedDigitalHumans,
    setAssistantSelectedDigitalHumans,
    toggleHostDigitalHumanSelection,
    toggleAssistantDigitalHumanSelection
  } = useDigitalHumanActions()

  // 获取当前活动标签页的选中数字人形象列表
  const getCurrentSelectedDigitalHumans = () => {
    return activeDigitalHumanTab === 'host' ? hostSelectedDigitalHumans : assistantSelectedDigitalHumans
  }

  // 获取当前活动标签页的切换函数
  const getCurrentToggleFunction = () => {
    return activeDigitalHumanTab === 'host' ? toggleHostDigitalHumanSelection : toggleAssistantDigitalHumanSelection
  }

  // 获取当前活动标签页的设置函数
  const getCurrentSetFunction = () => {
    return activeDigitalHumanTab === 'host' ? setHostSelectedDigitalHumans : setAssistantSelectedDigitalHumans
  }

  // 组件挂载时加载数字人形象列表
  useEffect(() => {
    const loadList = async () => {
      try {
        await loadDigitalHumanList()
      } catch (error) {
        console.error('加载数字人形象列表失败:', error)
        toast.error('无法连接到ComfyUI服务器，请检查服务器地址和状态')
      }
    }
    loadList()
  }, [loadDigitalHumanList, toast])

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    const newItems: UploadQueueItem[] = []

    for (let i = 0; i < files.length; i++) {
      const file = files[i]

      // 检查文件类型（视频文件）
      if (!file.type.startsWith('video/')) {
        toast.error(`文件 ${file.name} 不是有效的视频文件`)
        continue
      }

      // 检查文件大小（限制为100MB）
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (file.size > maxSize) {
        toast.error(`文件 ${file.name} 超过100MB大小限制`)
        continue
      }

      newItems.push({
        id: `${Date.now()}-${i}`,
        file,
        status: 'pending',
        progress: 0
      })
    }

    if (newItems.length > 0) {
      setUploadQueue(prev => [...prev, ...newItems])
      toast.success(`已添加 ${newItems.length} 个文件到上传队列`)
    }

    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // 处理拖拽文件
  const handleFileDrop = useCallback((files: FileList) => {
    const newItems: UploadQueueItem[] = []

    for (let i = 0; i < files.length; i++) {
      const file = files[i]

      // 检查文件类型（视频文件）
      if (!file.type.startsWith('video/')) {
        toast.error(`文件 ${file.name} 不是有效的视频文件`)
        continue
      }

      // 检查文件大小（限制为100MB）
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (file.size > maxSize) {
        toast.error(`文件 ${file.name} 超过100MB大小限制`)
        continue
      }

      newItems.push({
        id: `${Date.now()}-${i}`,
        file,
        status: 'pending',
        progress: 0
      })
    }

    if (newItems.length > 0) {
      setUploadQueue(prev => [...prev, ...newItems])
      toast.success(`已添加 ${newItems.length} 个文件到上传队列`)
    }
  }, [toast])

  // 拖拽事件处理
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragCounter(prev => prev + 1)
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true)
    }
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragCounter(prev => {
      const newCounter = prev - 1
      if (newCounter === 0) {
        setIsDragOver(false)
      }
      return newCounter
    })
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
    setDragCounter(0)

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileDrop(e.dataTransfer.files)
    }
  }, [handleFileDrop])

  // 处理上传队列
  const processUploadQueue = async () => {
    if (isProcessingQueue) return

    setIsProcessingQueue(true)

    try {
      const pendingItems = uploadQueue.filter(item => item.status === 'pending')

      for (const item of pendingItems) {
        // 更新状态为上传中
        setUploadQueue(prev => prev.map(qItem =>
          qItem.id === item.id
            ? { ...qItem, status: 'uploading' as const, progress: 0 }
            : qItem
        ))

        try {
          // 模拟上传进度
          for (let progress = 0; progress <= 100; progress += 10) {
            setUploadQueue(prev => prev.map(qItem =>
              qItem.id === item.id
                ? { ...qItem, progress }
                : qItem
            ))
            await new Promise(resolve => setTimeout(resolve, 100))
          }

          await uploadDigitalHuman(item.file)

          // 更新状态为成功
          setUploadQueue(prev => prev.map(qItem =>
            qItem.id === item.id
              ? { ...qItem, status: 'success' as const, progress: 100 }
              : qItem
          ))

          toast.success(`数字人形象 ${item.file.name} 上传成功`)
        } catch (error) {
          console.error('上传数字人形象失败:', error)

          // 更新状态为失败
          setUploadQueue(prev => prev.map(qItem =>
            qItem.id === item.id
              ? {
                ...qItem,
                status: 'error' as const,
                error: error instanceof Error ? error.message : '上传失败'
              }
              : qItem
          ))

          toast.error(`数字人形象 ${item.file.name} 上传失败`)
        }
      }
    } finally {
      setIsProcessingQueue(false)
    }
  }

  // 移除队列项
  const removeQueueItem = (id: string) => {
    setUploadQueue(prev => prev.filter(item => item.id !== id))
  }

  // 清空队列
  const clearQueue = () => {
    setUploadQueue([])
  }

  // 处理删除数字人形象
  const handleDeleteDigitalHuman = async (fileName: string) => {
    try {
      await deleteDigitalHuman(fileName)
      toast.success(`数字人形象 ${fileName} 删除成功`)
    } catch (error) {
      console.error('删除数字人形象失败:', error)
      toast.error(`删除数字人形象失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }



  // 处理手动添加数字人形象
  const handleManualAdd = () => {
    const fileName = manualFileName.trim()
    if (!fileName) {
      toast.error('请输入文件名')
      return
    }

    // 验证文件扩展名
    const validExtensions = ['mp4', 'avi', 'mov', 'wmv', 'mkv', 'webm']
    const extension = fileName.toLowerCase().split('.').pop()
    if (!extension || !validExtensions.includes(extension)) {
      toast.error('请输入有效的视频文件名（支持格式：MP4、AVI、MOV、WMV、MKV、WEBM）')
      return
    }

    // 检查是否已存在
    if (digitalHumanList.includes(fileName)) {
      toast.error('该数字人形象已存在')
      return
    }

    // 添加到本地列表
    addDigitalHumanToList(fileName)
    setManualFileName('')
    toast.success(`数字人形象 "${fileName}" 已添加到本地列表`)
  }

  // 处理全选/取消全选
  const handleSelectAll = () => {
    const currentSelectedDigitalHumans = getCurrentSelectedDigitalHumans()
    const setFunction = getCurrentSetFunction()

    if (currentSelectedDigitalHumans.length === digitalHumanList.length) {
      // 当前全选，取消全选
      setFunction([])
    } else {
      // 当前非全选，全选
      setFunction([...digitalHumanList])
    }
  }

  // 获取选择状态图标
  const getSelectionIcon = () => {
    const currentSelectedDigitalHumans = getCurrentSelectedDigitalHumans()

    if (currentSelectedDigitalHumans.length === 0) {
      return <Square className="h-4 w-4" />
    } else if (currentSelectedDigitalHumans.length === digitalHumanList.length) {
      return <CheckSquare className="h-4 w-4" />
    } else {
      return <MinusSquare className="h-4 w-4" />
    }
  }

  // 获取所有可能的视频URL路径（使用useMemo优化）
  const getPossibleVideoUrls = useCallback((digitalHuman: string): string[] => {
    // 如果已经是完整的URL，直接返回
    if (digitalHuman.startsWith('http://') || digitalHuman.startsWith('https://')) {
      return [digitalHuman]
    }

    // 提取文件名
    const filename = digitalHuman.includes('\\') || digitalHuman.includes('/')
      ? digitalHuman.split(/[\\\/]/).pop() || digitalHuman
      : digitalHuman

    // 使用工具函数获取可能的URL
    return getDigitalHumanPossibleUrls(filename)
  }, [])





  // 获取视频URL
  const getVideoUrl = useCallback((digitalHuman: string): string => {
    const possibleUrls = getPossibleVideoUrls(digitalHuman)
    return possibleUrls[0]
  }, [getPossibleVideoUrls])

  // 处理弹窗预览
  const handleDialogPreview = useCallback((digitalHuman: string) => {
    setPreviewingDialogVideo(digitalHuman)
    setPreviewDialogOpen(true)
  }, [])

  // 关闭弹窗预览
  const handleCloseDialogPreview = useCallback(() => {
    setPreviewDialogOpen(false)
    setPreviewingDialogVideo(null)
  }, [])

  // 获取自动回复配置
  const { config, updateLiveMode, updateAssistantEnabled, updateAssistantMode } = useAutoReply()
  const { liveMode, assistantEnabled, assistantMode } = config

  // 处理直播模式变更
  const handleLiveModeChange = (value: 'voice' | 'digital-human') => {
    if (value === 'digital-human') {
      // 检查是否有选中的数字人形象
      if (selectedDigitalHumans.length === 0) {
        toast.error('请先选择至少一个数字人形象')
        return
      }
      // 数字人模式自动开启助理
      if (!assistantEnabled) {
        updateAssistantEnabled(true)
        toast.success('已切换至数字人直播模式，助理功能已自动开启')
      } else {
        toast.success('已切换至数字人直播模式')
      }
    } else {
      toast.success('已切换至语音直播模式')
    }
    updateLiveMode(value)
  }

  // 处理助理功能变更
  const handleAssistantEnabledChange = (checked: boolean) => {
    if (!checked && liveMode === 'digital-human') {
      toast.error('数字人模式下不可关闭助理功能')
      return
    }
    updateAssistantEnabled(checked)
    toast.success(checked ? '助理功能已开启' : '助理功能已关闭')
  }

  // 处理助理模式变更
  const handleAssistantModeChange = (checked: boolean) => {
    const newMode = checked ? 'digital-human' : 'voice'

    if (newMode === 'digital-human') {
      // 检查是否有选中的数字人形象
      if (selectedDigitalHumans.length === 0) {
        toast.error('请先选择至少一个数字人形象')
        return
      }
      toast.success('已切换至数字人助理模式')
    } else {
      toast.success('已切换至语音助理模式')
    }

    updateAssistantMode(newMode)
  }

  return (
    <div className="space-y-6">
      {/* 直播模式和助理设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Video className="h-5 w-5" />
            直播模式设置
          </CardTitle>
          <CardDescription>
            配置直播模式、助理功能和数字人设置
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <h3 className="text-sm font-medium">直播模式</h3>
            <div className="flex items-center space-x-2">
              <Switch
                id="live-mode"
                checked={liveMode === 'digital-human'}
                onCheckedChange={(checked) => {
                  handleLiveModeChange(checked ? 'digital-human' : 'voice')
                }}
              />
              <Label htmlFor="live-mode">
                {liveMode === 'digital-human' ? '数字人' : '语音'}
              </Label>
            </div>
            <p className="text-xs text-muted-foreground">
              {liveMode === 'voice'
                ? '当前为语音直播模式：生成的内容将以语音形式播放'
                : '当前为数字人直播模式：生成的内容将以数字人视频形式播放（需要先选择数字人形象）'}
            </p>
          </div>

          <Separator />

          <div className="space-y-2">
            <h3 className="text-sm font-medium">助理功能</h3>
            <div className="flex items-center space-x-2">
              <Switch
                id="assistant-enabled"
                checked={assistantEnabled}
                onCheckedChange={handleAssistantEnabledChange}
                disabled={liveMode === 'digital-human'} // 数字人模式下不可关闭
              />
              <Label htmlFor="assistant-enabled">
                {assistantEnabled ? '已开启' : '已关闭'}
              </Label>
              {liveMode === 'digital-human' && (
                <span className="text-xs text-muted-foreground">(数字人模式下自动开启)</span>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              助理功能控制AI自动回复、智能对话等功能。数字人模式下助理功能将自动开启且不可关闭。
            </p>
          </div>

          {assistantEnabled && (
            <>
              <Separator />

              <div className="space-y-2">
                <h3 className="text-sm font-medium">助理模式</h3>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="assistant-mode"
                    checked={assistantMode === 'digital-human'}
                    onCheckedChange={handleAssistantModeChange}
                  />
                  <Label htmlFor="assistant-mode">
                    {assistantMode === 'digital-human' ? '数字人' : '语音'}
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground">
                  {assistantMode === 'voice'
                    ? '当前为语音助理模式：AI回复和自动互动将以语音形式输出'
                    : '当前为数字人助理模式：AI回复和自动互动将以数字人视频形式输出（需要先选择数字人形象）'}
                </p>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* 数字人形象上传 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Video className="h-5 w-5" />
            数字人形象管理
          </CardTitle>
          <CardDescription>
            上传和管理数字人形象文件，支持 MP4、AVI、MOV 等视频格式
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 上传区域 */}
          <div className="space-y-4">
            {/* 拖拽上传区域 */}
            <div
              className={`
                relative border-2 border-dashed rounded-md p-4 text-center transition-all duration-200 cursor-pointer
                ${isDragOver
                  ? 'border-primary bg-primary/5 scale-[1.01]'
                  : 'border-muted-foreground/25 hover:border-muted-foreground/50 hover:bg-muted/50'
                }
                ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
              `}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onClick={() => !isUploading && fileInputRef.current?.click()}
            >
              <div className="flex items-center gap-3">
                <div className={`
                  p-2 rounded-full transition-colors duration-200 flex-shrink-0
                  ${isDragOver ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}
                `}>
                  <Upload className="h-4 w-4" />
                </div>

                <div className="flex-1 text-left">
                  <p className="text-sm font-medium">
                    {isDragOver ? '松开上传' : '拖拽或点击上传视频文件'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    支持 MP4、MOV、AVI 等格式，最大 100MB
                  </p>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  disabled={isUploading}
                  className="flex items-center gap-1 flex-shrink-0"
                  onClick={(e) => {
                    e.stopPropagation()
                    fileInputRef.current?.click()
                  }}
                >
                  <Upload className="h-3 w-3" />
                  选择
                </Button>
              </div>

              {/* 拖拽覆盖层 */}
              {isDragOver && (
                <div className="absolute inset-0 bg-primary/10 rounded-md flex items-center justify-center">
                  <div className="text-primary font-medium text-sm">
                    松开上传文件
                  </div>
                </div>
              )}
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept="video/*"
              multiple
              onChange={handleFileSelect}
              className="hidden"
            />

            {/* 手动添加数字人形象 */}
            <div className="flex gap-2">
              <Input
                placeholder="输入数字人形象文件名 (如: avatar.mp4)"
                value={manualFileName}
                onChange={(e) => setManualFileName(e.target.value)}
                className="flex-1"
              />
              <Button
                onClick={handleManualAdd}
                disabled={!manualFileName.trim()}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                添加
              </Button>
            </div>

            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                支持的格式：MP4、AVI、MOV、WMV 等视频格式，仅限已上传过的文件。
              </p>


            </div>
          </div>

          {/* 上传队列 */}
          {uploadQueue.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>上传队列 ({uploadQueue.length} 项)</Label>
                <div className="flex items-center gap-2">
                  {uploadQueue.some(item => item.status === 'pending') && (
                    <Button
                      size="sm"
                      onClick={processUploadQueue}
                      disabled={isProcessingQueue}
                    >
                      开始上传
                    </Button>
                  )}
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={clearQueue}
                  >
                    清空队列
                  </Button>
                </div>
              </div>

              <ScrollArea className="h-32 border rounded-md p-2">
                <div className="space-y-2">
                  {uploadQueue.map((item) => (
                    <div key={item.id} className="flex items-center gap-2 p-2 border rounded">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          {item.status === 'pending' && <Clock className="h-4 w-4 text-muted-foreground" />}
                          {item.status === 'uploading' && <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />}
                          {item.status === 'success' && <CheckCircle className="h-4 w-4 text-green-500" />}
                          {item.status === 'error' && <AlertCircle className="h-4 w-4 text-red-500" />}

                          <span className="text-sm truncate">{item.file.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {(item.file.size / 1024 / 1024).toFixed(1)}MB
                          </Badge>
                        </div>

                        {item.status === 'uploading' && (
                          <Progress value={item.progress} className="h-1 mt-1" />
                        )}

                        {item.status === 'error' && item.error && (
                          <p className="text-xs text-red-500 mt-1">{item.error}</p>
                        )}
                      </div>

                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeQueueItem(item.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 数字人形象列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Video className="h-5 w-5" />
            数字人形象设置
          </CardTitle>
          <CardDescription>
            管理已上传的数字人形象，分别为主播和助理选择形象
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeDigitalHumanTab} onValueChange={(value) => setActiveDigitalHumanTab(value as 'host' | 'assistant')}>
            <div className="flex items-center justify-between mb-4">
              <TabsList className="grid w-[400px] grid-cols-2">
                <TabsTrigger value="host" className="flex items-center gap-2">
                  主播形象 ({hostSelectedDigitalHumans.length} 个已选择)
                </TabsTrigger>
                <TabsTrigger value="assistant" className="flex items-center gap-2">
                  助理形象 ({assistantSelectedDigitalHumans.length} 个已选择)
                </TabsTrigger>
              </TabsList>
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {digitalHumanList.length} 个形象
                </Badge>
              </div>
            </div>

            <TabsContent value={activeDigitalHumanTab}>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                  <span>加载中...</span>
                </div>
              ) : digitalHumanList.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Video className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>暂无数字人形象</p>
                  <p className="text-sm mt-2">点击上方"选择数字人形象文件"按钮上传形象</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* 批量操作 */}
                  <div className="flex items-center justify-between">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSelectAll}
                      className="flex items-center gap-2"
                    >
                      {getSelectionIcon()}
                      {getCurrentSelectedDigitalHumans().length === digitalHumanList.length ? '取消全选' : '全选'}
                    </Button>

                    <div className="text-sm text-muted-foreground">
                      生成视频时将从选中的{activeDigitalHumanTab === 'host' ? '主播' : '助理'}形象中随机选择
                    </div>
                  </div>

                  <Separator />

                  {/* 数字人形象宫格列表 */}
                  <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-3 p-2">
                    {digitalHumanList.map((digitalHuman) => {
                      const isSelected = getCurrentSelectedDigitalHumans().includes(digitalHuman)
                      const toggleFunction = getCurrentToggleFunction()

                      return (
                        <div
                          key={`${activeDigitalHumanTab}-${digitalHuman}`}
                          className={`
                          relative group border rounded-lg overflow-hidden transition-all cursor-pointer aspect-[9/16]
                          ${isSelected ? 'border-primary ring-2 ring-primary/20 bg-primary/5' : 'border-border hover:border-primary/50 hover:shadow-md'}
                        `}
                          onClick={() => handleDialogPreview(digitalHuman)}
                        >
                          {/* 视频预览区域 */}
                          <div className="w-full h-full relative overflow-hidden">
                            {/* 显示视频的第一帧作为缩略图 */}
                            <video
                              src={getVideoUrl(digitalHuman)}
                              className="w-full h-full object-cover"
                              muted
                              preload="metadata"
                              playsInline
                            />

                            {/* 播放按钮覆盖层 */}
                            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center pointer-events-none">
                              <div className="bg-white/90 dark:bg-black/90 rounded-full p-2">
                                <Play className="h-4 w-4 text-slate-700 dark:text-slate-300" />
                              </div>
                            </div>
                          </div>

                          {/* 选中状态指示器 - 改为可点击的选择按钮 */}
                          <div className="absolute top-2 left-2">
                            <Button
                              size="sm"
                              variant={isSelected ? "default" : "secondary"}
                              className="h-6 w-6 p-0 opacity-80 group-hover:opacity-100 transition-opacity"
                              onClick={(e) => {
                                e.stopPropagation()
                                toggleFunction(digitalHuman)
                              }}
                              title={isSelected ? "取消选择" : "选择此形象"}
                            >
                              {isSelected ? (
                                <CheckSquare className="h-3 w-3" />
                              ) : (
                                <Square className="h-3 w-3" />
                              )}
                            </Button>
                          </div>

                          {/* 操作按钮 */}
                          <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            {/* 下载按钮 */}
                            <Button
                              size="sm"
                              variant="secondary"
                              className="h-6 w-6 p-0"
                              onClick={(e) => {
                                e.stopPropagation()
                                // TODO: 实现下载功能
                                toast.success('下载功能开发中')
                              }}
                              title="下载数字人形象"
                            >
                              <Download className="h-3 w-3" />
                            </Button>

                            {/* 删除按钮 */}
                            <Button
                              size="sm"
                              variant="destructive"
                              className="h-6 w-6 p-0"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleDeleteDigitalHuman(digitalHuman)
                              }}
                              title="删除数字人形象"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>

                          {/* 文件名显示 */}
                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
                            <div className="text-white text-xs font-medium truncate">
                              {digitalHuman}
                            </div>
                          </div>

                          {/* 选中状态遮罩 */}
                          {isSelected && (
                            <div className="absolute inset-0 bg-primary/10 pointer-events-none" />
                          )}
                        </div>
                      )
                    })}
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 沉浸式预览弹窗 */}
      <Dialog open={previewDialogOpen} onOpenChange={setPreviewDialogOpen}>
        <DialogPrimitive.Portal>
          <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
          <DialogPrimitive.Content className="fixed inset-0 z-50 w-screen h-screen p-0 border-0 bg-black/95 flex items-center justify-center data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0">
            <div className="relative w-full h-full flex items-center justify-center">
              {previewingDialogVideo && (
                <div className="relative max-w-[90vw] max-h-[90vh] aspect-[9/16]">
                  <video
                    src={getVideoUrl(previewingDialogVideo)}
                    className="w-full h-full object-cover rounded-lg shadow-2xl"
                    autoPlay
                    loop
                    muted
                    playsInline
                    controls
                  />
                </div>
              )}

              {/* 关闭按钮 */}
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-4 right-4 text-white hover:bg-white/20 h-10 w-10"
                onClick={handleCloseDialogPreview}
              >
                <X className="h-6 w-6" />
              </Button>

              {/* 文件名显示 */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-black/60 backdrop-blur-sm rounded-full px-4 py-2">
                  <p className="text-white text-sm font-medium">
                    {previewingDialogVideo}
                  </p>
                </div>
              </div>
            </div>
          </DialogPrimitive.Content>
        </DialogPrimitive.Portal>
      </Dialog>

    </div >
  )
}

export default DigitalHumanSettings
