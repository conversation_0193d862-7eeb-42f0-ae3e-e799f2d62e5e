{"$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json", "appId": "com.qbw.obalivetool", "asar": true, "directories": {"output": "release/${version}"}, "files": ["dist-electron", "dist"], "mac": {"artifactName": "${productName}_${version}.${ext}", "target": [{"target": "dmg", "arch": ["universal"]}], "icon": "public/favicon.png"}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "public/favicon.ico", "artifactName": "${productName}_${version}.${ext}"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": false, "differentialPackage": true}}