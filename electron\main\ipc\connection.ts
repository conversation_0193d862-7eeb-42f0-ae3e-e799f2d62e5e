import { IPC_CHANNELS } from 'shared/ipcChannels'
import { createLogger } from '#/logger'
import { accountManager } from '#/managers/AccountManager'
import { contextManager } from '#/managers/BrowserContextManager'
import { LiveControlManager } from '#/tasks/connection/LiveControlManager'
import { typedIpcMainHandle } from '#/utils'

const TASK_NAME = '中控台'

function setupIpcHandlers() {
  typedIpcMainHandle(
    IPC_CHANNELS.tasks.liveControl.connect,
    async (_, { chromePath, headless, storageState, platform = 'douyin', testConnection = false }) => {
      const account = accountManager.getActiveAccount()

      const manager = new LiveControlManager(platform)
      if (chromePath) manager.setChromePath(chromePath)

      try {
        // 如果启用了测试连接模式，使用测试连接
        if (testConnection) {
          const { browser, context, page, accountName } = await manager.connectTest()
          contextManager.setContext(account.id, {
            browser,
            browserContext: context,
            page,
            platform,
            testConnection: true,
          })
          return {
            accountName,
          }
        }

        // 正常连接模式
        const { browser, context, page, accountName } = await manager.connect({
          headless,
          storageState,
        })

        contextManager.setContext(account.id, {
          browser,
          browserContext: context,
          page,
          platform,
          testConnection: false,
        })

        return {
          accountName,
        }
      } catch (error) {
        const logger = createLogger(TASK_NAME)
        logger.error(
          '连接直播间失败:',
          error instanceof Error ? error.message : String(error),
        )

        manager.disconnect()

        return null
      }
    },
  )

  typedIpcMainHandle(IPC_CHANNELS.tasks.liveControl.disconnect, async () => {
    try {
      const currentContext = contextManager.getCurrentContext()
      await currentContext.browser.close()
      return true
    } catch (error) {
      const logger = createLogger(TASK_NAME)
      logger.error(
        '断开连接失败:',
        error instanceof Error ? error.message : String(error),
      )
      return false
    }
  })
}

export function setupLiveControlIpcHandlers() {
  setupIpcHandlers()
}
