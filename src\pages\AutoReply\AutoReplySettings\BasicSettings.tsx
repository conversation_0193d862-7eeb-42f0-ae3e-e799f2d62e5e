import React from 'react'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import ValidatedNumberInput from '@/components/common/ValidateNumberInput'
import { useToast } from '@/hooks/useToast'
import { useAutoReply } from '@/hooks/useAutoReply'

import BlocklistManager from './BlocklistManager'
import EventAutoReplySettings from './EventAutoReplySettings'

const BasicSettings = () => {
  const { toast } = useToast()
  const { config, updateGeneralSettings, updateWSConfig } = useAutoReply()
  const { entry: listeningSource } = config

  // 处理监听源变更
  const handleSourceChange = (value: 'control' | 'compass') => {
    updateGeneralSettings({ entry: value })
    toast.success(`已切换至${value === 'control' ? '中控台' : '大屏'}监听`)
  }



  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-sm font-medium">评论来源</h3>
        <Select
          value={listeningSource}
          onValueChange={value =>
            handleSourceChange(value as 'control' | 'compass')
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="选择监听来源" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="control">中控台监听</SelectItem>
            <SelectItem value="compass">电商罗盘大屏</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          {listeningSource === 'control'
            ? '中控台监听只能获取评论消息'
            : '大屏监听可以获取评论、点赞、进入直播间等全部消息类型'}
        </p>
      </div>

      <Separator />

      <div className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Switch
              id="hide-host"
              checked={config.hideHost}
              onCheckedChange={checked =>
                updateGeneralSettings({ hideHost: checked })
              }
            />
            <Label htmlFor="hide-host">屏蔽自己的消息</Label>
          </div>
          <p className="text-xs text-muted-foreground">
            开启后，在消息列表中将不显示主播自己发送的消息，避免干扰查看观众评论
          </p>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Switch
              id="hide-username"
              checked={config.hideUsername}
              onCheckedChange={checked =>
                updateGeneralSettings({ hideUsername: checked })
              }
            />
            <Label htmlFor="hide-username">隐藏用户名</Label>
          </div>
          <p className="text-xs text-muted-foreground">
            系统会自动将
            <span className="font-bold mx-1 bg-muted px-1 rounded-md">
              {'{用户名}'}
            </span>
            替换为实际的用户名称，如果设置隐藏用户名，只会保留用户名的第一个字符。
            <br />
            未设置隐藏用户名时：{' '}
            <span className="font-bold mx-1 bg-muted px-1 rounded-md">
              {'{用户名}'}
            </span>{' '}
            {'->'}{' '}
            <span className="font-bold mx-1 bg-muted px-1 rounded-md">
              张三
            </span>
            <br />
            设置隐藏用户名时：{' '}
            <span className="font-bold mx-1 bg-muted px-1 rounded-md">
              {'{用户名}'}
            </span>{' '}
            {'->'}{' '}
            <span className="font-bold mx-1 bg-muted px-1 rounded-md">
              张***
            </span>
          </p>
        </div>

        <Separator />

        {listeningSource === 'compass' && (
          <>
            <EventAutoReplySettings />
            <Separator />
          </>
        )}

        <BlocklistManager />

        <Separator />

        <div className="flex flex-col space-y-1">
          <div className="flex justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                id="websocket-enable"
                checked={!!config.ws?.enable}
                onCheckedChange={checked =>
                  updateWSConfig({ enable: checked })
                }
              />
              <Label htmlFor="websocket-enable">启用 WebSocket 服务</Label>
            </div>
            <div className="flex space-x-1 items-center">
              <Label>端口号：</Label>
              <ValidatedNumberInput
                disabled={!config.ws?.enable}
                className="w-24"
                type="number"
                placeholder="12354"
                min={2000}
                max={65535}
                value={config.ws?.port ?? 12354}
                onCommit={value => updateWSConfig({ port: value })}
              />
            </div>
          </div>
          <p className="text-xs text-muted-foreground">
            启用时，点击「开始监听」按钮后将同步启动本地 WebSocket
            服务端，当有新的评论信息时会同步向客户端发送 JSON 格式的信息。
            <br />
            可以通过 WebSocket 客户端连接
            <span className="border p-0.5 px-1 rounded-md mx-1">
              ws://localhost:{config.ws?.port}
            </span>
          </p>
        </div>
      </div>
    </div>
  )
}

export default BasicSettings
