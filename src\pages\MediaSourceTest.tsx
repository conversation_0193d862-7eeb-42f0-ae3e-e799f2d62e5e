import React, { useRef, useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import MediaSourceAdapter, { MediaSourceSeamlessPlayerRef } from '@/components/MediaSourceAdapter'
import { VideoItem } from '@/components/MediaSourceSeamlessPlayer'

/**
 * MediaSource流拼接测试页面
 * 演示真正无缝的视频流拼接效果
 */
export default function MediaSourceTest() {
  const playerRef = useRef<MediaSourceSeamlessPlayerRef>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentText, setCurrentText] = useState('')
  const [logs, setLogs] = useState<string[]>([])

  // 测试视频列表（使用示例视频URL）
  const testVideos: VideoItem[] = [
    {
      id: 'test1',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      text: '测试视频1 - Big Buck Bunny',
      duration: 596 // 约10分钟
    },
    {
      id: 'test2', 
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
      text: '测试视频2 - Elephants Dream',
      duration: 653 // 约11分钟
    },
    {
      id: 'test3',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
      text: '测试视频3 - For Bigger Blazes',
      duration: 15
    },
    {
      id: 'test4',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
      text: '测试视频4 - For Bigger Escapes',
      duration: 15
    },
    {
      id: 'test5',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
      text: '测试视频5 - For Bigger Fun',
      duration: 60
    }
  ]

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]) // 保留最近20条
  }

  const handlePlaySingle = async (video: VideoItem) => {
    if (!playerRef.current) return
    
    try {
      addLog(`开始播放单个视频: ${video.text}`)
      await playerRef.current.playVideo(video)
      setIsPlaying(true)
      setCurrentText(video.text)
      addLog(`单个视频播放成功: ${video.text}`)
    } catch (error) {
      addLog(`播放失败: ${error}`)
      console.error('播放失败:', error)
    }
  }

  const handlePlayQueue = async () => {
    if (!playerRef.current) return
    
    try {
      addLog(`开始播放队列，共 ${testVideos.length} 个视频`)
      await playerRef.current.setQueue(testVideos)
      setIsPlaying(true)
      addLog('队列播放开始 - 这将是真正无缝的流拼接！')
    } catch (error) {
      addLog(`队列播放失败: ${error}`)
      console.error('播放队列失败:', error)
    }
  }

  const handleAddToStream = async (video: VideoItem) => {
    if (!playerRef.current) return
    
    try {
      addLog(`添加视频到流: ${video.text}`)
      await playerRef.current.playVideo(video)
      addLog(`视频已添加到流: ${video.text}`)
    } catch (error) {
      addLog(`添加到流失败: ${error}`)
      console.error('添加到流失败:', error)
    }
  }

  const handleStop = () => {
    if (!playerRef.current) return
    
    playerRef.current.stop()
    setIsPlaying(false)
    setCurrentText('')
    addLog('播放已停止')
  }

  const handleVideoEnded = (videoId: string) => {
    addLog(`视频播放结束: ${videoId}`)
    
    // 检查是否还有更多视频
    if (playerRef.current) {
      const state = playerRef.current.getState()
      if (!state.isPlaying) {
        setIsPlaying(false)
        setCurrentText('')
        addLog('所有视频播放完成')
      }
    }
  }

  const handleSegmentAdded = (videoId: string, duration: number) => {
    addLog(`视频片段已添加到流: ${videoId} (时长: ${Math.round(duration)}s)`)
  }

  const handleVideoError = (error: Error, videoId?: string) => {
    addLog(`视频播放错误: ${error.message} ${videoId ? `(${videoId})` : ''}`)
    console.error('视频播放错误:', error, videoId)
    setIsPlaying(false)
  }

  return (
    <div className="h-screen bg-gray-900 text-white flex">
      {/* 左侧控制面板 */}
      <div className="w-1/3 bg-gray-800 p-4 border-r border-gray-700 flex flex-col">
        {/* 标题 */}
        <div className="mb-4">
          <h1 className="text-xl font-bold">MediaSource流拼接测试</h1>
          <p className="text-sm text-gray-400 mt-1">
            真正无缝的视频流拼接技术演示
          </p>
        </div>

        {/* 主要控制按钮 */}
        <div className="mb-4">
          <Button
            onClick={handlePlayQueue}
            disabled={isPlaying}
            className="w-full mb-2 bg-blue-600 hover:bg-blue-700"
          >
            播放完整队列（无缝拼接）
          </Button>
          
          <Button
            onClick={handleStop}
            disabled={!isPlaying}
            variant="destructive"
            className="w-full"
          >
            停止播放
          </Button>
        </div>

        {/* 单个视频测试 */}
        <div className="mb-4">
          <h3 className="text-sm font-semibold mb-2">单个视频测试</h3>
          <div className="space-y-1">
            {testVideos.slice(0, 3).map((video) => (
              <Button
                key={video.id}
                onClick={() => handlePlaySingle(video)}
                disabled={isPlaying}
                variant="outline"
                size="sm"
                className="w-full text-left justify-start text-xs"
              >
                {video.text}
              </Button>
            ))}
          </div>
        </div>

        {/* 动态添加到流 */}
        <div className="mb-4">
          <h3 className="text-sm font-semibold mb-2">动态添加到流</h3>
          <div className="space-y-1">
            {testVideos.slice(3).map((video) => (
              <Button
                key={video.id}
                onClick={() => handleAddToStream(video)}
                disabled={!isPlaying}
                variant="secondary"
                size="sm"
                className="w-full text-left justify-start text-xs"
              >
                + {video.text}
              </Button>
            ))}
          </div>
        </div>

        {/* 状态显示 */}
        <div className="mb-4 p-3 bg-gray-700 rounded">
          <div className="text-sm">
            <div>状态: {isPlaying ? '播放中' : '已停止'}</div>
            <div>当前: {currentText || '无'}</div>
          </div>
        </div>

        {/* 日志显示 */}
        <div className="flex-1">
          <h3 className="text-sm font-semibold mb-2">操作日志</h3>
          <div className="bg-gray-700 rounded p-2 h-full overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index} className="text-xs text-gray-300 mb-1">
                {log}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 右侧播放器区域 */}
      <div className="flex-1 relative bg-black">
        <MediaSourceAdapter
          ref={playerRef}
          className="w-full h-full"
          autoPlay={true}
          muted={false}
          volume={0.5}
          onVideoEnded={handleVideoEnded}
          onError={handleVideoError}
          onSegmentAdded={handleSegmentAdded}
        />

        {/* 播放信息覆盖层 */}
        {currentText && (
          <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-70 text-white p-3 rounded">
            <div className="text-sm font-medium">{currentText}</div>
            <div className="text-xs text-gray-300 mt-1">
              {isPlaying ? 'MediaSource流播放中...' : '已暂停'}
            </div>
          </div>
        )}

        {/* 技术说明 */}
        <div className="absolute top-4 left-4 bg-blue-600 bg-opacity-90 text-white p-3 rounded max-w-md">
          <div className="text-sm font-semibold mb-1">MediaSource流拼接技术</div>
          <div className="text-xs">
            • 真正的视频流级别拼接<br/>
            • 零视觉间断和卡顿<br/>
            • 动态添加视频片段<br/>
            • 专业级无缝播放体验
          </div>
        </div>
      </div>
    </div>
  )
}
