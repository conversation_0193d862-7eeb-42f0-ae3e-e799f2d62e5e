export const providers = {
  openrouter: {
    name: 'OpenRouter',
    baseURL: 'https://openrouter.ai/api/v1',
    apiUrl: 'https://openrouter.ai/keys',
    models: [
      'deepseek/deepseek-r1-distill-qwen-7b',
      'deepseek/deepseek-r1-0528-qwen3-8b:free',
      'deepseek/deepseek-r1-0528-qwen3-8b',
      'deepseek/deepseek-r1-0528:free',
      'deepseek/deepseek-r1-0528',
      'deepseek/deepseek-prover-v2',
      'tngtech/deepseek-r1t-chimera:free',
      'deepseek/deepseek-v3-base:free',
      'deepseek/deepseek-chat-v3-0324:free',
      'deepseek/deepseek-chat-v3-0324',
      'deepseek/deepseek-r1-distill-llama-8b',
      'deepseek/deepseek-r1-distill-qwen-1.5b',
      'deepseek/deepseek-r1-distill-qwen-32b',
      'deepseek/deepseek-r1-distill-qwen-14b:free',
      'deepseek/deepseek-r1-distill-qwen-14b',
      'deepseek/deepseek-r1-distill-llama-70b:free',
      'deepseek/deepseek-r1-distill-llama-70b',
      'deepseek/deepseek-r1:free',
      'deepseek/deepseek-r1',
      'deepseek/deepseek-chat:free',
      'deepseek/deepseek-chat',
    ],
  },
  siliconflow: {
    name: '硅基流动',
    baseURL: 'https://api.siliconflow.cn/v1',
    apiUrl: 'https://cloud.siliconflow.cn/account/ak',
    models: [
      'deepseek-ai/DeepSeek-V2.5',
      'deepseek-ai/deepseek-vl2',
      'deepseek-ai/DeepSeek-V3',
      'deepseek-ai/DeepSeek-R1',
      'Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
      'Pro/deepseek-ai/DeepSeek-R1-Distill-Llama-8B',
      'deepseek-ai/DeepSeek-R1-Distill-Qwen-14B',
      'deepseek-ai/DeepSeek-R1-Distill-Qwen-32B',
      'deepseek-ai/DeepSeek-R1-Distill-Llama-70B',
      'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
      'deepseek-ai/DeepSeek-R1-Distill-Llama-8B',
      'Pro/deepseek-ai/DeepSeek-R1',
      'Pro/deepseek-ai/DeepSeek-V3',
      'Pro/deepseek-ai/DeepSeek-V3-1226',
      'Pro/deepseek-ai/DeepSeek-R1-0120',
      'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B',
    ],
  },
  volcengine: {
    name: '火山引擎',
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3/',
    apiUrl: 'https://console.volcengine.com/ark/region:ark+cn-beijing/apiKey',
    models: [],
  },
  deepseek: {
    name: 'DeepSeek',
    baseURL: 'https://api.deepseek.com',
    apiUrl: 'https://platform.deepseek.com/api_keys',
    models: ['deepseek-chat', 'deepseek-reasoner'],
  },
  custom: { name: '自定义', baseURL: '', apiUrl: '', models: [] },
} as const
