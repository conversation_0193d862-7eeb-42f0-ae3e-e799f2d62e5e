import React, { useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import SeamlessVideoPlayerAdapter, { SeamlessVideoPlayerRef } from '@/components/SeamlessVideoPlayerAdapter'
import { VideoItem } from '@/components/SeamlessVideoPlayer'

/**
 * 无缝播放器测试页面
 * 用于测试双缓冲无缝播放功能
 */
export default function SeamlessPlayerTest() {
  const playerRef = useRef<SeamlessVideoPlayerRef>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentText, setCurrentText] = useState('')

  // 测试视频列表（使用示例视频URL）
  const testVideos: VideoItem[] = [
    {
      id: 'test1',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      text: '测试视频1 - <PERSON> Buck <PERSON>'
    },
    {
      id: 'test2', 
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
      text: '测试视频2 - Elephants Dream'
    },
    {
      id: 'test3',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
      text: '测试视频3 - For Bigger Blazes'
    },
    {
      id: 'test4',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
      text: '测试视频4 - For Bigger Escapes'
    }
  ]

  const handlePlaySingle = async (video: VideoItem) => {
    if (!playerRef.current) return
    
    try {
      await playerRef.current.playVideo(video)
      setIsPlaying(true)
      setCurrentText(video.text)
    } catch (error) {
      console.error('播放失败:', error)
    }
  }

  const handlePlayQueue = () => {
    if (!playerRef.current) return
    
    try {
      playerRef.current.setQueue(testVideos)
      playerRef.current.playNext()
      setIsPlaying(true)
    } catch (error) {
      console.error('播放队列失败:', error)
    }
  }

  const handleStop = () => {
    if (!playerRef.current) return
    
    playerRef.current.stop()
    setIsPlaying(false)
    setCurrentText('')
  }

  const handleVideoEnded = (videoId: string) => {
    console.log('视频播放结束:', videoId)
    
    // 检查是否还有更多视频
    if (playerRef.current) {
      const state = playerRef.current.getState()
      if (!state.isPlaying) {
        setIsPlaying(false)
        setCurrentText('')
      }
    }
  }

  const handleVideoError = (error: Error, videoId?: string) => {
    console.error('视频播放错误:', error, videoId)
    setIsPlaying(false)
  }

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      {/* 标题栏 */}
      <div className="bg-gray-800 p-4 border-b border-gray-700">
        <h1 className="text-xl font-bold">无缝播放器测试</h1>
        <p className="text-sm text-gray-400 mt-1">
          测试双缓冲无缝视频播放功能
        </p>
      </div>

      {/* 控制面板 */}
      <div className="bg-gray-800 p-4 border-b border-gray-700">
        <div className="flex flex-wrap gap-2 mb-4">
          <Button
            onClick={handlePlayQueue}
            disabled={isPlaying}
            className="bg-blue-600 hover:bg-blue-700"
          >
            播放队列（无缝测试）
          </Button>
          
          <Button
            onClick={handleStop}
            disabled={!isPlaying}
            variant="destructive"
          >
            停止播放
          </Button>
        </div>

        <div className="grid grid-cols-2 gap-2">
          {testVideos.map((video) => (
            <Button
              key={video.id}
              onClick={() => handlePlaySingle(video)}
              disabled={isPlaying}
              variant="outline"
              className="text-left justify-start"
            >
              {video.text}
            </Button>
          ))}
        </div>

        {/* 状态显示 */}
        <div className="mt-4 p-3 bg-gray-700 rounded">
          <div className="text-sm">
            <div>状态: {isPlaying ? '播放中' : '已停止'}</div>
            <div>当前: {currentText || '无'}</div>
          </div>
        </div>
      </div>

      {/* 播放器区域 */}
      <div className="flex-1 relative bg-black">
        <SeamlessVideoPlayerAdapter
          ref={playerRef}
          className="w-full h-full"
          autoPlay={true}
          muted={false}
          volume={0.5}
          onVideoEnded={handleVideoEnded}
          onError={handleVideoError}
        />

        {/* 播放信息覆盖层 */}
        {currentText && (
          <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-70 text-white p-3 rounded">
            <div className="text-sm font-medium">{currentText}</div>
            <div className="text-xs text-gray-300 mt-1">
              {isPlaying ? '播放中...' : '已暂停'}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
