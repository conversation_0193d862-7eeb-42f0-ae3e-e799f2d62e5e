import { EVENTS, eventEmitter } from '@/utils/events'
import { useMemoizedFn } from 'ahooks'
import { useMemo, useRef } from 'react'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { useShallow } from 'zustand/react/shallow'
import { useAccounts } from './useAccounts'

export interface TimeAnnouncementMessage {
  id: string
  content: string
  enabled: boolean
}

interface AutoTimeAnnouncementConfig {
  scheduler: {
    interval: [number, number] // [最小间隔, 最大间隔]
  }
  messages: TimeAnnouncementMessage[]
  random: boolean
  enabled: boolean
}

interface AutoTimeAnnouncementContext {
  isRunning: boolean
  config: AutoTimeAnnouncementConfig
}

// 默认报时消息列表
const getDefaultMessages = (): TimeAnnouncementMessage[] => [
  {
    id: crypto.randomUUID(),
    content: '现在时间{时间}，新朋友记得点击关注，老朋友记得点赞支持！',
    enabled: true,
  }
]

const defaultContext = (): AutoTimeAnnouncementContext => ({
  isRunning: false,
  config: {
    scheduler: {
      interval: [300000, 600000], // 默认5-10分钟
    },
    messages: getDefaultMessages(),
    random: true,
    enabled: true,
  },
})

interface AutoTimeAnnouncementStore {
  contexts: Record<string, AutoTimeAnnouncementContext>
  setIsRunning: (accountId: string, running: boolean) => void
  setConfig: (accountId: string, config: Partial<AutoTimeAnnouncementConfig>) => void
  setMessages: (accountId: string, messages: TimeAnnouncementMessage[]) => void
  setScheduler: (accountId: string, scheduler: Partial<AutoTimeAnnouncementConfig['scheduler']>) => void
  setRandom: (accountId: string, random: boolean) => void
  setEnabled: (accountId: string, enabled: boolean) => void
}

export const useAutoTimeAnnouncementStore = create<AutoTimeAnnouncementStore>()(
  persist(
    immer(set => {
      eventEmitter.on(EVENTS.ACCOUNT_REMOVED, (accountId: string) => {
        set(state => {
          delete state.contexts[accountId]
        })
      })

      eventEmitter.on(EVENTS.ACCOUNT_ADDED, (accountId: string) => {
        set(state => {
          state.contexts[accountId] = defaultContext()
        })
      })

      const ensureContext = (state: AutoTimeAnnouncementStore, accountId: string) => {
        if (!state.contexts[accountId]) {
          state.contexts[accountId] = defaultContext()
        }
        return state.contexts[accountId]
      }

      return {
        contexts: {},
        setIsRunning: (accountId, running) =>
          set(state => {
            const context = ensureContext(state, accountId)
            context.isRunning = running
          }),
        setConfig: (accountId, newConfig) =>
          set(state => {
            const context = ensureContext(state, accountId)
            Object.assign(context.config, newConfig)
          }),
        setMessages: (accountId, messages) =>
          set(state => {
            const context = ensureContext(state, accountId)
            context.config.messages = messages
          }),
        setScheduler: (accountId, scheduler) =>
          set(state => {
            const context = ensureContext(state, accountId)
            Object.assign(context.config.scheduler, scheduler)
          }),
        setRandom: (accountId, random) =>
          set(state => {
            const context = ensureContext(state, accountId)
            context.config.random = random
          }),
        setEnabled: (accountId, enabled) =>
          set(state => {
            const context = ensureContext(state, accountId)
            context.config.enabled = enabled
          }),
      }
    }),
    {
      name: 'auto-time-announcement-storage',
      version: 1,
      partialize: state => ({
        contexts: Object.fromEntries(
          Object.entries(state.contexts).map(([accountId, context]) =>
            [
              accountId,
              Object.fromEntries(
                Object.entries(context).filter(([key]) => key !== 'isRunning'),
              ),
            ],
          ),
        ),
      }),
    },
  ),
)

export const useAutoTimeAnnouncementActions = () => {
  const setIsRunning = useAutoTimeAnnouncementStore(state => state.setIsRunning)
  const setConfig = useAutoTimeAnnouncementStore(state => state.setConfig)
  const setMessages = useAutoTimeAnnouncementStore(state => state.setMessages)
  const setScheduler = useAutoTimeAnnouncementStore(state => state.setScheduler)
  const setRandom = useAutoTimeAnnouncementStore(state => state.setRandom)
  const setEnabled = useAutoTimeAnnouncementStore(state => state.setEnabled)
  const currentAccountId = useAccounts(state => state.currentAccountId)

  const updateConfig = useMemoizedFn(
    (newConfig: Partial<AutoTimeAnnouncementConfig>) => {
      setConfig(currentAccountId, newConfig)
    },
  )

  const resetToDefaultMessages = useMemoizedFn(() => {
    setMessages(currentAccountId, getDefaultMessages())
  })

  return {
    setIsRunning: useMemoizedFn((running: boolean) =>
      setIsRunning(currentAccountId, running),
    ),
    updateConfig,
    setMessages: useMemoizedFn((messages: TimeAnnouncementMessage[]) =>
      setMessages(currentAccountId, messages),
    ),
    setScheduler: useMemoizedFn((scheduler: Partial<AutoTimeAnnouncementConfig['scheduler']>) =>
      setScheduler(currentAccountId, scheduler),
    ),
    setRandom: useMemoizedFn((random: boolean) =>
      setRandom(currentAccountId, random),
    ),
    setEnabled: useMemoizedFn((enabled: boolean) =>
      setEnabled(currentAccountId, enabled),
    ),
    resetToDefaultMessages,
  }
}

export const useCurrentAutoTimeAnnouncement = <T>(
  getter: (context: AutoTimeAnnouncementContext) => T,
): T => {
  const currentAccountId = useAccounts(state => state.currentAccountId)
  const defaultContextRef = useRef(defaultContext())
  return useAutoTimeAnnouncementStore(
    useShallow(state => {
      const context =
        state.contexts[currentAccountId] ?? defaultContextRef.current
      return getter(context)
    }),
  )
}

// 导出默认消息函数供外部使用
export { getDefaultMessages }
