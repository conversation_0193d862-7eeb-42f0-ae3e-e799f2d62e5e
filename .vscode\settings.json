{
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.tsc.autoDetect": "off",
  "json.schemas": [
    {
      "fileMatch": [
        "/*electron-builder.json5",
        "/*electron-builder.json"
      ],
      "url": "https://json.schemastore.org/electron-builder"
    }
  ],
  // Disable the default formatter, use eslint instead
  "prettier.enable": false,
  "editor.formatOnSave": true,
  // Auto fix
  "editor.codeActionsOnSave": {
    "source.organizeImports.biome": "explicit",
    "quickfix.biome": "explicit"
  },
  "editor.defaultFormatter": "biomejs.biome",
  "biome_lsp.trace.server": "messages",
  "biome.enabled": true,
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "[javascript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[ignore]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "claudeCodeChat.permissions.yoloMode": true
}