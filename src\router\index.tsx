import AutoReply from '@/pages/AutoReply'
import AutoReplySettings from '@/pages/AutoReply/AutoReplySettings'
import LiveControl from '@/pages/LiveControl'
import Settings from '@/pages/SettingsPage'
import DigitalHumanSettingsPage from '@/pages/DigitalHumanSettings'
import OBSPlayerPage from '@/pages/OBSPlayerPage'
import HostPlayerPage from '@/pages/HostPlayerPage'
import AssistantPlayerPage from '@/pages/AssistantPlayerPage'
import AIToolbox from '@/pages/AIToolbox'
import { createHashRouter } from 'react-router'
import App from '../App'
import AutoVoice from '@/pages/AutoVoice'

export const router = createHashRouter([
  {
    path: '/',
    element: <App />,
    children: [
      {
        path: '/',
        element: <LiveControl />,
      },

      {
        path: '/settings',
        element: <Settings />,
      },

      {
        path: 'auto-reply',
        element: <AutoReply />,
      },
      {
        path: '/auto-reply/settings',
        element: <AutoReplySettings />,
      },
      {
        path: '/live-control-settings',
        element: <AutoReplySettings />,
      },
      {
        path: '/auto-voice',
        element: <AutoVoice />,
      },
      {
        path: '/digital-human-settings',
        element: <DigitalHumanSettingsPage />,
      },
      {
        path: '/ai-toolbox',
        element: <AIToolbox />,
      }
    ],
  },
  // 数字人播放器独立页面
  {
    path: '/obs-player',
    element: <OBSPlayerPage />,
  },
  {
    path: '/host-player',
    element: <HostPlayerPage />,
  },
  {
    path: '/assistant-player',
    element: <AssistantPlayerPage />,
  },
])
