import { IPC_CHANNELS } from 'shared/ipcChannels'

type LogLevel = 'info' | 'warn' | 'error' | 'success'

/**
 * 发送日志到主进程，显示在LogDisplayer中
 */
function sendLogToMain(scope: string, level: LogLevel, message: string) {
  if (window.electronAPI) {
    window.electronAPI.invoke(IPC_CHANNELS.sendLog, {
      scope,
      level,
      message,
    })
  }
}

/**
 * 创建带作用域的日志记录器
 */
export function createLogger(scope: string) {
  return {
    info: (message: string) => sendLogToMain(scope, 'info', message),
    warn: (message: string) => sendLogToMain(scope, 'warn', message),
    error: (message: string) => sendLogToMain(scope, 'error', message),
    success: (message: string) => sendLogToMain(scope, 'success', message),
  }
}

/**
 * 默认的自动回复日志记录器
 */
export const autoReplyLogger = createLogger('自动回复')
