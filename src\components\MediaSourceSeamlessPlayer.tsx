import React, { useRef, useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'

export interface VideoItem {
  id: string
  url: string
  text: string
  duration?: number // 视频时长（秒）
}

export interface MediaSourceSeamlessPlayerProps {
  onVideoEnded?: (videoId: string) => void
  onError?: (error: Error, videoId?: string) => void
  onSegmentAdded?: (videoId: string, duration: number) => void
  className?: string
  autoPlay?: boolean
  muted?: boolean
  volume?: number
}

export interface MediaSourceSeamlessPlayerRef {
  playVideo: (video: VideoItem) => Promise<void>
  addToQueue: (video: VideoItem) => void
  setQueue: (videos: VideoItem[]) => void
  playNext: () => Promise<boolean>
  stop: () => void
  getState: () => any
  getCurrentText: () => string
  isPlaying: () => boolean
}

interface PlayerState {
  currentVideoId: string | null
  currentText: string
  isPlaying: boolean
  isBuffering: boolean
  totalDuration: number
  currentTime: number
}

interface VideoSegment {
  id: string
  text: string
  startTime: number
  endTime: number
  data: ArrayBuffer
  isAppended: boolean
}

/**
 * 基于MediaSource的真正无缝视频播放器
 * 通过视频流拼接实现完全无缝的播放体验
 */
export const MediaSourceSeamlessPlayer = forwardRef<MediaSourceSeamlessPlayerRef, MediaSourceSeamlessPlayerProps>(({
  onVideoEnded,
  onError,
  onSegmentAdded,
  className = '',
  autoPlay = true,
  muted = false,
  volume = 1
}, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const mediaSourceRef = useRef<MediaSource | null>(null)
  const sourceBufferRef = useRef<SourceBuffer | null>(null)
  const objectURLRef = useRef<string | null>(null)

  const [state, setState] = useState<PlayerState>({
    currentVideoId: null,
    currentText: '',
    isPlaying: false,
    isBuffering: false,
    totalDuration: 0,
    currentTime: 0
  })

  const [videoQueue, setVideoQueue] = useState<VideoItem[]>([])
  const [videoSegments, setVideoSegments] = useState<VideoSegment[]>([])
  const [pendingSegments, setPendingSegments] = useState<VideoSegment[]>([])
  const [isMediaSourceReady, setIsMediaSourceReady] = useState(false)

  /**
   * 检查MediaSource支持
   */
  const isMediaSourceSupported = useCallback(() => {
    if (!('MediaSource' in window)) {
      return false
    }

    // 只检查最基本的MP4支持
    return MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"')
  }, [])

  /**
   * 获取支持的编解码器
   */
  const getSupportedCodec = useCallback((): string | null => {
    // 简化：只使用最兼容的编解码器
    const codec = 'video/mp4; codecs="avc1.42E01E,mp4a.40.2"'

    if (MediaSource.isTypeSupported(codec)) {
      console.log('使用编解码器:', codec)
      return codec
    }

    console.error('不支持基本的H.264编解码器')
    return null
  }, [])

  /**
   * 处理待处理的片段
   */
  const processPendingSegments = useCallback(() => {
    const sourceBuffer = sourceBufferRef.current
    const mediaSource = mediaSourceRef.current

    if (!sourceBuffer || !mediaSource ||
      sourceBuffer.updating ||
      pendingSegments.length === 0 ||
      mediaSource.readyState !== 'open') {
      return
    }

    const nextSegment = pendingSegments[0]
    if (nextSegment.isAppended) {
      setPendingSegments(prev => prev.slice(1))
      // 递归处理下一个片段
      setTimeout(() => processPendingSegments(), 0)
      return
    }

    console.log('处理待处理片段:', nextSegment.text)

    try {
      // 检查SourceBuffer状态
      if (sourceBuffer.updating) {
        console.log('SourceBuffer正在更新，稍后重试')
        setTimeout(() => processPendingSegments(), 100)
        return
      }

      sourceBuffer.appendBuffer(nextSegment.data)
      nextSegment.isAppended = true

      // 更新总时长
      setState(prev => ({
        ...prev,
        totalDuration: prev.totalDuration + (nextSegment.endTime - nextSegment.startTime)
      }))

      onSegmentAdded?.(nextSegment.id, nextSegment.endTime - nextSegment.startTime)

      // 移除已处理的片段
      setPendingSegments(prev => prev.slice(1))

    } catch (error) {
      console.error('添加片段失败:', error)

      // 移除失败的片段，继续处理下一个
      setPendingSegments(prev => prev.slice(1))
      onError?.(error as Error, nextSegment.id)

      // 尝试处理下一个片段
      setTimeout(() => processPendingSegments(), 100)
    }
  }, [pendingSegments, onSegmentAdded, onError])

  /**
   * 简化的MediaSource初始化
   */
  const initializeMediaSource = useCallback((): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!videoRef.current) {
        reject(new Error('视频元素不存在'))
        return
      }

      if (!isMediaSourceSupported()) {
        reject(new Error('MediaSource不支持'))
        return
      }

      console.log('初始化简化MediaSource...')

      const mediaSource = new MediaSource()
      mediaSourceRef.current = mediaSource

      const objectURL = URL.createObjectURL(mediaSource)
      objectURLRef.current = objectURL
      videoRef.current.src = objectURL

      const onSourceOpen = () => {
        console.log('MediaSource已打开')

        try {
          // 使用固定的编解码器
          const codec = 'video/mp4; codecs="avc1.42E01E,mp4a.40.2"'
          const sourceBuffer = mediaSource.addSourceBuffer(codec)
          sourceBufferRef.current = sourceBuffer

          sourceBuffer.addEventListener('updateend', () => {
            console.log('SourceBuffer更新完成')
            setState(prev => ({ ...prev, isBuffering: false }))

            // 处理待处理的片段
            setTimeout(() => processPendingSegments(), 0)
          })

          sourceBuffer.addEventListener('error', (e) => {
            console.error('SourceBuffer错误:', e)
            onError?.(new Error('SourceBuffer错误'))
          })

          setIsMediaSourceReady(true)
          resolve()

        } catch (error) {
          console.error('创建SourceBuffer失败:', error)
          reject(error)
        }
      }

      const onSourceError = (e: Event) => {
        console.error('MediaSource错误:', e)
        reject(new Error('MediaSource初始化错误'))
      }

      mediaSource.addEventListener('sourceopen', onSourceOpen)
      mediaSource.addEventListener('error', onSourceError)
    })
  }, [isMediaSourceSupported, onError])



  /**
   * 获取视频数据和元信息
   */
  const fetchVideoData = useCallback(async (video: VideoItem): Promise<{ data: ArrayBuffer; duration: number }> => {
    console.log('开始获取视频数据:', video.text)

    try {
      const response = await fetch(video.url, {
        method: 'GET',
        headers: {
          'Range': 'bytes=0-', // 支持范围请求
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      console.log('视频数据获取完成，大小:', arrayBuffer.byteLength)

      // 检查数据完整性
      if (arrayBuffer.byteLength === 0) {
        throw new Error('视频数据为空')
      }

      // 获取视频时长（如果没有提供）
      let duration = video.duration || 0
      if (!duration) {
        duration = await getVideoDuration(video.url)
      }

      return { data: arrayBuffer, duration }
    } catch (error) {
      console.error('获取视频数据失败:', error)
      throw error
    }
  }, [])

  /**
   * 获取视频时长
   */
  const getVideoDuration = useCallback(async (url: string): Promise<number> => {
    return new Promise((resolve, reject) => {
      const tempVideo = document.createElement('video')
      tempVideo.preload = 'metadata'

      const onLoadedMetadata = () => {
        const duration = tempVideo.duration
        tempVideo.removeEventListener('loadedmetadata', onLoadedMetadata)
        tempVideo.removeEventListener('error', onError)
        tempVideo.src = ''
        resolve(duration)
      }

      const onError = () => {
        tempVideo.removeEventListener('loadedmetadata', onLoadedMetadata)
        tempVideo.removeEventListener('error', onError)
        tempVideo.src = ''
        reject(new Error('获取视频时长失败'))
      }

      tempVideo.addEventListener('loadedmetadata', onLoadedMetadata)
      tempVideo.addEventListener('error', onError)
      tempVideo.src = url
    })
  }, [])



  /**
   * 添加视频片段到流
   */
  const addVideoSegment = useCallback(async (video: VideoItem): Promise<void> => {
    console.log('添加视频片段到流:', video.text)

    try {
      // 获取视频数据和时长
      const { data, duration } = await fetchVideoData(video)

      // 计算片段的时间范围
      const startTime = state.totalDuration
      const endTime = startTime + duration

      const segment: VideoSegment = {
        id: video.id,
        text: video.text,
        startTime,
        endTime,
        data,
        isAppended: false
      }

      // 添加到片段列表
      setVideoSegments(prev => [...prev, segment])

      // 如果MediaSource准备就绪且SourceBuffer不在更新中，立即处理
      if (isMediaSourceReady && sourceBufferRef.current && !sourceBufferRef.current.updating) {
        setPendingSegments(prev => [...prev, segment])
        processPendingSegments()
      } else {
        // 否则添加到待处理队列
        setPendingSegments(prev => [...prev, segment])
      }

      console.log('视频片段添加完成:', video.text, `时长: ${duration}s`)
    } catch (error) {
      console.error('添加视频片段失败:', video.text, error)
      onError?.(error as Error, video.id)
    }
  }, [state.totalDuration, isMediaSourceReady, fetchVideoData, processPendingSegments, onError])

  /**
   * 开始播放流
   */
  const startPlayback = useCallback(async (): Promise<void> => {
    if (!videoRef.current) {
      throw new Error('视频元素不存在')
    }

    console.log('开始播放流')

    try {
      // 如果MediaSource还没初始化，先初始化
      if (!isMediaSourceReady) {
        await initializeMediaSource()
      }

      // 开始播放
      await videoRef.current.play()

      setState(prev => ({ ...prev, isPlaying: true }))
      console.log('流播放开始')
    } catch (error) {
      console.error('开始播放失败:', error)
      throw error
    }
  }, [isMediaSourceReady, initializeMediaSource])

  /**
   * 获取当前播放的视频信息
   */
  const getCurrentVideoInfo = useCallback((): { id: string; text: string } | null => {
    if (!videoRef.current || videoSegments.length === 0) {
      return null
    }

    const currentTime = videoRef.current.currentTime

    // 找到当前时间对应的视频片段
    for (const segment of videoSegments) {
      if (currentTime >= segment.startTime && currentTime < segment.endTime) {
        return { id: segment.id, text: segment.text }
      }
    }

    return null
  }, [videoSegments])

  /**
   * 智能清理SourceBuffer中的旧数据
   */
  const cleanupOldSegments = useCallback(() => {
    const sourceBuffer = sourceBufferRef.current
    const video = videoRef.current
    const mediaSource = mediaSourceRef.current

    if (!sourceBuffer || !video || !mediaSource ||
      sourceBuffer.updating ||
      mediaSource.readyState !== 'open') {
      return
    }

    try {
      const currentTime = video.currentTime

      if (!sourceBuffer.buffered || sourceBuffer.buffered.length === 0) {
        return
      }

      const bufferStart = sourceBuffer.buffered.start(0)
      const bufferEnd = sourceBuffer.buffered.end(sourceBuffer.buffered.length - 1)
      const totalBuffered = bufferEnd - bufferStart

      // 智能清理策略
      const BUFFER_AHEAD_TIME = 10 // 保留当前时间前10秒
      const MAX_BUFFER_SIZE = 120   // 最大缓冲120秒
      const CLEANUP_THRESHOLD = 60  // 当缓冲超过60秒时开始清理

      // 如果总缓冲时间超过阈值，进行清理
      if (totalBuffered > CLEANUP_THRESHOLD) {
        try {
          const removeEnd = Math.max(bufferStart, currentTime - BUFFER_AHEAD_TIME)

          if (removeEnd > bufferStart) {
            console.log(`智能清理旧片段: ${bufferStart.toFixed(2)}s - ${removeEnd.toFixed(2)}s`)
            sourceBuffer.remove(bufferStart, removeEnd)
          }
        } catch (error) {
          console.warn('清理旧片段失败:', error)
        }
      }

      // 如果缓冲区太大，清理更多
      if (totalBuffered > MAX_BUFFER_SIZE) {
        try {
          const aggressiveRemoveEnd = Math.max(bufferStart, currentTime - 5) // 只保留5秒
          if (aggressiveRemoveEnd > bufferStart) {
            console.log(`激进清理旧片段: ${bufferStart.toFixed(2)}s - ${aggressiveRemoveEnd.toFixed(2)}s`)
            sourceBuffer.remove(bufferStart, aggressiveRemoveEnd)
          }
        } catch (error) {
          console.warn('激进清理失败:', error)
        }
      }
    } catch (error) {
      console.warn('清理SourceBuffer失败:', error)
    }
  }, [])

  /**
   * 监控SourceBuffer状态
   */
  const monitorSourceBufferHealth = useCallback(() => {
    const sourceBuffer = sourceBufferRef.current
    const video = videoRef.current
    const mediaSource = mediaSourceRef.current

    if (!sourceBuffer || !video || !mediaSource || mediaSource.readyState !== 'open') {
      return null
    }

    try {
      const currentTime = video.currentTime
      let totalBuffered = 0
      let bufferedRanges = []

      // 安全地访问buffered属性
      if (sourceBuffer.buffered) {
        for (let i = 0; i < sourceBuffer.buffered.length; i++) {
          const start = sourceBuffer.buffered.start(i)
          const end = sourceBuffer.buffered.end(i)
          totalBuffered += (end - start)
          bufferedRanges.push({ start, end })
        }
      }

      // 检查缓冲区健康状况
      const isHealthy = totalBuffered < 180 && !sourceBuffer.updating

      if (!isHealthy) {
        console.warn('SourceBuffer健康状况不佳:', {
          totalBuffered: totalBuffered.toFixed(2),
          updating: sourceBuffer.updating,
          ranges: bufferedRanges.length,
          currentTime: currentTime.toFixed(2)
        })
      }

      return {
        totalBuffered,
        bufferedRanges,
        isHealthy,
        updating: sourceBuffer.updating
      }
    } catch (error) {
      console.warn('监控SourceBuffer状态失败:', error)
      return null
    }
  }, [])

  /**
   * 播放指定视频（添加到流中）
   */
  const playVideo = useCallback(async (video: VideoItem): Promise<void> => {
    console.log('播放视频（添加到流）:', video.text)

    try {
      // 如果是第一个视频，初始化MediaSource并开始播放
      if (videoSegments.length === 0 && !isMediaSourceReady) {
        console.log('初始化MediaSource并播放第一个视频')
        await initializeMediaSource()
        await addVideoSegment(video)
        await startPlayback()

        // 更新当前视频信息
        setState(prev => ({
          ...prev,
          currentVideoId: video.id,
          currentText: video.text
        }))
      } else {
        // 后续视频直接添加到流中，实现无缝拼接
        console.log('添加视频到现有流中')
        await addVideoSegment(video)
      }

      console.log('视频添加到流完成:', video.text)
    } catch (error) {
      console.error('播放视频失败:', error)
      onError?.(error as Error, video.id)
      throw error
    }
  }, [videoSegments.length, isMediaSourceReady, initializeMediaSource, addVideoSegment, startPlayback, onError])

  /**
   * 添加视频到播放队列
   */
  const addToQueue = useCallback((video: VideoItem) => {
    setVideoQueue(prev => [...prev, video])
  }, [])

  /**
   * 设置播放队列并开始播放
   */
  const setQueue = useCallback(async (videos: VideoItem[]) => {
    console.log('设置播放队列，共', videos.length, '个视频')

    // 清理现有状态
    setVideoQueue([])
    setVideoSegments([])
    setPendingSegments([])
    setState(prev => ({
      ...prev,
      totalDuration: 0,
      currentTime: 0,
      currentVideoId: null,
      currentText: '',
      isPlaying: false
    }))

    if (videos.length === 0) {
      return
    }

    try {
      // 初始化MediaSource
      await initializeMediaSource()

      // 添加所有视频到流中
      for (const video of videos) {
        await addVideoSegment(video)
      }

      // 开始播放
      await startPlayback()

      // 设置第一个视频为当前视频
      setState(prev => ({
        ...prev,
        currentVideoId: videos[0].id,
        currentText: videos[0].text
      }))

      console.log('播放队列设置完成，开始无缝播放')
    } catch (error) {
      console.error('设置播放队列失败:', error)
      onError?.(error as Error)
    }
  }, [initializeMediaSource, addVideoSegment, startPlayback, onError])

  /**
   * 播放队列中的下一个视频
   */
  const playNext = useCallback(async (): Promise<boolean> => {
    if (videoQueue.length === 0) {
      console.log('播放队列为空')
      setState(prev => ({ ...prev, isPlaying: false }))
      return false
    }

    const nextVideo = videoQueue[0]
    setVideoQueue(prev => prev.slice(1))

    try {
      // 添加下一个视频到流中
      await addVideoSegment(nextVideo)
      return true
    } catch (error) {
      console.error('播放下一个视频失败:', error)
      return false
    }
  }, [videoQueue, addVideoSegment])

  // 监听视频播放事件
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleTimeUpdate = () => {
      const currentTime = video.currentTime
      setState(prev => ({ ...prev, currentTime }))

      // 更新当前播放的视频信息
      const currentVideoInfo = getCurrentVideoInfo()
      if (currentVideoInfo && currentVideoInfo.id !== state.currentVideoId) {
        setState(prev => ({
          ...prev,
          currentVideoId: currentVideoInfo.id,
          currentText: currentVideoInfo.text
        }))

        // 触发视频切换事件
        if (state.currentVideoId) {
          onVideoEnded?.(state.currentVideoId)
        }
      }

      // 定期清理旧的缓冲区数据和监控健康状况
      if (Math.floor(currentTime) % 10 === 0) {
        const healthStatus = monitorSourceBufferHealth()

        // 如果缓冲区不健康，进行清理
        if (!healthStatus?.isHealthy) {
          cleanupOldSegments()
        }
      }

      // 更频繁的轻量级清理
      if (Math.floor(currentTime) % 30 === 0) {
        cleanupOldSegments()
      }
    }

    const handleEnded = () => {
      console.log('整个播放列表播放完成')
      setState(prev => ({ ...prev, isPlaying: false }))

      if (state.currentVideoId) {
        onVideoEnded?.(state.currentVideoId)
      }
    }

    const handleError = (error: Event) => {
      console.error('视频播放出错:', error)
      setState(prev => ({ ...prev, isPlaying: false }))
      onError?.(new Error('视频播放出错'))
    }

    const handleLoadStart = () => {
      console.log('开始加载视频流')
    }

    const handleCanPlay = () => {
      console.log('视频流可以播放')
    }

    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('ended', handleEnded)
    video.addEventListener('error', handleError)
    video.addEventListener('loadstart', handleLoadStart)
    video.addEventListener('canplay', handleCanPlay)

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('ended', handleEnded)
      video.removeEventListener('error', handleError)
      video.removeEventListener('loadstart', handleLoadStart)
      video.removeEventListener('canplay', handleCanPlay)
    }
  }, [getCurrentVideoInfo, state.currentVideoId, onVideoEnded, cleanupOldSegments, onError])

  // 设置视频属性
  useEffect(() => {
    const video = videoRef.current
    if (video) {
      video.muted = muted
      video.volume = volume
      video.autoplay = autoPlay
    }
  }, [muted, volume, autoPlay])

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    playVideo,
    addToQueue,
    setQueue,
    playNext,
    stop: () => {
      const video = videoRef.current
      if (video) {
        video.pause()
        video.currentTime = 0
      }

      // 清理MediaSource
      if (mediaSourceRef.current) {
        try {
          if (mediaSourceRef.current.readyState === 'open') {
            mediaSourceRef.current.endOfStream()
          }
        } catch (error) {
          console.warn('结束MediaSource失败:', error)
        }
      }

      // 清理URL对象
      if (objectURLRef.current) {
        URL.revokeObjectURL(objectURLRef.current)
        objectURLRef.current = null
      }

      // 重置状态
      setState({
        currentVideoId: null,
        currentText: '',
        isPlaying: false,
        isBuffering: false,
        totalDuration: 0,
        currentTime: 0
      })
      setVideoQueue([])
      setVideoSegments([])
      setPendingSegments([])
      setIsMediaSourceReady(false)

      // 重置引用
      mediaSourceRef.current = null
      sourceBufferRef.current = null
    },
    getState: () => state,
    getCurrentText: () => state.currentText,
    isPlaying: () => state.isPlaying
  }), [playVideo, addToQueue, setQueue, playNext, state])

  // 清理资源
  useEffect(() => {
    return () => {
      // 清理MediaSource
      if (mediaSourceRef.current) {
        try {
          if (mediaSourceRef.current.readyState === 'open') {
            mediaSourceRef.current.endOfStream()
          }
        } catch (error) {
          console.warn('清理MediaSource失败:', error)
        }
      }

      // 清理URL对象
      if (objectURLRef.current) {
        URL.revokeObjectURL(objectURLRef.current)
      }
    }
  }, [])

  // 检查浏览器支持
  if (!isMediaSourceSupported()) {
    return (
      <div className={`relative w-full h-full ${className} flex items-center justify-center bg-gray-800`}>
        <div className="text-white text-center">
          <div className="text-lg font-semibold mb-2">MediaSource不支持</div>
          <div className="text-sm text-gray-300">
            当前浏览器不支持MediaSource API<br />
            请使用现代浏览器 (Chrome 23+, Firefox 42+, Safari 8+)
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative w-full h-full ${className}`}>
      <video
        ref={videoRef}
        className="w-full h-full object-contain bg-black"
        autoPlay={autoPlay}
        muted={muted}
        playsInline
        controls={false}
      />

      {/* 缓冲指示器 */}
      {state.isBuffering && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
            <div className="text-white text-sm">缓冲中...</div>
          </div>
        </div>
      )}

      {/* 播放状态指示器 */}
      {state.isPlaying && (
        <div className="absolute top-4 right-4 bg-green-600 text-white text-xs px-2 py-1 rounded">
          流播放中
        </div>
      )}

      {/* 进度信息 */}
      {state.totalDuration > 0 && (
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white text-xs p-2 rounded">
          <div>总时长: {Math.round(state.totalDuration)}s</div>
          <div>当前时间: {Math.round(state.currentTime)}s</div>
          <div>进度: {Math.round((state.currentTime / state.totalDuration) * 100)}%</div>
        </div>
      )}

      {/* 调试信息（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs p-2 rounded">
          <div>视频ID: {state.currentVideoId}</div>
          <div>当前文本: {state.currentText}</div>
          <div>队列长度: {videoQueue.length}</div>
          <div>视频片段: {videoSegments.length}</div>
          <div>待处理: {pendingSegments.length}</div>
          <div>缓冲中: {state.isBuffering ? '是' : '否'}</div>
          <div>MediaSource: {mediaSourceRef.current?.readyState || '未初始化'}</div>
          <div>SourceBuffer: {sourceBufferRef.current ? '已创建' : '未创建'}</div>
          <div>总时长: {Math.round(state.totalDuration)}s</div>
          <div>当前时间: {Math.round(state.currentTime)}s</div>
        </div>
      )}
    </div>
  )
})

MediaSourceSeamlessPlayer.displayName = 'MediaSourceSeamlessPlayer'

export default MediaSourceSeamlessPlayer
