import React, { useRef, useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'

export interface VideoItem {
  id: string
  url: string
  text: string
}

export interface UltraSeamlessVideoPlayerProps {
  onVideoEnded?: (videoId: string) => void
  onError?: (error: Error, videoId?: string) => void
  className?: string
  autoPlay?: boolean
  muted?: boolean
  volume?: number
}

export interface UltraSeamlessVideoPlayerRef {
  playVideo: (video: VideoItem) => Promise<void>
  addToQueue: (video: VideoItem) => void
  setQueue: (videos: VideoItem[]) => void
  playNext: () => Promise<boolean>
  stop: () => void
  getState: () => any
  getCurrentText: () => string
  isPlaying: () => boolean
}

interface PlayerState {
  currentVideoId: string | null
  currentText: string
  isPlaying: boolean
  activePlayer: 'A' | 'B'
  nextPlayer: 'A' | 'B'
}

/**
 * 超级无缝视频播放器
 * 使用多重优化技术消除所有可能的卡顿
 */
export const UltraSeamlessVideoPlayer = forwardRef<UltraSeamlessVideoPlayerRef, UltraSeamlessVideoPlayerProps>(({
  onVideoEnded,
  onError,
  className = '',
  autoPlay = true,
  muted = false,
  volume = 1
}, ref) => {
  // 多个视频元素用于更好的缓冲
  const videoARef = useRef<HTMLVideoElement>(null)
  const videoBRef = useRef<HTMLVideoElement>(null)
  const videoCRef = useRef<HTMLVideoElement>(null) // 第三个视频元素用于预缓冲
  
  const [state, setState] = useState<PlayerState>({
    currentVideoId: null,
    currentText: '',
    isPlaying: false,
    activePlayer: 'A',
    nextPlayer: 'B'
  })

  const [videoQueue, setVideoQueue] = useState<VideoItem[]>([])
  const [preloadedVideos, setPreloadedVideos] = useState<Map<string, HTMLVideoElement>>(new Map())
  const [isTransitioning, setIsTransitioning] = useState(false)
  
  // 优化的切换参数
  const PRELOAD_ADVANCE_MS = 3000 // 提前3秒预加载
  const SWITCH_ADVANCE_MS = 100   // 提前100ms开始切换准备
  const BUFFER_SAFETY_MS = 200    // 缓冲安全边距

  /**
   * 获取指定播放器的视频元素
   */
  const getVideoElement = useCallback((player: 'A' | 'B' | 'C'): HTMLVideoElement | null => {
    switch (player) {
      case 'A': return videoARef.current
      case 'B': return videoBRef.current
      case 'C': return videoCRef.current
      default: return null
    }
  }, [])

  /**
   * 获取当前活跃的视频元素
   */
  const getActiveVideo = useCallback((): HTMLVideoElement | null => {
    return getVideoElement(state.activePlayer)
  }, [state.activePlayer, getVideoElement])

  /**
   * 获取下一个视频元素
   */
  const getNextVideo = useCallback((): HTMLVideoElement | null => {
    return getVideoElement(state.nextPlayer)
  }, [state.nextPlayer, getVideoElement])

  /**
   * 高级预加载 - 确保视频完全准备就绪
   */
  const advancedPreload = useCallback(async (video: VideoItem, targetElement: HTMLVideoElement): Promise<boolean> => {
    return new Promise((resolve) => {
      console.log(`高级预加载开始: ${video.text}`)
      
      let isResolved = false
      const resolveOnce = (result: boolean) => {
        if (!isResolved) {
          isResolved = true
          resolve(result)
        }
      }

      // 多重检查确保视频真正准备就绪
      const checkReadiness = () => {
        const isReady = targetElement.readyState >= 3 && // HAVE_FUTURE_DATA
                       targetElement.buffered.length > 0 &&
                       targetElement.buffered.end(0) > 1 // 至少缓冲1秒
        
        if (isReady) {
          console.log(`视频预加载完成并缓冲充足: ${video.text}`)
          setPreloadedVideos(prev => new Map(prev).set(video.id, targetElement))
          resolveOnce(true)
        }
      }

      // 监听多个事件确保完全加载
      const onCanPlayThrough = () => {
        console.log(`canplaythrough事件: ${video.text}`)
        setTimeout(checkReadiness, 100) // 稍等一下再检查
      }

      const onLoadedData = () => {
        console.log(`loadeddata事件: ${video.text}`)
        setTimeout(checkReadiness, 50)
      }

      const onProgress = () => {
        checkReadiness()
      }

      const onError = () => {
        console.error(`预加载失败: ${video.text}`)
        cleanup()
        resolveOnce(false)
      }

      const cleanup = () => {
        targetElement.removeEventListener('canplaythrough', onCanPlayThrough)
        targetElement.removeEventListener('loadeddata', onLoadedData)
        targetElement.removeEventListener('progress', onProgress)
        targetElement.removeEventListener('error', onError)
      }

      // 添加事件监听
      targetElement.addEventListener('canplaythrough', onCanPlayThrough)
      targetElement.addEventListener('loadeddata', onLoadedData)
      targetElement.addEventListener('progress', onProgress)
      targetElement.addEventListener('error', onError)
      
      // 设置视频属性
      targetElement.preload = 'auto'
      targetElement.muted = muted
      targetElement.volume = volume
      
      // 开始加载
      targetElement.src = video.url
      targetElement.load()
      
      // 超时处理
      setTimeout(() => {
        if (!isResolved) {
          console.warn(`预加载超时: ${video.text}`)
          cleanup()
          resolveOnce(false)
        }
      }, 15000) // 15秒超时
    })
  }, [muted, volume])

  /**
   * 智能切换播放器
   */
  const smartSwitch = useCallback(async (nextVideo: VideoItem): Promise<void> => {
    if (isTransitioning) {
      console.log('正在切换中，跳过')
      return
    }

    setIsTransitioning(true)
    console.log(`开始智能切换到: ${nextVideo.text}`)

    try {
      const nextVideoElement = getNextVideo()
      if (!nextVideoElement) {
        throw new Error('下一个视频元素不可用')
      }

      // 确保下一个视频已经预加载并准备就绪
      const isPreloaded = preloadedVideos.has(nextVideo.id)
      if (!isPreloaded) {
        console.log('视频未预加载，立即加载')
        await advancedPreload(nextVideo, nextVideoElement)
      }

      // 确保视频真正准备播放
      if (nextVideoElement.readyState < 3) {
        console.log('等待视频准备就绪')
        await new Promise<void>((resolve, reject) => {
          const onReady = () => {
            nextVideoElement.removeEventListener('canplaythrough', onReady)
            resolve()
          }
          nextVideoElement.addEventListener('canplaythrough', onReady)
          
          setTimeout(() => {
            nextVideoElement.removeEventListener('canplaythrough', onReady)
            reject(new Error('视频准备超时'))
          }, 3000)
        })
      }

      // 同步播放 - 在当前视频即将结束时开始播放下一个
      nextVideoElement.currentTime = 0
      await nextVideoElement.play()

      // 立即切换显示
      setState(prev => ({
        ...prev,
        activePlayer: prev.nextPlayer,
        nextPlayer: prev.activePlayer,
        currentVideoId: nextVideo.id,
        currentText: nextVideo.text
      }))

      // 从队列中移除
      setVideoQueue(prev => prev.slice(1))

      console.log(`智能切换完成: ${nextVideo.text}`)
      
      // 触发结束事件
      setTimeout(() => {
        onVideoEnded?.(nextVideo.id)
      }, 0)

    } catch (error) {
      console.error('智能切换失败:', error)
      onError?.(error as Error, nextVideo.id)
    } finally {
      setIsTransitioning(false)
    }
  }, [isTransitioning, getNextVideo, preloadedVideos, advancedPreload, onVideoEnded, onError])

  /**
   * 播放指定视频
   */
  const playVideo = useCallback(async (video: VideoItem): Promise<void> => {
    const activeVideo = getActiveVideo()
    if (!activeVideo) {
      throw new Error('没有可用的视频元素')
    }

    console.log(`开始播放视频: ${video.text}`)

    try {
      // 高级预加载
      await advancedPreload(video, activeVideo)
      
      // 开始播放
      await activeVideo.play()

      // 更新状态
      setState(prev => ({
        ...prev,
        currentVideoId: video.id,
        currentText: video.text,
        isPlaying: true
      }))

      console.log(`视频开始播放: ${video.text}`)
    } catch (error) {
      console.error('播放视频失败:', error)
      onError?.(error as Error, video.id)
      throw error
    }
  }, [getActiveVideo, advancedPreload, onError])

  /**
   * 添加视频到播放队列
   */
  const addToQueue = useCallback((video: VideoItem) => {
    setVideoQueue(prev => [...prev, video])
  }, [])

  /**
   * 设置播放队列
   */
  const setQueue = useCallback((videos: VideoItem[]) => {
    setVideoQueue(videos)
    setPreloadedVideos(new Map())
  }, [])

  /**
   * 播放队列中的下一个视频
   */
  const playNext = useCallback(async (): Promise<boolean> => {
    if (videoQueue.length === 0) {
      console.log('播放队列为空')
      setState(prev => ({ ...prev, isPlaying: false }))
      return false
    }

    const nextVideo = videoQueue[0]

    try {
      await smartSwitch(nextVideo)
      return true
    } catch (error) {
      console.error('播放下一个视频失败:', error)
      return false
    }
  }, [videoQueue, smartSwitch])

  // 智能预加载和切换监听
  useEffect(() => {
    const activeVideo = getActiveVideo()
    if (!activeVideo || !state.isPlaying) return

    const handleTimeUpdate = () => {
      const currentTime = activeVideo.currentTime
      const duration = activeVideo.duration
      
      if (!duration || currentTime <= 0) return

      const remainingTime = (duration - currentTime) * 1000

      // 提前预加载下一个视频
      if (remainingTime <= PRELOAD_ADVANCE_MS && videoQueue.length > 0) {
        const nextVideo = videoQueue[0]
        const nextVideoElement = getNextVideo()
        
        if (nextVideoElement && !preloadedVideos.has(nextVideo.id)) {
          console.log(`提前预加载: ${nextVideo.text}`)
          advancedPreload(nextVideo, nextVideoElement)
        }
      }

      // 智能切换时机
      if (remainingTime <= SWITCH_ADVANCE_MS && videoQueue.length > 0 && !isTransitioning) {
        const nextVideo = videoQueue[0]
        if (preloadedVideos.has(nextVideo.id)) {
          console.log(`触发智能切换: ${nextVideo.text}`)
          smartSwitch(nextVideo)
        }
      }
    }

    const handleEnded = () => {
      console.log('视频自然结束')
      if (!isTransitioning && videoQueue.length > 0) {
        playNext()
      } else if (videoQueue.length === 0) {
        setState(prev => ({ ...prev, isPlaying: false }))
      }
    }

    activeVideo.addEventListener('timeupdate', handleTimeUpdate)
    activeVideo.addEventListener('ended', handleEnded)

    return () => {
      activeVideo.removeEventListener('timeupdate', handleTimeUpdate)
      activeVideo.removeEventListener('ended', handleEnded)
    }
  }, [getActiveVideo, state.isPlaying, videoQueue, getNextVideo, preloadedVideos, advancedPreload, isTransitioning, smartSwitch, playNext])

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    playVideo,
    addToQueue,
    setQueue,
    playNext,
    stop: () => {
      [videoARef.current, videoBRef.current, videoCRef.current].forEach(video => {
        if (video) {
          video.pause()
          video.src = ''
        }
      })
      setState(prev => ({ ...prev, isPlaying: false }))
      setVideoQueue([])
      setPreloadedVideos(new Map())
      setIsTransitioning(false)
    },
    getState: () => state,
    getCurrentText: () => state.currentText,
    isPlaying: () => state.isPlaying
  }), [playVideo, addToQueue, setQueue, playNext, state])

  return (
    <div className={`relative w-full h-full ${className}`}>
      {/* 视频元素A */}
      <video
        ref={videoARef}
        className={`absolute inset-0 w-full h-full object-contain will-change-transform ${
          state.activePlayer === 'A' ? 'z-20' : 'z-0'
        }`}
        style={{
          transform: 'translate3d(0,0,0)', // 强制硬件加速
          opacity: state.activePlayer === 'A' ? 1 : 0,
          transition: 'opacity 16ms linear', // 一帧的时间
          backfaceVisibility: 'hidden'
        }}
        muted={muted}
        autoPlay={autoPlay}
        playsInline
        preload="auto"
      />
      
      {/* 视频元素B */}
      <video
        ref={videoBRef}
        className={`absolute inset-0 w-full h-full object-contain will-change-transform ${
          state.activePlayer === 'B' ? 'z-20' : 'z-0'
        }`}
        style={{
          transform: 'translate3d(0,0,0)', // 强制硬件加速
          opacity: state.activePlayer === 'B' ? 1 : 0,
          transition: 'opacity 16ms linear', // 一帧的时间
          backfaceVisibility: 'hidden'
        }}
        muted={muted}
        autoPlay={autoPlay}
        playsInline
        preload="auto"
      />

      {/* 隐藏的预缓冲视频元素C */}
      <video
        ref={videoCRef}
        className="hidden"
        muted={muted}
        autoPlay={false}
        playsInline
        preload="auto"
      />

      {/* 切换指示器 */}
      {isTransitioning && (
        <div className="absolute top-4 right-4 bg-green-600 text-white text-xs px-2 py-1 rounded animate-pulse">
          切换中
        </div>
      )}

      {/* 调试信息（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs p-2 rounded">
          <div>当前播放器: {state.activePlayer}</div>
          <div>视频ID: {state.currentVideoId}</div>
          <div>队列长度: {videoQueue.length}</div>
          <div>预加载: {preloadedVideos.size}</div>
          <div>切换中: {isTransitioning ? '是' : '否'}</div>
        </div>
      )}
    </div>
  )
})

UltraSeamlessVideoPlayer.displayName = 'UltraSeamlessVideoPlayer'

export default UltraSeamlessVideoPlayer
