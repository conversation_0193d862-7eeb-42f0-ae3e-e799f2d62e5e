import { merge } from 'lodash-es'
import type { Page } from 'playwright'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { createLogger } from '#/logger'
import { randomInt, sleep, takeScreenshot } from '#/utils'
import windowManager from '#/windowManager'
import type { BaseConfig } from './scheduler'
import { TaskScheduler } from './scheduler'

const TASK_NAME = '自动报时'

interface TimeAnnouncementMessage {
  id: string
  content: string
  enabled: boolean
}

export interface AutoTimeAnnouncementConfig extends BaseConfig {
  messages: TimeAnnouncementMessage[]
  random?: boolean
  enabled?: boolean
}

export class AutoTimeAnnouncementTask {
  private currentMessageIndex = -1
  private config: AutoTimeAnnouncementConfig
  private readonly scheduler: TaskScheduler
  private logger: ReturnType<typeof createLogger>
  private abortController = new AbortController()

  constructor(
    private readonly page: Page,
    private account: Account,
    userConfig: AutoTimeAnnouncementConfig,
  ) {
    this.logger = createLogger(`${TASK_NAME} @${account.name}`)
    this.validateConfig(userConfig)
    this.config = userConfig
    this.scheduler = this.createTaskScheduler()
  }

  private validateConfig(config: AutoTimeAnnouncementConfig) {
    if (!config.enabled) {
      throw new Error('自动报时功能未启用')
    }
    
    const enabledMessages = config.messages.filter(msg => msg.enabled && msg.content.trim())
    if (enabledMessages.length === 0) {
      throw new Error('没有可用的报时消息')
    }
  }

  private createTaskScheduler() {
    return new TaskScheduler(
      TASK_NAME,
      (...args) => this.execute(...args),
      merge({}, this.config.scheduler, {
        onStart: () => this.logger.info('任务开始执行'),
        onStop: () => {
          this.logger.info('任务停止执行')
          this.abortController.abort()
          windowManager.send(
            IPC_CHANNELS.tasks.autoTimeAnnouncement.stoppedEvent,
            this.account.id,
          )
        },
      }),
      this.logger,
    )
  }

  private getNextMessage(): TimeAnnouncementMessage {
    const enabledMessages = this.config.messages.filter(msg => msg.enabled && msg.content.trim())
    
    if (enabledMessages.length === 0) {
      throw new Error('没有可用的报时消息')
    }

    if (this.config.random) {
      const randomIndex = randomInt(0, enabledMessages.length - 1)
      return enabledMessages[randomIndex]
    } else {
      this.currentMessageIndex = (this.currentMessageIndex + 1) % enabledMessages.length
      return enabledMessages[this.currentMessageIndex]
    }
  }

  private formatTimeMessage(content: string): string {
    const now = new Date()
    const timeString = now.toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
    })
    
    return content.replace(/\{时间\}/g, timeString)
  }

  private async execute(screenshot = false) {
    try {
      const message = this.getNextMessage()
      const formattedContent = this.formatTimeMessage(message.content)

      this.logger.info(`发送报时消息: ${formattedContent}`)

      // 发送到语音输出列表
      windowManager.send(IPC_CHANNELS.autoVoice.addTimeAnnouncement, {
        id: crypto.randomUUID(),
        text: formattedContent,
        type: 'time-announcement',
        timestamp: Date.now(),
      })

    } catch (error) {
      this.logger.error(
        `执行失败: ${error instanceof Error ? error.message : String(error)}`,
      )
      if (screenshot) {
        await takeScreenshot(this.page, TASK_NAME, this.account.name).catch(e =>
          this.logger.debug(`截图失败：${e}`),
        )
      }
      throw error
    }
  }

  get isRunning() {
    return this.scheduler.isRunning
  }

  start() {
    this.scheduler.start()
  }

  stop() {
    this.scheduler.stop()
  }

  updateConfig(newConfig: Partial<AutoTimeAnnouncementConfig>) {
    this.config = merge(this.config, newConfig)
    if (newConfig.scheduler) {
      this.scheduler.updateConfig({ scheduler: newConfig.scheduler })
    }
  }
}
