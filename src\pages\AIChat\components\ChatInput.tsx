import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { useAIChatStore } from '@/hooks/useAIChat'
import { useForbiddenWords } from '@/hooks/useForbiddenWords'
import { useToast } from '@/hooks/useToast'
import { messagesToContext } from '@/lib/utils'
import { getForbiddenWordsInText } from '@/utils/filter'
import { useMemoizedFn } from 'ahooks'
import { SendIcon, CheckCircle, XCircle } from 'lucide-react'
import { useState } from 'react'

export default function ChatInput({
  onSubmit,
}: {
  onSubmit: (messages: { role: string; content: string }[]) => void
}) {
  const [input, setInput] = useState('')
  const { status, addMessage, messages, config, apiKeys } = useAIChatStore()
  const { forbiddenWords } = useForbiddenWords()
  const { toast } = useToast()

  // 检查输入文本中的违禁词
  const forbiddenWordsInInput = getForbiddenWordsInText(input, forbiddenWords)
  const hasForbiddenWords = forbiddenWordsInInput.length > 0

  const handleSubmit = useMemoizedFn(async () => {
    if (!apiKeys[config.provider]) {
      toast.error('请先配置 API Key')
      return
    }
    if (!input.trim() || status !== 'ready') return

    // 检查违禁词
    if (hasForbiddenWords) {
      toast.error(`提示包含违禁词：${forbiddenWordsInInput.join('、')}`)
      return
    }

    const userMessage = input.trim()
    setInput('')
    addMessage({ role: 'user', content: userMessage })
    const contextMessages = messagesToContext(messages, userMessage, config.systemPrompt)
    onSubmit(contextMessages)
  })

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  return (
    <>
      <div className="relative flex-1">
        <Textarea
          value={input}
          onChange={e => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="输入消息，按 Enter 发送..."
          className={`resize-none w-full min-h-[56px] max-h-[200px] bg-muted/50 focus:bg-background transition-colors pr-10 ${hasForbiddenWords ? 'border-red-500 focus:border-red-500' : ''
            }`}
          rows={3}
        />
        {/* 违禁词检查图标 */}
        {input.trim() && (
          <div
            className="absolute top-2 right-2 flex items-center"
            title={hasForbiddenWords ? `包含违禁词：${forbiddenWordsInInput.join('、')}` : "内容安全"}
          >
            {hasForbiddenWords ? (
              <XCircle className="h-4 w-4 text-red-500" />
            ) : (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
          </div>
        )}
      </div>
      <Button
        size="icon"
        className="px-8 h-auto bg-primary hover:bg-primary/90"
        onClick={handleSubmit}
        disabled={!input.trim() || status !== 'ready' || hasForbiddenWords}
      >
        <SendIcon className="h-5 w-5" />
      </Button>
    </>
  )
}
