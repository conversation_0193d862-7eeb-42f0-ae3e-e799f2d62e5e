import React, { useState, useCallback, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/useToast'
import { Upload, FileVideo, Scissors } from 'lucide-react'
import { cn } from '@/lib/utils'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { useComfyUIBasePath } from '@/hooks/useVoiceSettings'

interface VideoCutDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
}

interface VideoFile {
    file: File
    name: string
    size: number
    duration?: number
}

interface CutOptions {
    mode: 'time-range' | 'by-minutes' | 'by-segments' | 'by-cut-points'
    startTime?: number
    endTime?: number
    segmentDuration?: number
    segmentCount?: number
    cutPoints?: number[]
}

interface VideoInfo {
    duration?: number
    filename?: string
    error?: string
}

interface SaveDialogResult {
    canceled: boolean
    filePath?: string
}

interface CopyFileResult {
    success: boolean
    error?: string
}

export default function VideoCutDialog({ open, onOpenChange }: VideoCutDialogProps) {
    const [videoFile, setVideoFile] = useState<VideoFile | null>(null)
    const [cutOptions, setCutOptions] = useState<CutOptions>({ mode: 'by-segments', segmentCount: 4 })
    const [isProcessing, setIsProcessing] = useState(false)
    const [progress, setProgress] = useState(0)
    const [isDragOver, setIsDragOver] = useState(false)
    const fileInputRef = useRef<HTMLInputElement>(null)
    const { toast } = useToast()
    const comfyUIBasePath = useComfyUIBasePath()

    // 调试：检查ComfyUI路径
    React.useEffect(() => {
        console.log('当前ComfyUI基础路径:', comfyUIBasePath)
    }, [comfyUIBasePath])

    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        setIsDragOver(true)
    }, [])

    const handleDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        setIsDragOver(false)
    }, [])

    const handleDrop = useCallback(async (e: React.DragEvent) => {
        e.preventDefault()
        setIsDragOver(false)

        const files = Array.from(e.dataTransfer.files)
        const videoFile = files.find(file => file.type.startsWith('video/'))

        if (videoFile) {
            const newVideoFile = {
                file: videoFile,
                name: videoFile.name,
                size: videoFile.size
            }
            setVideoFile(newVideoFile)

            // 获取视频时长
            try {
                const arrayBuffer = await videoFile.arrayBuffer()
                const uint8Array = new Uint8Array(arrayBuffer)
                const tempFileName = `temp_info_${Date.now()}_${videoFile.name}`
                await window.ipcRenderer.invoke(IPC_CHANNELS.video.writeFile as any, tempFileName, uint8Array)

                const videoInfo = await window.ipcRenderer.invoke(IPC_CHANNELS.video.getInfo as any, tempFileName) as VideoInfo
                if (!videoInfo.error && videoInfo.duration) {
                    setVideoFile(prev => prev ? { ...prev, duration: videoInfo.duration } : null)
                }
            } catch (error) {
                console.warn('获取视频时长失败:', error)
            }
        } else {
            toast.error('请选择视频文件')
        }
    }, [toast])

    const handleFileSelect = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0]
        if (file && file.type.startsWith('video/')) {
            const newVideoFile = {
                file: file,
                name: file.name,
                size: file.size
            }
            setVideoFile(newVideoFile)

            // 获取视频时长
            try {
                const arrayBuffer = await file.arrayBuffer()
                const uint8Array = new Uint8Array(arrayBuffer)
                const tempFileName = `temp_info_${Date.now()}_${file.name}`
                await window.ipcRenderer.invoke(IPC_CHANNELS.video.writeFile as any, tempFileName, uint8Array)

                const videoInfo = await window.ipcRenderer.invoke(IPC_CHANNELS.video.getInfo as any, tempFileName) as VideoInfo
                if (!videoInfo.error && videoInfo.duration) {
                    setVideoFile(prev => prev ? { ...prev, duration: videoInfo.duration } : null)
                }
            } catch (error) {
                console.warn('获取视频时长失败:', error)
            }
        } else {
            toast.error('请选择视频文件')
        }
    }, [toast])

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const formatTime = (seconds: number) => {
        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        const secs = Math.floor(seconds % 60)
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }

    // 时间轴拖动组件
    const TimelineSlider = ({ duration, startTime, endTime, onTimeChange, onCutPointsChange, initialCutPoints = [] }: {
        duration: number
        startTime: number
        endTime: number
        onTimeChange: (start: number, end: number) => void
        onCutPointsChange?: (cutPoints: number[]) => void
        initialCutPoints?: number[]
    }) => {
        const [isDragging, setIsDragging] = useState<'start' | 'end' | null>(null)
        const [cutPoints, setCutPoints] = useState<number[]>(initialCutPoints)
        const sliderRef = useRef<HTMLDivElement>(null)
        const [dragStartX, setDragStartX] = useState(0)
        const [dragStartTime, setDragStartTime] = useState(0)
        const animationFrameRef = useRef<number | null>(null)

        const handleMouseDown = (type: 'start' | 'end') => (e: React.MouseEvent) => {
            e.preventDefault()
            e.stopPropagation()

            if (!sliderRef.current) return

            const rect = sliderRef.current.getBoundingClientRect()
            setDragStartX(e.clientX - rect.left)
            setDragStartTime(type === 'start' ? startTime : endTime)
            setIsDragging(type)

            // 添加鼠标样式
            document.body.style.cursor = 'grabbing'
            document.body.style.userSelect = 'none'
        }

        const handleMouseMove = useCallback((e: MouseEvent) => {
            if (!isDragging || !sliderRef.current) return

            e.preventDefault()

            // 取消之前的动画帧
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current)
            }

            // 使用 requestAnimationFrame 来优化性能
            animationFrameRef.current = requestAnimationFrame(() => {
                if (!sliderRef.current) return

                const rect = sliderRef.current.getBoundingClientRect()
                const x = Math.max(0, Math.min(rect.width, e.clientX - rect.left))
                const percentage = x / rect.width
                const time = Math.round(percentage * duration * 10) / 10 // 精确到0.1秒

                if (isDragging === 'start') {
                    const newStartTime = Math.max(0, Math.min(time, endTime - 0.1))
                    onTimeChange(newStartTime, endTime)
                } else if (isDragging === 'end') {
                    const newEndTime = Math.min(duration, Math.max(time, startTime + 0.1))
                    onTimeChange(startTime, newEndTime)
                }
            })
        }, [isDragging, duration, startTime, endTime, onTimeChange])

        const handleMouseUp = useCallback(() => {
            setIsDragging(null)
            // 恢复鼠标样式
            document.body.style.cursor = ''
            document.body.style.userSelect = ''
        }, [])

        // 双击添加切割点
        const handleDoubleClick = useCallback((e: React.MouseEvent) => {
            e.preventDefault()
            e.stopPropagation()

            if (!sliderRef.current) return

            const rect = sliderRef.current.getBoundingClientRect()
            const x = e.clientX - rect.left
            const percentage = Math.max(0, Math.min(1, x / rect.width))
            const time = percentage * duration

            // 检查是否点击在现有切割点附近（15像素范围内）
            const clickThreshold = 15 / rect.width * duration
            const existingPointIndex = cutPoints.findIndex(point =>
                Math.abs(point - time) < clickThreshold
            )

            if (existingPointIndex !== -1) {
                // 移除现有切割点
                const newCutPoints = cutPoints.filter((_, index) => index !== existingPointIndex)
                setCutPoints(newCutPoints)
                onCutPointsChange?.(newCutPoints)
            } else {
                // 添加新切割点，确保不在开始和结束位置
                if (time > 1 && time < duration - 1) {
                    const newCutPoints = [...cutPoints, time].sort((a, b) => a - b)
                    setCutPoints(newCutPoints)
                    onCutPointsChange?.(newCutPoints)
                }
            }
        }, [duration, cutPoints, onCutPointsChange])

        // 点击时间轴设置开始或结束时间
        const [clickTimeout, setClickTimeout] = useState<NodeJS.Timeout | null>(null)

        const handleTimelineClick = useCallback((e: React.MouseEvent) => {
            if (!sliderRef.current || isDragging) return

            // 延迟处理单击，以便双击可以取消单击
            if (clickTimeout) {
                clearTimeout(clickTimeout)
                setClickTimeout(null)
                return
            }

            const timeout = setTimeout(() => {
                const rect = sliderRef.current?.getBoundingClientRect()
                if (!rect) return

                const x = e.clientX - rect.left
                const percentage = Math.max(0, Math.min(1, x / rect.width))
                const time = percentage * duration

                // 判断点击位置更接近开始时间还是结束时间
                const distanceToStart = Math.abs(time - startTime)
                const distanceToEnd = Math.abs(time - endTime)

                if (distanceToStart < distanceToEnd) {
                    // 更接近开始时间，设置开始时间
                    const newStartTime = Math.max(0, Math.min(time, endTime - 1))
                    onTimeChange(newStartTime, endTime)
                } else {
                    // 更接近结束时间，设置结束时间
                    const newEndTime = Math.min(duration, Math.max(time, startTime + 1))
                    onTimeChange(startTime, newEndTime)
                }
                setClickTimeout(null)
            }, 200)

            setClickTimeout(timeout)
        }, [duration, startTime, endTime, onTimeChange, isDragging, clickTimeout])

        // 同步初始切割点
        React.useEffect(() => {
            setCutPoints(initialCutPoints)
        }, [initialCutPoints])

        // 清理点击超时和动画帧
        React.useEffect(() => {
            return () => {
                if (clickTimeout) {
                    clearTimeout(clickTimeout)
                }
                if (animationFrameRef.current) {
                    cancelAnimationFrame(animationFrameRef.current)
                }
            }
        }, [clickTimeout])

        React.useEffect(() => {
            if (isDragging) {
                document.addEventListener('mousemove', handleMouseMove)
                document.addEventListener('mouseup', handleMouseUp)
                return () => {
                    document.removeEventListener('mousemove', handleMouseMove)
                    document.removeEventListener('mouseup', handleMouseUp)
                }
            }
        }, [isDragging, handleMouseMove, handleMouseUp])

        const startPercentage = (startTime / duration) * 100
        const endPercentage = (endTime / duration) * 100

        return (
            <div className="space-y-3">
                <div className="flex justify-between text-sm text-muted-foreground">
                    <span>开始: {formatTime(startTime)}</span>
                    <span>结束: {formatTime(endTime)}</span>
                    <span>时长: {formatTime(endTime - startTime)}</span>
                </div>

                {/* 操作提示 */}
                <div className="text-xs text-muted-foreground">
                    拖动圆点调整时间 • 单击时间轴快速设置 • 双击添加/移除切割点
                </div>

                <div
                    ref={sliderRef}
                    className="relative h-8 bg-muted rounded cursor-pointer select-none"
                    onClick={handleTimelineClick}
                    onDoubleClick={handleDoubleClick}
                >
                    {/* 时间轴背景 */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-100 to-blue-200 rounded" />

                    {/* 切割点标记 */}
                    {cutPoints.map((point, index) => {
                        const pointPercentage = (point / duration) * 100
                        return (
                            <div
                                key={index}
                                className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10"
                                style={{ left: `${pointPercentage}%` }}
                                title={`切割点: ${formatTime(point)}`}
                            />
                        )
                    })}

                    {/* 选中区域 */}
                    <div
                        className="absolute top-1 bottom-1 bg-primary/40 border-l-2 border-r-2 border-primary rounded-sm"
                        style={{
                            left: `${startPercentage}%`,
                            width: `${endPercentage - startPercentage}%`
                        }}
                    />

                    {/* 开始时间拖拽点 */}
                    <div
                        className="absolute top-1/2 w-5 h-5 bg-primary border-2 border-white rounded-full cursor-grab active:cursor-grabbing transform -translate-y-1/2 -translate-x-1/2 shadow-lg hover:scale-110 transition-transform z-20"
                        style={{ left: `${startPercentage}%` }}
                        onMouseDown={handleMouseDown('start')}
                        title={`开始时间: ${formatTime(startTime)}`}
                    />

                    {/* 结束时间拖拽点 */}
                    <div
                        className="absolute top-1/2 w-5 h-5 bg-primary border-2 border-white rounded-full cursor-grab active:cursor-grabbing transform -translate-y-1/2 translate-x-1/2 shadow-lg hover:scale-110 transition-transform z-20"
                        style={{ left: `${endPercentage}%` }}
                        onMouseDown={handleMouseDown('end')}
                        title={`结束时间: ${formatTime(endTime)}`}
                    />
                </div>

                {/* 时间刻度 */}
                <div className="relative h-4">
                    {Array.from({ length: 11 }, (_, i) => {
                        const time = (duration * i) / 10
                        return (
                            <div
                                key={i}
                                className="absolute text-xs text-muted-foreground transform -translate-x-1/2"
                                style={{ left: `${i * 10}%` }}
                            >
                                {formatTime(time)}
                            </div>
                        )
                    })}
                </div>

                {/* 切割点列表 */}
                {cutPoints.length > 0 && (
                    <div className="space-y-2">
                        <div className="text-sm font-medium">切割点:</div>
                        <div className="flex flex-wrap gap-2">
                            {cutPoints.map((point, index) => (
                                <div
                                    key={index}
                                    className="px-2 py-1 bg-red-100 text-red-700 rounded text-xs cursor-pointer hover:bg-red-200"
                                    onClick={() => {
                                        const newCutPoints = cutPoints.filter((_, i) => i !== index)
                                        setCutPoints(newCutPoints)
                                        onCutPointsChange?.(newCutPoints)
                                    }}
                                    title="点击移除此切割点"
                                >
                                    {formatTime(point)}
                                </div>
                            ))}
                            <button
                                className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs hover:bg-gray-200"
                                onClick={() => {
                                    setCutPoints([])
                                    onCutPointsChange?.([])
                                }}
                            >
                                清除所有
                            </button>
                        </div>
                    </div>
                )}
            </div>
        )
    }

    const parseTimeInput = (timeStr: string): number => {
        const parts = timeStr.split(':').map(Number)
        if (parts.length === 3) {
            return parts[0] * 3600 + parts[1] * 60 + parts[2]
        } else if (parts.length === 2) {
            return parts[0] * 60 + parts[1]
        } else if (parts.length === 1) {
            return parts[0]
        }
        return 0
    }

    const handleStartCut = async () => {
        if (!videoFile) {
            toast.error('请选择视频文件')
            return
        }

        setIsProcessing(true)
        setProgress(0)

        try {
            // 设置ComfyUI路径到后端
            console.log('前端ComfyUI路径:', comfyUIBasePath)
            await window.ipcRenderer.invoke(IPC_CHANNELS.video.setComfyUIPath as any, comfyUIBasePath)
            console.log('ComfyUI路径已发送到后端')

            // 首先检查FFmpeg是否可用
            const ffmpegAvailable = await window.ipcRenderer.invoke(IPC_CHANNELS.video.checkFFmpeg as any)
            if (!ffmpegAvailable) {
                throw new Error('FFmpeg不可用，请确保已安装FFmpeg')
            }

            // 将文件写入临时目录
            const arrayBuffer = await videoFile.file.arrayBuffer()
            const uint8Array = new Uint8Array(arrayBuffer)

            const tempFileName = `temp_${Date.now()}_${videoFile.name}`
            await window.ipcRenderer.invoke(IPC_CHANNELS.video.writeFile as any, tempFileName, uint8Array)

            // 根据切割模式执行不同的切割逻辑
            if (cutOptions.mode === 'time-range' && cutOptions.startTime !== undefined && cutOptions.endTime !== undefined) {
                const outputFilename = `cut_${Date.now()}_${videoFile.name}`
                const result = await window.ipcRenderer.invoke(IPC_CHANNELS.video.cutSegment as any, {
                    originalFilename: tempFileName,
                    startTime: cutOptions.startTime,
                    endTime: cutOptions.endTime,
                    outputFilename
                })

                if (result.success) {
                    // 弹出保存对话框
                    const saveResult = await window.ipcRenderer.invoke(IPC_CHANNELS.dialog.showSaveDialog as any, {
                        defaultPath: `切割_${videoFile.name}`,
                        filters: [
                            { name: '视频文件', extensions: ['mp4', 'avi', 'mov', 'mkv'] },
                            { name: '所有文件', extensions: ['*'] }
                        ]
                    }) as unknown as SaveDialogResult

                    if (!saveResult.canceled && saveResult.filePath) {
                        // 复制文件到用户选择的位置
                        const resultData = result as any
                        // 使用后端返回的完整路径，如果没有则构建路径
                        const sourcePath = resultData.outputPath ||
                            (resultData.outputFilename.includes('\\') || resultData.outputFilename.includes('/')
                                ? resultData.outputFilename
                                : `${comfyUIBasePath}\\input\\${resultData.outputFilename}`)
                        // 检查源文件是否存在
                        const fileExists = await window.ipcRenderer.invoke(IPC_CHANNELS.fs.exists as any, sourcePath)
                        console.log(`源文件是否存在: ${sourcePath} -> ${fileExists}`)

                        if (!fileExists) {
                            throw new Error(`切割后的文件不存在: ${sourcePath}`)
                        }

                        const copyResult = await window.ipcRenderer.invoke(IPC_CHANNELS.fs.copyFile as any,
                            sourcePath,
                            saveResult.filePath
                        ) as unknown as CopyFileResult

                        if (copyResult.success) {
                            toast.success(`视频已成功切割并保存到 ${saveResult.filePath}`)
                        } else {
                            throw new Error('保存文件失败')
                        }
                    } else {
                        toast.success('视频已切割完成，但未保存到本地')
                    }
                } else {
                    const resultData = result as any
                    throw new Error(resultData.error || '切割失败')
                }
            } else if (cutOptions.mode === 'by-minutes' && cutOptions.segmentDuration) {
                // 按分钟切割的逻辑需要先获取视频信息
                const videoInfo = await window.ipcRenderer.invoke(IPC_CHANNELS.video.getInfo as any, tempFileName) as VideoInfo
                if (videoInfo.error) {
                    throw new Error('无法获取视频信息')
                }

                // 获取视频时长
                const totalDuration = videoInfo.duration!
                if (!totalDuration) {
                    throw new Error('无法获取视频时长')
                }
                const segmentCount = Math.ceil(totalDuration / cutOptions.segmentDuration)

                // 弹出保存对话框选择保存目录
                const saveResult = await window.ipcRenderer.invoke(IPC_CHANNELS.dialog.showSaveDialog as any, {
                    defaultPath: `视频切割片段`,
                    properties: ['openDirectory', 'createDirectory'],
                    buttonLabel: '选择保存目录'
                }) as unknown as SaveDialogResult

                if (saveResult.canceled || !saveResult.filePath) {
                    toast.error('用户取消了保存操作')
                    return
                }

                const saveDirectory = saveResult.filePath
                const results = []

                // 按分钟切割
                for (let i = 0; i < segmentCount; i++) {
                    const startTime = i * cutOptions.segmentDuration
                    const endTime = Math.min((i + 1) * cutOptions.segmentDuration, totalDuration)
                    const outputFilename = `segment_${i + 1}_${Date.now()}.mp4`

                    setProgress((i / segmentCount) * 80) // 80%用于切割进度

                    const result = await window.ipcRenderer.invoke(IPC_CHANNELS.video.cutSegment as any, {
                        originalFilename: tempFileName,
                        startTime,
                        endTime,
                        outputFilename
                    })

                    const resultData = result as any
                    if (resultData.success) {
                        // 复制到用户选择的目录
                        const finalPath = `${saveDirectory}\\片段_${i + 1}.mp4`
                        // 使用后端返回的完整路径，如果没有则构建路径
                        const sourcePath = resultData.outputPath ||
                            (resultData.outputFilename.includes('\\') || resultData.outputFilename.includes('/')
                                ? resultData.outputFilename
                                : `${comfyUIBasePath}\\input\\${resultData.outputFilename}`)
                        // 检查源文件是否存在
                        const fileExists = await window.ipcRenderer.invoke(IPC_CHANNELS.fs.exists as any, sourcePath)
                        console.log(`源文件是否存在: ${sourcePath} -> ${fileExists}`)

                        if (!fileExists) {
                            console.error(`源文件不存在: ${sourcePath}`)
                            continue // 跳过这个文件，继续处理下一个
                        }

                        const copyResult = await window.ipcRenderer.invoke(IPC_CHANNELS.fs.copyFile as any,
                            sourcePath,
                            finalPath
                        ) as unknown as CopyFileResult

                        if (copyResult.success) {
                            results.push(finalPath)
                        } else {
                            console.error(`复制失败: ${copyResult.error}`)
                        }
                    }
                }

                setProgress(100)

                if (results.length > 0) {
                    toast.success(`成功切割 ${results.length} 个片段到 ${saveDirectory}`)
                } else {
                    throw new Error('切割失败，没有生成任何片段')
                }
            } else if (cutOptions.mode === 'by-segments' && cutOptions.segmentCount) {
                // 按段数切割的逻辑
                const videoInfo = await window.ipcRenderer.invoke(IPC_CHANNELS.video.getInfo as any, tempFileName) as VideoInfo
                if (videoInfo.error) {
                    throw new Error('无法获取视频信息')
                }

                // 获取视频时长
                const totalDuration = videoInfo.duration!
                if (!totalDuration) {
                    throw new Error('无法获取视频时长')
                }
                const segmentDuration = totalDuration / cutOptions.segmentCount

                // 弹出保存对话框选择保存目录
                const saveResult = await window.ipcRenderer.invoke(IPC_CHANNELS.dialog.showSaveDialog as any, {
                    defaultPath: `视频切割片段`,
                    properties: ['openDirectory', 'createDirectory'],
                    buttonLabel: '选择保存目录'
                }) as unknown as SaveDialogResult

                if (saveResult.canceled || !saveResult.filePath) {
                    toast.error('用户取消了保存操作')
                    return
                }

                const saveDirectory = saveResult.filePath
                const results = []

                // 按段数切割
                for (let i = 0; i < cutOptions.segmentCount; i++) {
                    const startTime = i * segmentDuration
                    const endTime = Math.min((i + 1) * segmentDuration, totalDuration)
                    const outputFilename = `segment_${i + 1}_${Date.now()}.mp4`

                    setProgress((i / cutOptions.segmentCount) * 80) // 80%用于切割进度

                    const result = await window.ipcRenderer.invoke(IPC_CHANNELS.video.cutSegment as any, {
                        originalFilename: tempFileName,
                        startTime,
                        endTime,
                        outputFilename
                    })

                    const resultData = result as any
                    if (resultData.success) {
                        // 复制到用户选择的目录
                        const finalPath = `${saveDirectory}\\片段_${i + 1}.mp4`
                        // 使用后端返回的完整路径，如果没有则构建路径
                        const sourcePath = resultData.outputPath ||
                            (resultData.outputFilename.includes('\\') || resultData.outputFilename.includes('/')
                                ? resultData.outputFilename
                                : `${comfyUIBasePath}\\input\\${resultData.outputFilename}`)
                        // 检查源文件是否存在
                        const fileExists = await window.ipcRenderer.invoke(IPC_CHANNELS.fs.exists as any, sourcePath)
                        console.log(`源文件是否存在: ${sourcePath} -> ${fileExists}`)

                        if (!fileExists) {
                            console.error(`源文件不存在: ${sourcePath}`)
                            continue // 跳过这个文件，继续处理下一个
                        }

                        const copyResult = await window.ipcRenderer.invoke(IPC_CHANNELS.fs.copyFile as any,
                            sourcePath,
                            finalPath
                        ) as unknown as CopyFileResult

                        if (copyResult.success) {
                            results.push(finalPath)
                        } else {
                            console.error(`复制失败: ${copyResult.error}`)
                        }
                    }
                }

                setProgress(100)

                if (results.length > 0) {
                    toast.success(`成功切割 ${results.length} 个片段到 ${saveDirectory}`)
                } else {
                    throw new Error('切割失败，没有生成任何片段')
                }
            } else if (cutOptions.mode === 'by-cut-points') {
                if (!cutOptions.cutPoints || cutOptions.cutPoints.length === 0) {
                    throw new Error('请先添加切割点。双击时间轴可以添加切割点。')
                }
                // 按切割点切割的逻辑
                const videoInfo = await window.ipcRenderer.invoke(IPC_CHANNELS.video.getInfo as any, tempFileName) as VideoInfo
                if (videoInfo.error) {
                    throw new Error('无法获取视频信息')
                }

                // 获取视频时长
                const totalDuration = videoInfo.duration!
                if (!totalDuration) {
                    throw new Error('无法获取视频时长')
                }

                // 弹出保存对话框选择保存目录
                const saveResult = await window.ipcRenderer.invoke(IPC_CHANNELS.dialog.showSaveDialog as any, {
                    defaultPath: `视频切割片段`,
                    properties: ['openDirectory', 'createDirectory'],
                    buttonLabel: '选择保存目录'
                }) as unknown as SaveDialogResult

                if (saveResult.canceled || !saveResult.filePath) {
                    toast.error('用户取消了保存操作')
                    return
                }

                const saveDirectory = saveResult.filePath
                const results = []

                // 构建切割时间段：从0开始，到每个切割点，最后到视频结束
                const timeSegments: { start: number; end: number }[] = []
                const sortedCutPoints = [...cutOptions.cutPoints].sort((a, b) => a - b)

                console.log('切割点:', sortedCutPoints)
                console.log('视频总时长:', totalDuration)

                if (sortedCutPoints.length === 0) {
                    throw new Error('没有设置切割点')
                }

                // 第一段：从开始到第一个切割点
                if (sortedCutPoints[0] > 1) { // 至少1秒的片段
                    timeSegments.push({ start: 0, end: sortedCutPoints[0] })
                }

                // 中间段：从每个切割点到下一个切割点
                for (let i = 0; i < sortedCutPoints.length - 1; i++) {
                    const start = sortedCutPoints[i]
                    const end = sortedCutPoints[i + 1]
                    if (end - start > 1) { // 至少1秒的片段
                        timeSegments.push({ start, end })
                    }
                }

                // 最后一段：从最后一个切割点到结束
                const lastCutPoint = sortedCutPoints[sortedCutPoints.length - 1]
                if (totalDuration - lastCutPoint > 1) { // 至少1秒的片段
                    timeSegments.push({ start: lastCutPoint, end: totalDuration })
                }

                console.log('生成的时间段:', timeSegments)

                if (timeSegments.length === 0) {
                    throw new Error('没有生成有效的时间段，请检查切割点设置')
                }

                // 按切割点切割
                for (let i = 0; i < timeSegments.length; i++) {
                    const { start, end } = timeSegments[i]
                    const outputFilename = `cutpoint_${i + 1}_${Date.now()}.mp4`

                    console.log(`切割片段 ${i + 1}: ${start}s - ${end}s`)
                    setProgress((i / timeSegments.length) * 80) // 80%用于切割进度

                    // 确保每次切割前路径都是正确的
                    await window.ipcRenderer.invoke(IPC_CHANNELS.video.setComfyUIPath as any, comfyUIBasePath)

                    const result = await window.ipcRenderer.invoke(IPC_CHANNELS.video.cutSegment as any, {
                        originalFilename: tempFileName,
                        startTime: start,
                        endTime: end,
                        outputFilename
                    })

                    console.log(`切割结果 ${i + 1}:`, result)
                    const resultData = result as any
                    if (resultData.success) {
                        // 复制到用户选择的目录
                        const finalPath = `${saveDirectory}\\片段_${i + 1}_${formatTime(start)}-${formatTime(end)}.mp4`
                        // 使用后端返回的完整路径，如果没有则构建路径
                        const sourcePath = resultData.outputPath ||
                            (resultData.outputFilename.includes('\\') || resultData.outputFilename.includes('/')
                                ? resultData.outputFilename
                                : `${comfyUIBasePath}\\input\\${resultData.outputFilename}`)

                        console.log(`复制文件: ${sourcePath} -> ${finalPath}`)

                        // 检查源文件是否存在
                        const fileExists = await window.ipcRenderer.invoke(IPC_CHANNELS.fs.exists as any, sourcePath)
                        console.log(`源文件是否存在: ${sourcePath} -> ${fileExists}`)

                        if (!fileExists) {
                            console.error(`源文件不存在: ${sourcePath}`)
                            continue // 跳过这个文件，继续处理下一个
                        }

                        const copyResult = await window.ipcRenderer.invoke(IPC_CHANNELS.fs.copyFile as any,
                            sourcePath,
                            finalPath
                        ) as unknown as CopyFileResult

                        console.log(`复制结果 ${i + 1}:`, copyResult)
                        if (copyResult.success) {
                            results.push(finalPath)
                        } else {
                            console.error(`复制失败 ${i + 1}:`, copyResult.error)
                        }
                    } else {
                        console.error(`切割失败 ${i + 1}:`, resultData.error)
                    }
                }

                setProgress(100)

                if (results.length > 0) {
                    toast.success(`成功按切割点切割 ${results.length} 个片段到 ${saveDirectory}`)
                } else {
                    throw new Error('切割失败，没有生成任何片段')
                }
            }

            setProgress(100)
        } catch (error) {
            console.error('视频切割失败:', error)
            toast.error(error instanceof Error ? error.message : '未知错误')
        } finally {
            setIsProcessing(false)
        }
    }

    const handleReset = () => {
        setVideoFile(null)
        setCutOptions({ mode: 'by-segments', segmentCount: 4 })
        setProgress(0)
        if (fileInputRef.current) {
            fileInputRef.current.value = ''
        }
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-2xl">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Scissors className="w-5 h-5" />
                        视频切割工具
                    </DialogTitle>
                    <DialogDescription>
                        上传视频文件，选择切割方式，快速分割视频
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                    {/* 文件上传区域 */}
                    <div className="space-y-4">
                        <Label>选择视频文件</Label>
                        {!videoFile ? (
                            <Card
                                className={cn(
                                    'border-2 border-dashed transition-colors cursor-pointer',
                                    isDragOver ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'
                                )}
                                onDragOver={handleDragOver}
                                onDragLeave={handleDragLeave}
                                onDrop={handleDrop}
                                onClick={() => fileInputRef.current?.click()}
                            >
                                <CardContent className="flex flex-col items-center justify-center py-8">
                                    <Upload className="w-8 h-8 text-muted-foreground mb-2" />
                                    <p className="text-sm text-muted-foreground text-center">
                                        拖拽视频文件到此处，或点击选择文件
                                    </p>
                                    <p className="text-xs text-muted-foreground mt-1">
                                        支持 MP4, AVI, MOV, MKV 等格式
                                    </p>
                                </CardContent>
                            </Card>
                        ) : (
                            <Card>
                                <CardContent className="flex items-center gap-3 py-4">
                                    <FileVideo className="w-8 h-8 text-primary" />
                                    <div className="flex-1">
                                        <p className="font-medium">{videoFile.name}</p>
                                        <p className="text-sm text-muted-foreground">
                                            {formatFileSize(videoFile.size)}
                                        </p>
                                    </div>
                                    <Button variant="outline" size="sm" onClick={handleReset}>
                                        重新选择
                                    </Button>
                                </CardContent>
                            </Card>
                        )}
                        <input
                            ref={fileInputRef}
                            type="file"
                            accept="video/*"
                            onChange={handleFileSelect}
                            className="hidden"
                        />
                    </div>

                    {/* 切割选项 */}
                    {videoFile && (
                        <div className="space-y-4">
                            <Label>切割方式</Label>
                            <Select
                                value={cutOptions.mode}
                                onValueChange={(value: CutOptions['mode']) =>
                                    setCutOptions({ mode: value })
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    {/* <SelectItem value="time-range">按时间段切割</SelectItem>
                                    <SelectItem value="by-cut-points">按切割点切割</SelectItem> */}
                                    <SelectItem value="by-minutes">按分钟切割</SelectItem>
                                    <SelectItem value="by-segments">按段数切割</SelectItem>
                                </SelectContent>
                            </Select>

                            {/* 时间段切割选项 */}
                            {cutOptions.mode === 'time-range' && (
                                <div className="space-y-4">
                                    {videoFile?.duration ? (
                                        <div className="space-y-4">
                                            <Label>拖动选择时间段</Label>
                                            <TimelineSlider
                                                duration={videoFile.duration}
                                                startTime={cutOptions.startTime || 0}
                                                endTime={cutOptions.endTime || Math.min(60, videoFile.duration)}
                                                initialCutPoints={cutOptions.cutPoints || []}
                                                onTimeChange={(start, end) => {
                                                    setCutOptions(prev => ({
                                                        ...prev,
                                                        startTime: start,
                                                        endTime: end
                                                    }))
                                                }}
                                                onCutPointsChange={(cutPoints) => {
                                                    setCutOptions(prev => ({
                                                        ...prev,
                                                        cutPoints
                                                    }))
                                                }}
                                            />
                                        </div>
                                    ) : (
                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label>开始时间 (HH:MM:SS)</Label>
                                                <Input
                                                    placeholder="00:00:00"
                                                    onChange={(e) =>
                                                        setCutOptions(prev => ({
                                                            ...prev,
                                                            startTime: parseTimeInput(e.target.value)
                                                        }))
                                                    }
                                                />
                                            </div>
                                            <div className="space-y-2">
                                                <Label>结束时间 (HH:MM:SS)</Label>
                                                <Input
                                                    placeholder="00:01:00"
                                                    onChange={(e) =>
                                                        setCutOptions(prev => ({
                                                            ...prev,
                                                            endTime: parseTimeInput(e.target.value)
                                                        }))
                                                    }
                                                />
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* 按切割点切割选项 */}
                            {cutOptions.mode === 'by-cut-points' && (
                                <div className="space-y-4">
                                    {videoFile?.duration ? (
                                        <div className="space-y-4">
                                            <Label>双击时间轴添加切割点</Label>
                                            <TimelineSlider
                                                duration={videoFile.duration}
                                                startTime={0}
                                                endTime={videoFile.duration}
                                                initialCutPoints={cutOptions.cutPoints || []}
                                                onTimeChange={() => { }} // 在切割点模式下不需要时间范围
                                                onCutPointsChange={(cutPoints) => {
                                                    setCutOptions(prev => ({
                                                        ...prev,
                                                        cutPoints
                                                    }))
                                                }}
                                            />
                                            {(!cutOptions.cutPoints || cutOptions.cutPoints.length === 0) && (
                                                <div className="text-sm text-muted-foreground">
                                                    双击时间轴添加切割点，视频将在这些点处分割
                                                </div>
                                            )}
                                        </div>
                                    ) : (
                                        <div className="text-sm text-muted-foreground">
                                            请先上传视频文件以使用切割点功能
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* 按分钟切割选项 */}
                            {cutOptions.mode === 'by-minutes' && (
                                <div className="space-y-2">
                                    <Label>每段时长 (分钟)</Label>
                                    <Input
                                        type="number"
                                        placeholder="5"
                                        min="1"
                                        onChange={(e) =>
                                            setCutOptions(prev => ({
                                                ...prev,
                                                segmentDuration: parseInt(e.target.value) * 60
                                            }))
                                        }
                                    />
                                </div>
                            )}

                            {/* 按段数切割选项 */}
                            {cutOptions.mode === 'by-segments' && (
                                <div className="space-y-2">
                                    <Label>分割段数</Label>
                                    <Input
                                        type="number"
                                        placeholder="4"
                                        min="2"
                                        onChange={(e) =>
                                            setCutOptions(prev => ({
                                                ...prev,
                                                segmentCount: parseInt(e.target.value)
                                            }))
                                        }
                                    />
                                </div>
                            )}
                        </div>
                    )}

                    {/* 进度条 */}
                    {isProcessing && (
                        <div className="space-y-2">
                            <Label>处理进度</Label>
                            <Progress value={progress} className="w-full" />
                            <p className="text-sm text-muted-foreground text-center">
                                正在处理视频，请稍候...
                            </p>
                        </div>
                    )}
                </div>

                <DialogFooter>
                    <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isProcessing}>
                        取消
                    </Button>
                    <Button
                        onClick={handleStartCut}
                        disabled={!videoFile || isProcessing}
                    >
                        {isProcessing ? (
                            <>处理中...</>
                        ) : (
                            <>
                                <Scissors className="w-4 h-4 mr-2" />
                                开始切割
                            </>
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
