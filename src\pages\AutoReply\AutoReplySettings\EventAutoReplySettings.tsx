import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { useAutoReply } from '@/hooks/useAutoReply'
import { pick } from 'lodash-es'
import ReplyMessageManager from './ReplyMessageManager'
import type { EventMessageType, SimpleEventReplyMessage } from '@/hooks/useAutoReply'

// 自动回复类型
const autoReplyTypes = [
  {
    id: 'room_enter' as EventMessageType,
    name: '进入直播间',
    default: '欢迎{用户名}来到直播间！',
  },
  {
    id: 'room_like' as EventMessageType,
    name: '点赞',
    default: '感谢{用户名}的点赞！',
  },
  {
    id: 'room_follow' as EventMessageType,
    name: '关注',
    default: '感谢{用户名}的关注！',
  },
  {
    id: 'subscribe_merchant_brand_vip' as EventMessageType,
    name: '加入粉丝团',
    default: '欢迎{用户名}加入粉丝团！',
  },
  {
    id: 'live_order' as EventMessageType,
    name: '下单',
    default: '感谢{用户名}的下单！',
  },
  {
    id: 'ecom_fansclub_participate' as EventMessageType,
    name: '参与粉丝团活动',
    default: '感谢{用户名}参与粉丝团活动！',
  },
]

const EventAutoReplySettings = () => {
  const {
    config,
    updateEventReplyContents,
    updateEventReplyEnabled,
    updateEventReplyOptions,
  } = useAutoReply()

  const typeReplies = pick(config, [
    'room_enter',
    'room_like',
    'room_follow',
    'subscribe_merchant_brand_vip',
    'live_order',
    'ecom_fansclub_participate',
  ])

  // 自动回复消息的开关
  const handleReplyChange = (type: EventMessageType, checked: boolean) => {
    updateEventReplyEnabled(type, checked)
  }

  // 处理其余消息的回复内容
  const handleMessageAdd = (
    type: EventMessageType,
    message: SimpleEventReplyMessage,
  ) => {
    updateEventReplyContents(type, [...typeReplies[type].messages, message])
  }

  const hanldeOptionsChange = (
    type: EventMessageType,
    options: Record<string, boolean>,
  ) => {
    updateEventReplyOptions(type, options)
  }

  const handleMessageRemove = (type: EventMessageType, index: number) => {
    const newMessages = typeReplies[type].messages.filter((_, i) => i !== index)
    updateEventReplyContents(type, newMessages)
  }

  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">其他自动回复</h3>

      <div className="space-y-6">
        {autoReplyTypes.map(type => (
          <div key={type.id} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="text-sm font-medium">
                  {type.name}自动回复
                </h4>
                <p className="text-xs text-muted-foreground">
                  当用户
                  {type.name}
                  时自动回复
                </p>
              </div>
              <Switch
                checked={typeReplies[type.id]?.enable || false}
                onCheckedChange={checked =>
                  handleReplyChange(type.id, checked)
                }
              />
            </div>

            {typeReplies[type.id]?.enable && (
              <Card className="border-dashed">
                <CardContent className="pt-4">
                  <ReplyMessageManager
                    title={`${type.name}回复消息`}
                    description="系统将从以下消息中随机选择一条发送，可使用{用户名}变量"
                    messages={
                      typeReplies[type.id]?.messages || [type.default]
                    }
                    onAdd={message =>
                      handleMessageAdd(type.id, message)
                    }
                    onRemove={index =>
                      handleMessageRemove(type.id, index)
                    }
                    placeholder={`例如：${type.default}`}
                    msgType={type.id}
                  />
                  {
                    // 单独处理已下单、已支付的回复
                    type.id === 'live_order' && (
                      <>
                        <Separator className="mt-4" />
                        <div className="flex justify-between items-center pt-4 text-sm">
                          <div className="flex flex-col">
                            <span>仅在已支付时回复</span>
                            <span className="text-muted-foreground">
                              用户订单具有<strong>已下单</strong>和
                              <strong>已支付</strong>两种状态
                            </span>
                          </div>
                          <Switch
                            checked={
                              config[type.id]?.options?.onlyReplyPaid
                            }
                            onCheckedChange={e =>
                              hanldeOptionsChange(type.id, {
                                onlyReplyPaid: e,
                              })
                            }
                          />
                        </div>
                      </>
                    )
                  }
                </CardContent>
              </Card>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

export default EventAutoReplySettings
