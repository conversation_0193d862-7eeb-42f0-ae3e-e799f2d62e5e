import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { persist } from 'zustand/middleware'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { setAudioOutputDevice, isAudioOutputSupported } from '@/utils/audioDevices'
import { DualTrackAudioManager } from '../services/DualTrackAudioService'

// 定义消息优先级
export const MESSAGE_PRIORITY = {
  'time-announcement': 1,    // 报时消息 - 最高优先级
  'auto-reply': 1,          // 自动回复
  'passive-interaction': 1,  // 被动互动
  'manual': 1,              // 手动添加
  'script': 5               // 话术列表 - 最低优先级
} as const

// 播放状态接口
interface PlaybackStatus {
  isRunning: boolean
  currentVoiceId: string | null
  currentTime: number
  duration: number
  progress: number // 0-100 的百分比
}

// 全局语音播放服务
class VoicePlaybackService {
  private static instance: VoicePlaybackService
  private currentAudioRef: HTMLAudioElement | null = null
  private stopPlaybackSignal = false
  private isPlaybackRunning = false
  private playbackLoop: Promise<void> | null = null
  private currentVoiceId: string | null = null
  private progressUpdateInterval: NodeJS.Timeout | null = null

  static getInstance(): VoicePlaybackService {
    if (!VoicePlaybackService.instance) {
      VoicePlaybackService.instance = new VoicePlaybackService()
    }
    return VoicePlaybackService.instance
  }

  async startPlayback() {
    if (this.isPlaybackRunning) {
      console.log('语音播放已在运行中')
      return
    }

    this.isPlaybackRunning = true
    this.stopPlaybackSignal = false
    console.log('开始全局语音播放服务')

    // 不等待播放循环，让它在后台运行
    this.playbackLoop = this.playVoiceListLoop()
  }

  async stopPlayback() {
    console.log('停止全局语音播放服务')
    this.stopPlaybackSignal = true

    // 清除进度更新定时器
    if (this.progressUpdateInterval) {
      clearInterval(this.progressUpdateInterval)
      this.progressUpdateInterval = null
    }

    if (this.currentAudioRef) {
      this.currentAudioRef.pause()
      this.currentAudioRef.currentTime = 0
      this.currentAudioRef = null
    }

    this.currentVoiceId = null

    // 不关闭OBS播放器，保持窗口打开
    console.log('语音播放服务已停止，OBS播放器保持打开状态')

    // 立即设置为停止状态，不等待播放循环
    this.isPlaybackRunning = false
    console.log('全局语音播放服务已停止')

    // 异步等待播放循环结束，但不阻塞停止操作
    if (this.playbackLoop) {
      this.playbackLoop.catch(error => {
        console.error('播放循环结束时出错:', error)
      }).finally(() => {
        this.playbackLoop = null
      })
    }
  }

  private async playVoiceListLoop(): Promise<void> {
    try {
      while (this.isPlaybackRunning && !this.stopPlaybackSignal) {
        // 检查停止信号
        if (this.stopPlaybackSignal) {
          console.log('收到停止信号，退出播放循环')
          break
        }

        // 每次循环都获取最新的语音列表
        const currentVoiceList = useAutoVoice.getState().voiceList

        if (currentVoiceList.length === 0) {
          console.log('语音列表为空，等待添加语音。')
          // 检查停止信号，避免在等待期间无法停止
          for (let i = 0; i < 10 && !this.stopPlaybackSignal; i++) {
            await new Promise(resolve => setTimeout(resolve, 100))
          }
          continue
        }

        // 数字人视频播放现在由双轨播放服务处理，这里跳过
        // 首先处理数字人视频项目
        // const digitalHumanItems = currentVoiceList.filter(voice =>
        //   voice.outputType === 'digital-human' && voice.videoUrl
        // )

        // console.log(`当前队列中有 ${currentVoiceList.length} 个项目`)
        // console.log(`其中数字人视频项目: ${digitalHumanItems.length} 个`)

        // if (digitalHumanItems.length > 0) {
        //   // 再次检查停止信号
        //   if (this.stopPlaybackSignal) {
        //     console.log('收到停止信号，跳过数字人视频播放')
        //     break
        //   }

        //   const digitalHumanToPlay = digitalHumanItems[0]
        //   console.log(`开始播放数字人视频: "${digitalHumanToPlay.text}"`)
        //   console.log(`视频URL: ${digitalHumanToPlay.videoUrl}`)

        //   // 确保OBS播放器已打开
        //   await this.ensureOBSPlayerOpen()

        //   // 再次检查停止信号
        //   if (this.stopPlaybackSignal) {
        //     console.log('收到停止信号，跳过数字人视频播放')
        //     break
        //   }

        //   // 播放数字人视频（通过IPC）并等待播放完成
        //   const playResult = await this.playDigitalHumanVideoViaIPC(digitalHumanToPlay)

        //   // 播放完成后移除项目
        //   if (playResult === 'completed') {
        //     console.log(`数字人视频播放完成，移除项目: ${digitalHumanToPlay.id}`)
        //     useAutoVoice.getState().removeVoiceItem(digitalHumanToPlay.id)
        //   } else if (playResult === 'timeout') {
        //     console.log(`数字人视频播放超时，移除项目: ${digitalHumanToPlay.id}`)
        //     useAutoVoice.getState().removeVoiceItem(digitalHumanToPlay.id)
        //   } else {
        //     console.log(`数字人视频播放失败，移除项目: ${digitalHumanToPlay.id}`)
        //     useAutoVoice.getState().removeVoiceItem(digitalHumanToPlay.id)
        //   }
        //   continue
        // }

        // 找到第一个有音频URL的语音项
        const voiceToPlay = currentVoiceList.find(voice =>
          voice.audioUrl &&
          voice.audioUrl.length > 0 &&
          voice.outputType !== 'digital-human'
        )

        if (!voiceToPlay) {
          console.log('语音列表中没有可播放的语音（都在生成中），等待...')
          // 等待一段时间后重新检查
          for (let i = 0; i < 5 && !this.stopPlaybackSignal; i++) {
            await new Promise(resolve => setTimeout(resolve, 200))
          }
          continue
        }

        if (!this.isPlaybackRunning || this.stopPlaybackSignal) {
          console.log('全局语音播放任务已中断。')
          return
        }

        // 播放找到的语音
        const voice = voiceToPlay
        console.log(`开始播放语音: "${voice.text}"`)

        // 设置当前播放的语音ID
        this.currentVoiceId = voice.id

        // 设置音频输出设备、音量和播放速度
        const state = useAutoVoice.getState()
        const { hostDeviceId, hostVolume, hostPlaybackSpeed } = state
        const audio = new Audio(voice.audioUrl)

        // 设置音量 (0-100 转换为 0-1) - 这里假设是主播音频
        audio.volume = hostVolume / 100

        // 设置播放速度
        audio.playbackRate = hostPlaybackSpeed

        if (isAudioOutputSupported() && hostDeviceId !== 'default') {
          await setAudioOutputDevice(audio, hostDeviceId)
        }

        this.currentAudioRef = audio

        // 开始进度更新
        this.startProgressUpdate()

        try {
          await new Promise<void>((resolve, reject) => {
            // 添加停止信号检查
            const checkStopSignal = () => {
              if (this.stopPlaybackSignal) {
                audio.pause()
                this.currentAudioRef = null
                this.currentVoiceId = null
                this.stopProgressUpdate()
                resolve()
                return true
              }
              return false
            }

            audio.onended = () => {
              this.currentAudioRef = null
              this.currentVoiceId = null
              this.stopProgressUpdate()
              resolve()
            }
            audio.onerror = (e) => {
              console.error("播放音频失败:", e)
              this.currentAudioRef = null
              this.currentVoiceId = null
              this.stopProgressUpdate()
              reject(e)
            }

            // 定期检查停止信号
            const stopCheckInterval = setInterval(() => {
              if (checkStopSignal()) {
                clearInterval(stopCheckInterval)
              }
            }, 100)

            audio.play().then(() => {
              // 播放开始后也检查一次停止信号
              if (checkStopSignal()) {
                clearInterval(stopCheckInterval)
              }
            }).catch(e => {
              clearInterval(stopCheckInterval)
              console.error("音频播放API调用失败:", e)
              this.currentAudioRef = null
              this.currentVoiceId = null
              this.stopProgressUpdate()
              reject(e)
            })
          })

          // 语音播放完成后自动移除
          if (!this.stopPlaybackSignal && this.isPlaybackRunning) {
            console.log(`语音 "${voice.text}" 播放完成，正在从列表中移除。`)
            useAutoVoice.getState().removeVoiceItem(voice.id)
          }

        } catch (e) {
          console.error('播放单个语音时发生错误或中断:', e)
          // 播放失败时也移除这个语音项，避免卡住
          if (this.isPlaybackRunning) {
            console.log(`语音 "${voice.text}" 播放失败，从列表中移除。`)
            useAutoVoice.getState().removeVoiceItem(voice.id)
          }
        }

        // 在每个语音播放后检查停止信号
        if (this.stopPlaybackSignal) {
          console.log('检测到停止信号，退出播放循环')
          return
        }
      }
    } finally {
      console.log('全局语音播放循环已停止。')
      this.isPlaybackRunning = false
      if (this.currentAudioRef) {
        this.currentAudioRef.pause()
        this.currentAudioRef = null
      }
    }
  }

  // 开始进度更新
  private startProgressUpdate() {
    this.stopProgressUpdate() // 确保没有重复的定时器

    this.progressUpdateInterval = setInterval(() => {
      if (this.currentAudioRef && this.currentVoiceId) {
        const currentTime = this.currentAudioRef.currentTime
        const duration = this.currentAudioRef.duration || 0
        const progress = duration > 0 ? (currentTime / duration) * 100 : 0

        // 更新 zustand store 中的播放状态
        useAutoVoice.getState().updatePlaybackStatus({
          isRunning: this.isPlaybackRunning,
          currentVoiceId: this.currentVoiceId,
          currentTime,
          duration,
          progress
        })
      }
    }, 100) // 每100ms更新一次进度
  }

  // 停止进度更新
  private stopProgressUpdate() {
    if (this.progressUpdateInterval) {
      clearInterval(this.progressUpdateInterval)
      this.progressUpdateInterval = null
    }

    // 清除播放状态
    useAutoVoice.getState().updatePlaybackStatus({
      isRunning: this.isPlaybackRunning,
      currentVoiceId: null,
      currentTime: 0,
      duration: 0,
      progress: 0
    })
  }

  // 确保OBS播放器已打开
  private async ensureOBSPlayerOpen(): Promise<void> {
    try {
      console.log('检查并确保OBS播放器已打开')

      // 从localStorage读取保存的比例设置
      let aspectRatio: '16:9' | '9:16' = '9:16' // 默认值
      try {
        const obsPlayerSettings = localStorage.getItem('obs-player-settings')
        if (obsPlayerSettings) {
          const settings = JSON.parse(obsPlayerSettings)
          if (settings.state && settings.state.aspectRatio) {
            aspectRatio = settings.state.aspectRatio
          }
        }
      } catch (error) {
        console.warn('读取OBS播放器设置失败，使用默认比例:', error)
      }

      await (window as any).ipcRenderer.invoke('obsPlayer:open', aspectRatio)
      console.log('OBS播放器已确保打开，等待2秒让窗口完全加载')

      // 等待2秒让OBS播放器窗口完全加载
      await new Promise(resolve => setTimeout(resolve, 2000))
      console.log('OBS播放器准备就绪')
    } catch (error) {
      console.error('打开OBS播放器失败:', error)
      // 不抛出错误，继续执行
    }
  }

  // 播放数字人视频（通过IPC）- 异步版本
  private async playDigitalHumanVideoViaIPC(voiceItem: VoiceItem): Promise<'completed' | 'timeout' | 'failed'> {
    if (!voiceItem.videoUrl) {
      console.error('数字人视频URL为空')
      return 'failed'
    }

    console.log('准备播放数字人视频:', {
      text: voiceItem.text,
      videoUrl: voiceItem.videoUrl,
      id: voiceItem.id
    })

    try {
      // 检查IPC是否可用
      if (!(window as any).ipcRenderer) {
        console.error('IPC渲染器不可用')
        return 'failed'
      }

      // 检查是否收到停止信号
      if (this.stopPlaybackSignal) {
        console.log('收到停止信号，跳过数字人视频播放')
        return 'failed'
      }

      // 通过IPC发送播放请求到OBS播放器
      console.log('发送IPC播放请求...')

      const videoUrl = String(voiceItem.videoUrl || '')
      const text = String(voiceItem.text || '')
      const id = String(voiceItem.id || '')

      console.log('发送的参数:', { videoUrl, text, id })

      // 添加超时保护，避免无限等待
      const playPromise = (window as any).ipcRenderer.invoke('obsPlayer:playVideo', videoUrl, text, id)
      const timeoutPromise = new Promise<'timeout'>((resolve) => {
        setTimeout(() => {
          console.log('数字人视频播放超时，强制继续')
          resolve('timeout')
        }, 35000) // 35秒超时，比主进程的30秒稍长
      })

      const result = await Promise.race([
        playPromise.then(() => 'completed' as const),
        timeoutPromise
      ])

      if (result === 'timeout') {
        console.log('数字人视频播放超时')
        return 'timeout'
      } else {
        console.log('数字人视频播放完成:', voiceItem.text)
        return 'completed'
      }
    } catch (error) {
      console.error('播放数字人视频失败:', error)
      return 'failed'
    }
  }



  getPlaybackStatus(): PlaybackStatus {
    const currentTime = this.currentAudioRef?.currentTime || 0
    const duration = this.currentAudioRef?.duration || 0
    const progress = duration > 0 ? (currentTime / duration) * 100 : 0

    return {
      isRunning: this.isPlaybackRunning,
      currentVoiceId: this.currentVoiceId,
      currentTime,
      duration,
      progress
    }
  }
}

// 导出全局实例
export const voicePlaybackService = VoicePlaybackService.getInstance()

// 将播放服务暴露到全局，以便其他组件访问
;(window as any).__voicePlaybackService = voicePlaybackService

// 默认泛化提示词
const DEFAULT_GENERALIZATION_PROMPT = `你是一个专业的直播话术泛化助手。你的任务是将给定的直播话术进行多样化改写，保持原意的同时增加表达的丰富性。

请遵循以下原则：
1. 保持原话术的核心意思和目的不变
2. 使用不同的表达方式、语气和词汇
3. 适合直播场景，语言要亲切自然
4. 每个变体都应该是完整的、可直接使用的话术
5. 生成5-8个不同的变体

请对以下话术进行泛化改写：
{original_script}

请直接返回变体列表，每行一个变体，不需要编号或其他格式。`

// 话术变体接口
interface ScriptVariant {
  id: string
  text: string
  usageCount: number // 使用次数
}

// 话术项接口
interface ScriptItem {
  id: string
  originalText: string // 原始话术
  variants: ScriptVariant[] // 泛化变体列表
  isGeneralized: boolean // 是否已泛化
}

// 话术列表接口
interface ScriptList {
  id: string
  name: string // 话术列表名称
  scripts: ScriptItem[] // 话术列表中的话术项
  createdAt: number // 创建时间
  updatedAt: number // 更新时间
}

// 定义 VoiceItem 接口
export interface VoiceItem {
  id: string
  text: string
  audioUrl?: string
  videoUrl?: string // 数字人视频URL
  duration?: number; // 音频时长 (毫秒)
  voiceFile?: string; // 生成此语音时使用的音色文件名
  digitalHumanFile?: string; // 生成此视频时使用的数字人形象文件名
  outputType?: 'voice' | 'digital-human'; // 输出类型
  source?: 'script' | 'manual' | 'auto-reply' | 'passive-interaction' | 'time-announcement'; // 来源类型
  speaker?: 'host' | 'assistant'; // 说话者：主播或助理
  priority?: number; // 消息优先级，数字越小优先级越高
  timestamp?: number; // 添加时间戳，用于相同优先级内的排序
}

// 工具函数：根据直播模式、助理开关、助理模式和来源确定说话者
export function determineSpeaker(
  source: VoiceItem['source'],
  liveMode: 'voice' | 'digital-human',
  assistantEnabled: boolean,
  assistantMode: 'voice' | 'digital-human'
): 'host' | 'assistant' {
  // 如果助理功能未开启，全部分配给主播
  if (!assistantEnabled) {
    return 'host'
  }

  // 话术列表和手动输入的内容始终分配给主播
  if (source === 'script' || source === 'manual') {
    return 'host'
  }

  // 其他内容（AI回复、自动互动等）分配给助理
  return 'assistant'
}

// 工具函数：根据说话者、直播模式和助理模式确定输出类型
export function determineOutputType(
  speaker: 'host' | 'assistant',
  liveMode: 'voice' | 'digital-human',
  assistantMode: 'voice' | 'digital-human'
): 'voice' | 'digital-human' {
  // 主播输出：根据直播模式决定
  if (speaker === 'host') {
    return liveMode
  }

  // 助理输出：根据助理模式决定
  return assistantMode
}

interface AutoVoiceState {
  isRunning: boolean;
  voiceList: VoiceItem[]; // 语音列表
  inputText: string;      // 输入文本
  scriptLists: Record<string, ScriptList>; // 话术列表集合
  currentScriptListId: string; // 当前选中的话术列表ID
  generalizationPrompt: string; // 泛化提示词
  // 主播音频设置
  hostDeviceId: string; // 主播音频输出设备ID
  hostVolume: number; // 主播音频音量 (0-100)
  hostPlaybackSpeed: number; // 主播音频播放速度 (0.25-2.0)
  // 助理音频设置
  assistantDeviceId: string; // 助理音频输出设备ID
  assistantVolume: number; // 助理音频音量 (0-100)
  assistantPlaybackSpeed: number; // 助理音频播放速度 (0.25-2.0)
  // 音色设置 - 分别为主播和助理设置
  hostSelectedVoices: string[]; // 主播选中的音色文件名列表（多选）
  assistantSelectedVoices: string[]; // 助理选中的音色文件名列表（多选）
  selectedVoices: string[]; // 兼容性保留：选中的音色文件名列表（多选）
  // 播放状态
  playbackStatus: PlaybackStatus; // 当前播放状态
  // 输出选项卡状态
  activeOutputTab: 'host' | 'assistant'; // 当前选中的输出选项卡
  // 批量操作状态
  batchGeneralizing: boolean; // 批量泛化状态
  batchGeneratingVoices: boolean; // 批量生成语音状态
  batchGeneralizingAbortController: AbortController | null; // 批量泛化中止控制器
  batchGeneratingAbortController: AbortController | null; // 批量生成语音中止控制器
  // 生成进度记录
  lastGenerationIndex: number; // 上次停止时的生成序号
  lastGenerationCount: number; // 上次停止时的总生成数量
  lastGenerationTimestamp: number; // 上次停止时的时间戳
  setIsRunning: (running: boolean) => void;
  notifyStreamServer: () => void; // 通知推流服务器更新输出列表
  setVoiceList: (list: VoiceItem[]) => void; // 设置语音列表的方法
  addVoiceItem: (item: VoiceItem) => void; // 添加单个语音条目的方法
  addVoiceItemToTop: (item: VoiceItem) => void; // 添加单个语音条目到顶部的方法
  addVoiceItemByPriority: (item: VoiceItem) => void; // 根据优先级添加语音条目的方法
  updateVoiceItem: (id: string, updates: Partial<VoiceItem>) => void; // 更新语音条目的方法
  setInputText: (text: string) => void;     // 设置输入文本的方法
  clearVoiceList: () => void; // 清空语音列表的方法
  removeVoiceItem: (id: string) => void; // 移除指定语音条目的方法
  // 话术列表管理方法
  createScriptList: (name: string) => string; // 创建新的话术列表，返回ID
  deleteScriptList: (listId: string) => void; // 删除话术列表
  renameScriptList: (listId: string, newName: string) => void; // 重命名话术列表
  switchScriptList: (listId: string) => void; // 切换当前话术列表
  getCurrentScriptList: () => ScriptList | null; // 获取当前话术列表
  getAllScriptLists: () => ScriptList[]; // 获取所有话术列表
  // 兼容性方法 - 获取当前话术列表的scripts数组
  getScriptList: () => ScriptItem[]; // 获取当前话术列表的话术数组（兼容性）
  // 话术管理方法（针对当前选中的话术列表）
  addScript: (script: string) => void; // 添加单个话术的方法
  removeScript: (index: number) => void; // 移除指定话术的方法
  clearScriptList: () => void; // 清空当前话术列表的方法
  updateScript: (scriptId: string, newText: string) => void; // 更新话术原始文本
  updateVariant: (scriptId: string, variantId: string, newText: string) => void; // 更新变体文本
  removeVariant: (scriptId: string, variantId: string) => void; // 删除变体
  exportScripts: () => string; // 导出话术（仅原始话术，不包含变体）
  clearAllVariants: () => void; // 清除所有话术的变体，保留原始话术
  clearScriptVariants: (scriptId: string) => void; // 清除单个话术的变体，保留原始话术
  setGeneralizationPrompt: (prompt: string) => void; // 设置泛化提示词
  generalizeScript: (scriptId: string, variants: string[]) => void; // 泛化单个话术
  getRandomVariant: (scriptId: string) => string | null; // 获取随机变体
  incrementVariantUsage: (scriptId: string, variantId: string) => void; // 增加变体使用次数
  // 主播音频控制方法
  setHostDeviceId: (deviceId: string) => void; // 设置主播音频输出设备
  setHostVolume: (volume: number) => void; // 设置主播音频音量
  setHostPlaybackSpeed: (speed: number) => void; // 设置主播音频播放速度
  // 助理音频控制方法
  setAssistantDeviceId: (deviceId: string) => void; // 设置助理音频输出设备
  setAssistantVolume: (volume: number) => void; // 设置助理音频音量
  setAssistantPlaybackSpeed: (speed: number) => void; // 设置助理音频播放速度
  // 音色设置方法 - 分别为主播和助理
  setHostSelectedVoices: (voices: string[]) => void; // 设置主播选中的音色列表
  setAssistantSelectedVoices: (voices: string[]) => void; // 设置助理选中的音色列表
  toggleHostVoiceSelection: (voice: string) => void; // 切换主播音色选择状态
  toggleAssistantVoiceSelection: (voice: string) => void; // 切换助理音色选择状态
  // 兼容性方法
  setSelectedVoices: (voices: string[]) => void; // 设置选中的音色列表（兼容性保留）
  toggleVoiceSelection: (voice: string) => void; // 切换音色选择状态（兼容性保留）
  getRandomSelectedVoice: () => string | null; // 从选中的音色中随机获取一个
  updatePlaybackStatus: (status: PlaybackStatus) => void; // 更新播放状态的方法
  setActiveOutputTab: (tab: 'host' | 'assistant') => void; // 设置当前选中的输出选项卡
  // 批量操作方法
  setBatchGeneralizing: (status: boolean) => void; // 设置批量泛化状态
  setBatchGeneratingVoices: (status: boolean) => void; // 设置批量生成语音状态
  setBatchGeneralizingAbortController: (controller: AbortController | null) => void; // 设置批量泛化中止控制器
  setBatchGeneratingAbortController: (controller: AbortController | null) => void; // 设置批量生成语音中止控制器
  stopBatchGeneralizing: () => void; // 停止批量泛化
  stopBatchGeneratingVoices: () => void; // 停止批量生成语音
  // 生成进度管理方法
  saveGenerationProgress: (index: number, count: number) => void; // 保存生成进度
  clearGenerationProgress: () => void; // 清除生成进度
  hasGenerationProgress: () => boolean; // 检查是否有保存的生成进度
}

// 创建默认话术列表
const createDefaultScriptList = (): ScriptList => ({
  id: 'default',
  name: '默认话术列表',
  scripts: [],
  createdAt: Date.now(),
  updatedAt: Date.now()
})

export const useAutoVoice = create<AutoVoiceState>()(
  persist(
    immer((set, get) => ({
    isRunning: false,
    voiceList: [], // 初始化语音列表
    inputText: '', // 初始化输入文本
    scriptLists: { default: createDefaultScriptList() }, // 初始化话术列表集合
    currentScriptListId: 'default', // 初始化当前选中的话术列表ID
    generalizationPrompt: DEFAULT_GENERALIZATION_PROMPT, // 初始化泛化提示词
    // 主播音频设置
    hostDeviceId: 'default', // 初始化主播音频输出设备
    hostVolume: 50, // 初始化主播音频音量为50%
    hostPlaybackSpeed: 1.0, // 初始化主播音频播放速度为正常速度
    // 助理音频设置
    assistantDeviceId: 'default', // 初始化助理音频输出设备
    assistantVolume: 50, // 初始化助理音频音量为50%
    assistantPlaybackSpeed: 1.0, // 初始化助理音频播放速度为正常速度
    // 音色设置 - 分别为主播和助理设置
    hostSelectedVoices: ['默认音色.wav'], // 初始化主播选中的音色列表
    assistantSelectedVoices: ['默认音色.wav'], // 初始化助理选中的音色列表
    selectedVoices: ['默认音色.wav'], // 兼容性保留：初始化选中的音色列表（默认选中一个）
    // 输出选项卡状态
    activeOutputTab: 'host', // 默认显示主播输出
    // 播放状态
    playbackStatus: {
      isRunning: false,
      currentVoiceId: null,
      currentTime: 0,
      duration: 0,
      progress: 0
    },
    // 批量操作状态
    batchGeneralizing: false,
    batchGeneratingVoices: false,
    batchGeneralizingAbortController: null,
    batchGeneratingAbortController: null,
    // 生成进度记录
    lastGenerationIndex: 0,
    lastGenerationCount: 0,
    lastGenerationTimestamp: 0,

    setIsRunning: async (running: boolean) => {
      try {
        if (running) {
          await window.ipcRenderer.invoke(IPC_CHANNELS.tasks.autoVoice.start)
          // 启动双轨播放服务
          await DualTrackAudioManager.getInstance().startDualTrackPlayback()
        } else {
          await window.ipcRenderer.invoke(IPC_CHANNELS.tasks.autoVoice.stop)
          // 停止双轨播放服务
          await DualTrackAudioManager.getInstance().stopDualTrackPlayback()

          // 停止运行时，自动停止批量操作
          const currentState = get()
          if (currentState.batchGeneralizing) {
            if (currentState.batchGeneralizingAbortController) {
              currentState.batchGeneralizingAbortController.abort()
            }
          }
          if (currentState.batchGeneratingVoices) {
            if (currentState.batchGeneratingAbortController) {
              currentState.batchGeneratingAbortController.abort()
            }
          }
        }
        set(state => {
          state.isRunning = running
          // 停止运行时，重置批量操作状态
          if (!running) {
            state.batchGeneralizing = false
            state.batchGeneratingVoices = false
            state.batchGeneralizingAbortController = null
            state.batchGeneratingAbortController = null
          }
        })
      } catch (error) {
        console.error('切换自动语音状态失败:', error)
      }
    },

    setVoiceList: (list: VoiceItem[]) => {
      set(state => {
        state.voiceList = list;
      });
    },

    addVoiceItem: (item: VoiceItem) => {
      set(state => {
        // 为话术列表项设置优先级和时间戳
        const itemWithPriority = {
          ...item,
          priority: item.priority ?? MESSAGE_PRIORITY[item.source || 'script'],
          timestamp: item.timestamp ?? Date.now()
        };
        state.voiceList.push(itemWithPriority);
      });
      // 通知推流服务器更新输出列表
      get().notifyStreamServer();
    },

    addVoiceItemToTop: (item: VoiceItem) => {
      set(state => {
        // 为高优先级项设置优先级和时间戳
        const itemWithPriority = {
          ...item,
          priority: item.priority ?? MESSAGE_PRIORITY[item.source || 'manual'],
          timestamp: item.timestamp ?? Date.now()
        };
        state.voiceList.unshift(itemWithPriority);
      });
      // 通知推流服务器更新输出列表
      get().notifyStreamServer();
    },

    addVoiceItemByPriority: (item: VoiceItem) => {
      set(state => {
        // 设置优先级和时间戳
        const itemWithPriority = {
          ...item,
          priority: item.priority ?? MESSAGE_PRIORITY[item.source || 'script'],
          timestamp: item.timestamp ?? Date.now()
        };

        // 找到合适的插入位置
        let insertIndex = state.voiceList.length;

        for (let i = 0; i < state.voiceList.length; i++) {
          const existingItem = state.voiceList[i];
          const existingPriority = existingItem.priority ?? MESSAGE_PRIORITY[existingItem.source || 'script'];

          // 如果新项优先级更高（数字更小），插入到这个位置
          if (itemWithPriority.priority < existingPriority) {
            insertIndex = i;
            break;
          }
          // 如果优先级相同，继续查找，直到找到优先级更低的项或到达列表末尾
          // 这样相同优先级的项会按添加顺序排列（新的在后面）
        }

        // 在指定位置插入
        state.voiceList.splice(insertIndex, 0, itemWithPriority);
      });
      // 通知推流服务器更新输出列表
      get().notifyStreamServer();
    },

    updateVoiceItem: (id: string, updates: Partial<VoiceItem>) => {
      set(state => {
        const index = state.voiceList.findIndex(item => item.id === id);
        if (index !== -1) {
          Object.assign(state.voiceList[index], updates);
        }
      });
      // 通知推流服务器更新输出列表
      get().notifyStreamServer();
    },

    setInputText: (text: string) => {
      set(state => {
        state.inputText = text;
      });
    },

    clearVoiceList: () => {
      set(state => {
        state.voiceList = [];
      });
      // 通知推流服务器更新输出列表
      get().notifyStreamServer();
    },

    removeVoiceItem: (id: string) => { // 实现移除方法
      set(state => {
        state.voiceList = state.voiceList.filter(item => item.id !== id);
      });
      // 通知推流服务器更新输出列表
      get().notifyStreamServer();
    },

    // 话术列表管理方法
    createScriptList: (name: string) => {
      const newId = Date.now().toString() + Math.random().toString(36).substr(2, 9)
      const newList: ScriptList = {
        id: newId,
        name: name.trim() || '新话术列表',
        scripts: [],
        createdAt: Date.now(),
        updatedAt: Date.now()
      }

      set(state => {
        state.scriptLists[newId] = newList
        state.currentScriptListId = newId // 自动切换到新创建的列表
      })

      return newId
    },

    deleteScriptList: (listId: string) => {
      const currentState = get()
      const scriptLists = Object.keys(currentState.scriptLists)

      // 不能删除最后一个话术列表
      if (scriptLists.length <= 1) {
        return
      }

      // 不能删除不存在的列表
      if (!currentState.scriptLists[listId]) {
        return
      }

      set(state => {
        delete state.scriptLists[listId]

        // 如果删除的是当前选中的列表，切换到第一个可用的列表
        if (state.currentScriptListId === listId) {
          const remainingIds = Object.keys(state.scriptLists)
          state.currentScriptListId = remainingIds[0]
        }
      })
    },

    renameScriptList: (listId: string, newName: string) => {
      set(state => {
        if (state.scriptLists[listId]) {
          state.scriptLists[listId].name = newName.trim() || '未命名列表'
          state.scriptLists[listId].updatedAt = Date.now()
        }
      })
    },

    switchScriptList: (listId: string) => {
      const currentState = get()
      if (currentState.scriptLists[listId]) {
        set(state => {
          state.currentScriptListId = listId
        })
      }
    },

    getCurrentScriptList: () => {
      const currentState = get()
      return currentState.scriptLists[currentState.currentScriptListId] || null
    },

    getAllScriptLists: () => {
      const currentState = get()
      return Object.values(currentState.scriptLists).sort((a, b) => a.createdAt - b.createdAt)
    },

    getScriptList: () => {
      const currentState = get()
      const currentList = currentState.scriptLists[currentState.currentScriptListId]
      return currentList ? currentList.scripts : []
    },

    addScript: (script: string) => {
      set(state => {
        const currentList = state.scriptLists[state.currentScriptListId]
        if (!currentList) return

        const trimmedScript = script.trim();
        if (trimmedScript && !currentList.scripts.some(item => item.originalText === trimmedScript)) {
          const newScript: ScriptItem = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            originalText: trimmedScript,
            variants: [{
              id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
              text: trimmedScript,
              usageCount: 0
            }],
            isGeneralized: false
          };
          currentList.scripts.push(newScript);
          currentList.updatedAt = Date.now();
        }
      });
    },

    removeScript: (index: number) => {
      set(state => {
        const currentList = state.scriptLists[state.currentScriptListId]
        if (!currentList) return

        currentList.scripts.splice(index, 1);
        currentList.updatedAt = Date.now();
      });
    },

    clearScriptList: () => {
      const currentState = get();
      const currentList = currentState.scriptLists[currentState.currentScriptListId]
      if (!currentList || currentList.scripts.length === 0) {
        return;
      }

      if (confirm('确定要清空当前话术列表吗？此操作不可撤销。')) {
        set(state => {
          const list = state.scriptLists[state.currentScriptListId]
          if (list) {
            list.scripts = [];
            list.updatedAt = Date.now();
          }
        });
      }
    },

    setGeneralizationPrompt: (prompt: string) => {
      set(state => {
        state.generalizationPrompt = prompt;
      });
    },

    generalizeScript: (scriptId: string, variants: string[]) => {
      set(state => {
        const currentList = state.scriptLists[state.currentScriptListId]
        if (!currentList) return

        const scriptIndex = currentList.scripts.findIndex(item => item.id === scriptId);
        if (scriptIndex !== -1) {
          const script = currentList.scripts[scriptIndex];
          script.variants = variants.map((text, index) => ({
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9) + index,
            text: text.trim(),
            usageCount: 0
          }));
          script.isGeneralized = true;
          currentList.updatedAt = Date.now();
        }
      });
    },

    getRandomVariant: (scriptId: string): string | null => {
      // 这个方法需要在外部调用，所以我们先返回一个简单的实现
      return null;
    },

    incrementVariantUsage: (scriptId: string, variantId: string) => {
      set(state => {
        const currentList = state.scriptLists[state.currentScriptListId]
        if (!currentList) return

        const scriptIndex = currentList.scripts.findIndex(item => item.id === scriptId);
        if (scriptIndex !== -1) {
          const script = currentList.scripts[scriptIndex];
          const variantIndex = script.variants.findIndex(variant => variant.id === variantId);
          if (variantIndex !== -1) {
            script.variants[variantIndex].usageCount += 1;
            currentList.updatedAt = Date.now();
          }
        }
      });
    },



    // 主播音频控制方法
    setHostDeviceId: (deviceId: string) => {
      set(state => {
        state.hostDeviceId = deviceId;
      });
    },

    setHostVolume: (volume: number) => {
      const clampedVolume = Math.max(0, Math.min(100, volume)); // 确保音量在0-100范围内
      set(state => {
        state.hostVolume = clampedVolume;
      });
    },

    setHostPlaybackSpeed: (speed: number) => {
      set(state => {
        state.hostPlaybackSpeed = Math.max(0.25, Math.min(2.0, speed)); // 确保播放速度在0.25-2.0范围内
      });
    },

    // 助理音频控制方法
    setAssistantDeviceId: (deviceId: string) => {
      set(state => {
        state.assistantDeviceId = deviceId;
      });
    },

    setAssistantVolume: (volume: number) => {
      const clampedVolume = Math.max(0, Math.min(100, volume)); // 确保音量在0-100范围内
      set(state => {
        state.assistantVolume = clampedVolume;
      });
    },

    setAssistantPlaybackSpeed: (speed: number) => {
      set(state => {
        state.assistantPlaybackSpeed = Math.max(0.25, Math.min(2.0, speed)); // 确保播放速度在0.25-2.0范围内
      });
    },

    // 主播音色设置方法
    setHostSelectedVoices: (voices: string[]) => {
      set(state => {
        state.hostSelectedVoices = voices;
        // 同时更新兼容性字段
        state.selectedVoices = voices;
      });
    },

    toggleHostVoiceSelection: (voice: string) => {
      set(state => {
        const index = state.hostSelectedVoices.indexOf(voice);
        if (index > -1) {
          // 如果已选中，则取消选择（但至少保留一个）
          if (state.hostSelectedVoices.length > 1) {
            state.hostSelectedVoices.splice(index, 1);
          }
        } else {
          // 如果未选中，则添加到选择列表
          state.hostSelectedVoices.push(voice);
        }
        // 同时更新兼容性字段
        state.selectedVoices = [...state.hostSelectedVoices];
      });
    },

    // 助理音色设置方法
    setAssistantSelectedVoices: (voices: string[]) => {
      set(state => {
        state.assistantSelectedVoices = voices;
      });
    },

    toggleAssistantVoiceSelection: (voice: string) => {
      set(state => {
        const index = state.assistantSelectedVoices.indexOf(voice);
        if (index > -1) {
          // 如果已选中，则取消选择（但至少保留一个）
          if (state.assistantSelectedVoices.length > 1) {
            state.assistantSelectedVoices.splice(index, 1);
          }
        } else {
          // 如果未选中，则添加到选择列表
          state.assistantSelectedVoices.push(voice);
        }
      });
    },

    // 兼容性方法
    setSelectedVoices: (voices: string[]) => {
      set(state => {
        state.selectedVoices = voices;
        // 同时更新主播音色（保持向后兼容）
        state.hostSelectedVoices = voices;
      });
    },

    toggleVoiceSelection: (voice: string) => {
      set(state => {
        const index = state.selectedVoices.indexOf(voice);
        if (index > -1) {
          // 如果已选中，则取消选择（但至少保留一个）
          if (state.selectedVoices.length > 1) {
            state.selectedVoices.splice(index, 1);
          }
        } else {
          // 如果未选中，则添加到选择列表
          state.selectedVoices.push(voice);
        }
        // 同时更新主播音色（保持向后兼容）
        state.hostSelectedVoices = [...state.selectedVoices];
      });
    },

    getRandomSelectedVoice: () => {
      // 使用 get() 函数来获取当前状态，避免循环引用
      const currentState = get();
      if (currentState.selectedVoices.length === 0) {
        return null;
      }
      const randomIndex = Math.floor(Math.random() * currentState.selectedVoices.length);
      return currentState.selectedVoices[randomIndex];
    },

    updatePlaybackStatus: (status: PlaybackStatus) => {
      set(state => {
        state.playbackStatus = status;
      });
    },

    setActiveOutputTab: (tab: 'host' | 'assistant') => {
      set(state => {
        state.activeOutputTab = tab;
      });
    },

    updateScript: (scriptId: string, newText: string) => {
      set(state => {
        const currentList = state.scriptLists[state.currentScriptListId]
        if (!currentList) return

        const scriptIndex = currentList.scripts.findIndex(item => item.id === scriptId);
        if (scriptIndex !== -1) {
          const script = currentList.scripts[scriptIndex];
          script.originalText = newText.trim();
          // 如果还没有泛化，也更新第一个变体（原始文本）
          if (!script.isGeneralized && script.variants.length > 0) {
            script.variants[0].text = newText.trim();
          }
          currentList.updatedAt = Date.now();
        }
      });
    },

    updateVariant: (scriptId: string, variantId: string, newText: string) => {
      set(state => {
        const currentList = state.scriptLists[state.currentScriptListId]
        if (!currentList) return

        const scriptIndex = currentList.scripts.findIndex(item => item.id === scriptId);
        if (scriptIndex !== -1) {
          const script = currentList.scripts[scriptIndex];
          const variantIndex = script.variants.findIndex(variant => variant.id === variantId);
          if (variantIndex !== -1) {
            script.variants[variantIndex].text = newText.trim();
            currentList.updatedAt = Date.now();
          }
        }
      });
    },

    removeVariant: (scriptId: string, variantId: string) => {
      set(state => {
        const currentList = state.scriptLists[state.currentScriptListId]
        if (!currentList) return

        const scriptIndex = currentList.scripts.findIndex(item => item.id === scriptId);
        if (scriptIndex !== -1) {
          const script = currentList.scripts[scriptIndex];
          // 确保至少保留一个变体
          if (script.variants.length > 1) {
            script.variants = script.variants.filter(variant => variant.id !== variantId);
            currentList.updatedAt = Date.now();
          }
        }
      });
    },

    exportScripts: () => {
      const currentState = get();
      const currentList = currentState.scriptLists[currentState.currentScriptListId]
      if (!currentList || currentList.scripts.length === 0) {
        return '';
      }

      // 只导出原始话术，不包含变体
      return currentList.scripts
        .map(script => script.originalText)
        .join('\n');
    },

    clearAllVariants: () => {
      const currentState = get();
      const currentList = currentState.scriptLists[currentState.currentScriptListId]
      if (!currentList) return

      const hasVariants = currentList.scripts.some(script =>
        script.isGeneralized && script.variants.length > 1
      );

      if (!hasVariants) {
        return;
      }

      if (confirm('确定要清除当前话术列表所有话术的变体吗？此操作将保留原始话术，但删除所有泛化生成的变体，且不可撤销。')) {
        set(state => {
          const list = state.scriptLists[state.currentScriptListId]
          if (list) {
            list.scripts.forEach(script => {
              if (script.isGeneralized) {
                // 只保留第一个变体（原始话术）
                script.variants = script.variants.slice(0, 1);
                script.isGeneralized = false;
              }
            });
            list.updatedAt = Date.now();
          }
        });
      }
    },

    clearScriptVariants: (scriptId: string) => {
      set(state => {
        const currentList = state.scriptLists[state.currentScriptListId]
        if (!currentList) return

        const scriptIndex = currentList.scripts.findIndex(item => item.id === scriptId);
        if (scriptIndex !== -1) {
          const script = currentList.scripts[scriptIndex];
          if (script.isGeneralized && script.variants.length > 1) {
            // 只保留第一个变体（原始话术）
            script.variants = script.variants.slice(0, 1);
            script.isGeneralized = false;
            currentList.updatedAt = Date.now();
          }
        }
      });
    },

    // 批量操作方法
    setBatchGeneralizing: (status: boolean) => {
      set(state => {
        state.batchGeneralizing = status;
      });
    },

    setBatchGeneratingVoices: (status: boolean) => {
      set(state => {
        state.batchGeneratingVoices = status;
      });
    },

    setBatchGeneralizingAbortController: (controller: AbortController | null) => {
      set(state => {
        state.batchGeneralizingAbortController = controller;
      });
    },

    setBatchGeneratingAbortController: (controller: AbortController | null) => {
      set(state => {
        state.batchGeneratingAbortController = controller;
      });
    },

    stopBatchGeneralizing: () => {
      set(state => {
        if (state.batchGeneralizingAbortController) {
          state.batchGeneralizingAbortController.abort();
          state.batchGeneralizingAbortController = null;
        }
        state.batchGeneralizing = false;
      });
    },

    stopBatchGeneratingVoices: () => {
      set(state => {
        if (state.batchGeneratingAbortController) {
          state.batchGeneratingAbortController.abort();
          state.batchGeneratingAbortController = null;
        }
        state.batchGeneratingVoices = false;
      });
    },

    // 生成进度管理方法
    saveGenerationProgress: (index: number, count: number) => {
      set(state => {
        state.lastGenerationIndex = index;
        state.lastGenerationCount = count;
        state.lastGenerationTimestamp = Date.now();
      });
    },

    clearGenerationProgress: () => {
      set(state => {
        state.lastGenerationIndex = 0;
        state.lastGenerationCount = 0;
        state.lastGenerationTimestamp = 0;
      });
    },

    hasGenerationProgress: () => {
      const state = get();
      return state.lastGenerationTimestamp > 0 && state.lastGenerationIndex > 0;
    },

    // 通知推流服务器更新输出列表
    notifyStreamServer: () => {
      try {
        const currentState = get();
        const voiceList = currentState.voiceList;

        // 通过IPC通知推流服务器
        if (typeof window !== 'undefined' && window.ipcRenderer) {
          window.ipcRenderer.invoke(IPC_CHANNELS.streamServer.updateVoiceList, voiceList).catch(error => {
            console.error('通知推流服务器失败:', error);
          });
        }
      } catch (error) {
        console.error('通知推流服务器失败:', error);
      }
    }
  })),
  {
    name: 'auto-voice-storage',
    version: 2, // 增加版本号以支持数据迁移
    partialize: (state) => ({
      scriptLists: state.scriptLists,
      currentScriptListId: state.currentScriptListId,
      generalizationPrompt: state.generalizationPrompt,
      hostDeviceId: state.hostDeviceId,
      hostVolume: state.hostVolume,
      hostPlaybackSpeed: state.hostPlaybackSpeed,
      assistantDeviceId: state.assistantDeviceId,
      assistantVolume: state.assistantVolume,
      assistantPlaybackSpeed: state.assistantPlaybackSpeed,
      hostSelectedVoices: state.hostSelectedVoices,
      assistantSelectedVoices: state.assistantSelectedVoices,
      selectedVoices: state.selectedVoices,
      activeOutputTab: state.activeOutputTab,
    }),
    migrate: (persistedState: any, version: number) => {
      // 从版本1迁移到版本2：将单个scriptList转换为多个scriptLists
      if (version === 1) {
        const oldState = persistedState as any
        const defaultList = createDefaultScriptList()

        // 如果存在旧的scriptList，将其迁移到默认列表中
        if (oldState.scriptList && Array.isArray(oldState.scriptList)) {
          defaultList.scripts = oldState.scriptList
          defaultList.updatedAt = Date.now()
        }

        return {
          ...oldState,
          scriptLists: { default: defaultList },
          currentScriptListId: 'default',
          // 移除旧的scriptList字段
          scriptList: undefined
        }
      }

      return persistedState
    },
  }
  ),
)

// 导出双轨音频播放管理器实例
export const dualTrackAudioManager = DualTrackAudioManager.getInstance()

// 将双轨播放服务暴露到全局，以便其他组件访问
;(window as any).__dualTrackAudioManager = dualTrackAudioManager

// 设置推流完成事件监听器
let streamCompletionListenerSetup = false

export const setupStreamCompletionListener = () => {
  console.log('尝试设置推流完成事件监听器...')
  console.log('streamCompletionListenerSetup:', streamCompletionListenerSetup)
  console.log('typeof window:', typeof window)
  console.log('window.ipcRenderer:', !!window.ipcRenderer)
  console.log('window.ipcRenderer.on:', typeof window.ipcRenderer?.on)

  // 检查是否在 Electron 环境中
  if (streamCompletionListenerSetup) {
    console.log('监听器已经设置过了')
    return
  }

  if (typeof window === 'undefined') {
    console.log('window 未定义')
    return
  }

  if (!window.ipcRenderer) {
    console.log('window.ipcRenderer 不存在')
    return
  }

  if (typeof window.ipcRenderer.on !== 'function') {
    console.log('window.ipcRenderer.on 不是函数')
    return
  }

  streamCompletionListenerSetup = true

  try {
    console.log('开始设置监听器，事件名称:', IPC_CHANNELS.streamServer.itemStreamed)

    window.ipcRenderer.on(IPC_CHANNELS.streamServer.itemStreamed, (itemId: string) => {
      console.log('收到推流完成通知，移除项目:', itemId)

      try {
        // 延迟获取 store 状态，确保 store 已经初始化
        setTimeout(() => {
          try {
            const currentState = useAutoVoice.getState()
            if (currentState && currentState.removeVoiceItem) {
              currentState.removeVoiceItem(itemId)
              console.log('成功移除推流完成的项目:', itemId)
            } else {
              console.error('useAutoVoice store 或 removeVoiceItem 方法不可用')
            }
          } catch (error) {
            console.error('移除推流完成项目失败:', error)
          }
        }, 100)
      } catch (error) {
        console.error('处理推流完成通知失败:', error)
      }
    })

    console.log('推流完成事件监听器已设置成功')
  } catch (error) {
    console.error('设置推流完成事件监听器失败:', error)
  }
}