import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'
import { type Message, useAutoReply, useNicknameOptionsStore } from '@/hooks/useAutoReply'
import { useCurrentLiveControl } from '@/hooks/useLiveControl'
import { useToast } from '@/hooks/useToast'
import { cn } from '@/lib/utils'
import { Pause, Play, RefreshCcw, Plus, Trash2 } from 'lucide-react'
import { useMemo, useState, useEffect, useRef } from 'react'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { useVirtualizer } from '@tanstack/react-virtual'

const getMessageColor = (type: Message['msg_type']) => {
  switch (type) {
    case 'comment':
      return 'text-foreground'
    case 'room_enter':
      return 'text-blue-500'
    case 'room_like':
      return 'text-pink-500'
    case 'room_follow':
      return 'text-purple-500'
    case 'subscribe_merchant_brand_vip':
      return 'text-amber-500'
    case 'live_order':
      return 'text-green-500'
    case 'ecom_fansclub_participate':
      return 'text-purple-500'
    default:
      return 'text-foreground'
  }
}

const getMessageText = (message: Message) => {
  switch (message.msg_type) {
    case 'comment':
      return message.content
    case 'room_enter':
      return '进入直播间'
    case 'room_like':
      return '点赞了直播间'
    case 'room_follow':
      return '关注了直播间'
    case 'subscribe_merchant_brand_vip':
      return '加入了品牌会员'
    case 'live_order':
      return message.product_title
    case 'ecom_fansclub_participate':
      return '加入了粉丝团'
    default:
      return '未知消息'
  }
}

const getOrderStatusColor = (status: LiveOrderMessage['order_status']) => {
  switch (status) {
    case '已下单':
      return 'text-blue-500 bg-blue-50' // 待付款状态显示蓝色
    case '已付款':
      return 'text-green-500 bg-green-50' // 已付款状态显示绿色
    default:
      return 'text-foreground'
  }
}

const MessageItem = ({
  message,
  isHighlighted,
}: { message: Message; isHighlighted: boolean }) => {
  const displayName = message.nick_name

  return (
    <div
      className={cn(
        'flex items-start gap-3 py-2 px-3 rounded-lg hover:bg-muted/50 transition-colors',
        isHighlighted ? 'bg-blue-50 hover:bg-blue-100' : 'hover:bg-muted/50',
      )}
    >
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="truncate text-sm text-muted-foreground">
            {displayName}
          </span>
          {message.msg_type === 'live_order' && (
            <Badge
              variant="outline"
              className={cn(
                'text-xs px-1.5 py-0',
                getOrderStatusColor(message.order_status),
              )}
            >
              {message.order_status}
            </Badge>
          )}
          <span className="text-xs text-muted-foreground ml-auto">
            {message.time}
          </span>
        </div>

        <div className="mt-0.5 text-sm">
          <p
            className={cn(
              getMessageColor(message.msg_type),
              message.msg_type === 'live_order' ? 'font-medium' : '',
            )}
          >
            {getMessageText(message)}
          </p>
        </div>
      </div>
    </div>
  )
}



export default function CommentList({
  highlight: highlightedCommentId,
}: {
  highlight: string | null
}) {
  const { comments, isListening, setIsListening, config, clearComments, addManualComment } = useAutoReply()
  const isConnected = useCurrentLiveControl(context => context.isConnected)
  const platform = useCurrentLiveControl(context => context.platform)
  const { toast } = useToast()
  const [messageTypeFilter, setMessageTypeFilter] = useState<Message['msg_type'] | 'all'>('all')
  const [open, setOpen] = useState(false)
  const [tempComment, setTempComment] = useState('什么时候发货')

  // 虚拟列表相关
  const parentRef = useRef<HTMLDivElement>(null)

  // 昵称选项管理
  const { options, addOption, removeOption, setDefault, getDefaultOption } = useNicknameOptionsStore()
  const [selectedNicknameId, setSelectedNicknameId] = useState<string>(() => {
    const defaultOption = getDefaultOption()
    return defaultOption?.id || 'default'
  })
  const [newNickname, setNewNickname] = useState('')
  const [showNicknameManager, setShowNicknameManager] = useState(false)

  // 当默认昵称改变时，更新选中状态
  useEffect(() => {
    const defaultOption = getDefaultOption()
    if (defaultOption && selectedNicknameId !== defaultOption.id) {
      setSelectedNicknameId(defaultOption.id)
    }
  }, [options, getDefaultOption, selectedNicknameId])

  // 手动启动监听评论
  const startListening = async () => {
    if (isConnected !== 'connected') {
      toast.error('请先连接直播间')
      return
    }

    try {
      setIsListening('waiting')
      const result = await window.ipcRenderer.invoke(
        IPC_CHANNELS.tasks.autoReply.startCommentListener,
        {
          source: config.entry,
          ws: config.ws?.enable ? { port: config.ws.port } : undefined,
        },
      )
      if (!result) throw new Error('监听评论失败')
      toast.success('监听评论成功')
      setIsListening('listening')
    } catch (error) {
      setIsListening('error')
      toast.error('监听评论失败')
    }
  }

  // 手动停止监听评论
  const stopListening = async () => {
    try {
      await window.ipcRenderer.invoke(
        IPC_CHANNELS.tasks.autoReply.stopCommentListener,
      )
      setIsListening('stopped')
      toast.success('已停止监听评论')
    } catch (error) {
      toast.error('停止监听评论失败')
    }
  }

  const accountName = useCurrentLiveControl(ctx => ctx.accountName)

  // 处理添加昵称选项
  const handleAddNickname = () => {
    if (!newNickname.trim()) {
      toast.error('请输入昵称')
      return
    }
    addOption(newNickname.trim())
    setNewNickname('')
    toast.success('昵称已添加')
  }

  // 处理删除昵称选项
  const handleRemoveNickname = (id: string) => {
    if (options.length <= 1) {
      toast.error('至少需要保留一个昵称选项')
      return
    }
    removeOption(id)
    // 如果删除的是当前选中的，切换到默认选项
    if (selectedNicknameId === id) {
      const defaultOption = getDefaultOption()
      setSelectedNicknameId(defaultOption?.id || options[0]?.id || 'default')
    }
    toast.success('昵称已删除')
  }

  // 处理设置默认昵称
  const handleSetDefault = (id: string) => {
    setDefault(id)
    setSelectedNicknameId(id)
    toast.success('默认昵称已设置')
  }

  // 处理添加评论
  const handleAddComment = () => {
    if (!tempComment.trim()) {
      toast.error('请输入评论内容')
      return
    }

    const selectedOption = options.find(option => option.id === selectedNicknameId)
    const nickname = selectedOption?.name || '游客'
    addManualComment(tempComment.trim(), nickname)
    setTempComment('')
    setOpen(false)
    toast.success('评论已添加')
  }

  // 处理清空评论
  const handleClearComments = () => {
    clearComments()
    toast.success('评论列表已清空')
  }

  const filteredComments = useMemo(() => {
    let filtered = comments

    // 按消息类型筛选
    if (messageTypeFilter !== 'all') {
      filtered = filtered.filter(comment => comment.msg_type === messageTypeFilter)
    }

    // 屏蔽主播自己的消息
    if (config.hideHost) {
      filtered = filtered.filter(comment => comment.nick_name !== accountName)
    }

    return filtered
  }, [comments, messageTypeFilter, config.hideHost, accountName])

  // 创建虚拟列表实例
  const virtualizer = useVirtualizer({
    count: filteredComments.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 80, // 估算每个消息项的高度
    overscan: 5, // 预渲染额外的项目数量
    // 启用动态高度测量
    measureElement: (element) => {
      return element?.getBoundingClientRect().height ?? 80
    },
  })

  const isButtonDisabled =
    isListening === 'waiting' ||
    isConnected !== 'connected' ||
    (platform !== 'douyin' && platform !== 'buyin')

  return (
    <>
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div>
              <CardTitle>消息列表</CardTitle>
              <CardDescription>直播间消息弹幕</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {/* 消息类型筛选 - 移动到监听按钮前面 */}
              <div className="flex items-center">
                <Label htmlFor="message-type-filter" className="text-sm">筛选：</Label>
                <Select
                  value={messageTypeFilter}
                  onValueChange={(value: Message['msg_type'] | 'all') => setMessageTypeFilter(value)}
                >
                  <SelectTrigger className="w-[100px]" id="message-type-filter">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="comment">评论</SelectItem>
                    <SelectItem value="room_enter">进入直播间</SelectItem>
                    <SelectItem value="room_like">点赞</SelectItem>
                    <SelectItem value="room_follow">关注</SelectItem>
                    <SelectItem value="live_order">下单</SelectItem>
                    <SelectItem value="subscribe_merchant_brand_vip">品牌会员</SelectItem>
                    <SelectItem value="ecom_fansclub_participate">粉丝团</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Dialog open={open} onOpenChange={setOpen}>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[600px]">
                  <DialogHeader>
                    <DialogTitle>模拟添加消息</DialogTitle>
                    <DialogDescription>
                      手动添加一条评论到消息列表中
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label>用户昵称</Label>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowNicknameManager(!showNicknameManager)}
                        >
                          {showNicknameManager ? '收起管理' : '管理昵称'}
                        </Button>
                      </div>

                      {/* 昵称选择列表 */}
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {options.map(option => (
                          <div key={option.id} className="flex items-center space-x-2 p-2 border rounded">
                            <Checkbox
                              id={`nickname-${option.id}`}
                              checked={selectedNicknameId === option.id}
                              onCheckedChange={() => setSelectedNicknameId(option.id)}
                            />
                            <Label
                              htmlFor={`nickname-${option.id}`}
                              className="flex-1 cursor-pointer"
                            >
                              {option.name}
                              {option.isDefault && (
                                <Badge variant="secondary" className="ml-2 text-xs">默认</Badge>
                              )}
                            </Label>
                            {showNicknameManager && (
                              <div className="flex gap-1">
                                {!option.isDefault && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleSetDefault(option.id)}
                                  >
                                    设为默认
                                  </Button>
                                )}
                                {options.length > 1 && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleRemoveNickname(option.id)}
                                  >
                                    删除
                                  </Button>
                                )}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>

                      {/* 添加新昵称 */}
                      {showNicknameManager && (
                        <div className="flex gap-2">
                          <Input
                            placeholder="输入新昵称..."
                            value={newNickname}
                            onChange={e => setNewNickname(e.target.value)}
                            onKeyDown={e => {
                              if (e.key === 'Enter') {
                                handleAddNickname()
                              }
                            }}
                          />
                          <Button
                            variant="outline"
                            onClick={handleAddNickname}
                            disabled={!newNickname.trim()}
                          >
                            添加
                          </Button>
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="comment-input">评论内容</Label>
                      <Textarea
                        id="comment-input"
                        placeholder="输入评论内容..."
                        value={tempComment}
                        onChange={e => setTempComment(e.target.value)}
                        rows={3}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setOpen(false)
                        setShowNicknameManager(false)
                      }}
                    >
                      取消
                    </Button>
                    <Button
                      onClick={handleAddComment}
                      disabled={!tempComment.trim()}
                    >
                      添加
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
              {filteredComments.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearComments}
                  className="flex items-center gap-1"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}

              {/* 监听控制按钮 */}
              {isListening === 'listening' ? (
                <Button
                  variant="default"
                  size="sm"
                  onClick={stopListening}
                  className="flex items-center gap-1 bg-green-600 hover:bg-green-700 text-white"
                >
                  <Pause className="h-4 w-4" />
                  监听
                </Button>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={startListening}
                  className="flex items-center gap-1"
                  title={
                    isListening === 'waiting' || isConnected !== 'connected'
                      ? '请先连接直播间'
                      : undefined
                  }
                  disabled={isButtonDisabled}
                >
                  {isListening === 'waiting' ? (
                    '连接中...'
                  ) : (
                    <>
                      <Play className="h-4 w-4" />
                      监听
                    </>
                  )}
                </Button>
              )}

              {isConnected === 'connected' && isListening === 'error' && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={startListening}
                  title="重试"
                >
                  <RefreshCcw className="h-4 w-4" />
                </Button>
              )}


            </div>
          </div>
        </CardHeader>
        <Separator />
        <CardContent className="p-0">
          {filteredComments.length === 0 ? (
            <div className="flex items-center justify-center h-[calc(100vh-360px)] text-muted-foreground">
              {isListening === 'listening'
                ? '暂无评论数据'
                : '请点击"开始监听"按钮开始接收评论'}
            </div>
          ) : (
            <div
              ref={parentRef}
              className="h-[calc(100vh-360px)] overflow-auto"
            >
              <div
                style={{
                  height: `${virtualizer.getTotalSize()}px`,
                  width: '100%',
                  position: 'relative',
                }}
              >
                {virtualizer.getVirtualItems().map((virtualItem) => (
                  <div
                    key={virtualItem.key}
                    data-index={virtualItem.index}
                    ref={virtualizer.measureElement}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      transform: `translateY(${virtualItem.start}px)`,
                    }}
                    className="py-1 px-2" // 添加间距
                  >
                    <MessageItem
                      message={filteredComments[virtualItem.index]}
                      isHighlighted={highlightedCommentId === filteredComments[virtualItem.index].msg_id}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>


    </>
  )
}
