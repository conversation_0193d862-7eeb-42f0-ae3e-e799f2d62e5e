import { TaskButton } from '@/components/common/TaskButton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  useAutoTimeAnnouncementActions,
  useCurrentAutoTimeAnnouncement,
} from '@/hooks/useAutoTimeAnnouncement'
import { useToast } from '@/hooks/useToast'
import { useMemoizedFn } from 'ahooks'
import { useCallback, useState } from 'react'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import TimeAnnouncementListCard from './components/TimeAnnouncementListCard'
import TimeAnnouncementSettingsCard from './components/TimeAnnouncementSettingsCard'

interface TaskControl {
  isRunning: boolean
  onStartTask: () => void
  onStopTask: () => void
}

// 实现useTaskControl hook的功能
function useTaskControl(): TaskControl {
  const isRunning = useCurrentAutoTimeAnnouncement(context => context.isRunning)
  const config = useCurrentAutoTimeAnnouncement(context => context.config)
  const { setIsRunning } = useAutoTimeAnnouncementActions()
  const { toast } = useToast()

  const onStartTask = useCallback(async () => {
    if (!config.enabled) {
      toast.error('请先启用自动报时功能')
      return
    }
    
    const enabledMessages = config.messages.filter(msg => msg.enabled && msg.content.trim())
    if (enabledMessages.length === 0) {
      toast.error('请至少添加一条启用的报时消息')
      return
    }

    const result = await window.ipcRenderer.invoke(
      IPC_CHANNELS.tasks.autoTimeAnnouncement.start,
      config,
    )
    if (result) {
      setIsRunning(true)
      toast.success('自动报时任务已启动')
    } else {
      setIsRunning(false)
      toast.error('自动报时任务启动失败')
    }
  }, [config, setIsRunning, toast])

  const onStopTask = useCallback(async () => {
    await window.ipcRenderer.invoke(IPC_CHANNELS.tasks.autoTimeAnnouncement.stop)
    setIsRunning(false)
    toast.success('自动报时任务已停止')
  }, [setIsRunning, toast])

  return {
    isRunning,
    onStartTask,
    onStopTask,
  }
}

export default function AutoTimeAnnouncementSettings() {
  const { isRunning, onStartTask, onStopTask } = useTaskControl()
  const [validationError] = useState<string | null>(null)

  const handleTaskButtonClick = useMemoizedFn(() => {
    if (!isRunning) onStartTask()
    else onStopTask()
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h3 className="text-sm font-medium">自动报时</h3>
          <p className="text-sm text-muted-foreground">
            定时在输出列表中插入报时语音信息
          </p>
        </div>
        <TaskButton
          isTaskRunning={isRunning}
          onStartStop={handleTaskButtonClick}
        />
      </div>

      {validationError && (
        <Alert variant="destructive">
          <AlertDescription>{validationError}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-6">
        <TimeAnnouncementSettingsCard />
        <TimeAnnouncementListCard />
      </div>
    </div>
  )
}
