import { useMemoizedFn } from 'ahooks'
import { CheckIcon, CircleAlert, GlobeIcon, XIcon } from 'lucide-react'
import React, { useState } from 'react'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  useCurrentChromeConfig,
  useCurrentChromeConfigActions,
} from '@/hooks/useChromeConfig'
import {
  useCurrentLiveControl,
  useCurrentLiveControlActions,
} from '@/hooks/useLiveControl'
import { useToast } from '@/hooks/useToast'
import PlatformSelect from './PlatformSelect'
import { useDevMode } from '@/hooks/useDevMode'

const StatusAlert = React.memo(() => {
  const platform = useCurrentLiveControl(state => state.platform)
  if (platform === 'wxchannel') {
    return (
      <Alert>
        <CircleAlert className="h-4 w-4" />
        <AlertTitle>你选择了视频号平台，请注意以下事项：</AlertTitle>
        <AlertDescription>
          <ol className="list-decimal list-inside">
            <li>
              请先确认<strong>开播后</strong>再连接中控台
            </li>
            <li>
              视频号助手无法<strong>一号多登</strong>，在别处登录视频号助手会
              <strong>中断连接</strong>!
            </li>
          </ol>
        </AlertDescription>
      </Alert>
    )
  }
  if (platform === 'taobao') {
    return (
      <Alert>
        <CircleAlert className="h-4 w-4" />
        <AlertTitle>你选择了淘宝平台，请注意以下事项：</AlertTitle>
        <AlertDescription>
          <ol className="list-decimal list-inside">
            <li>
              请先确认<strong>开播后</strong>
              再连接中控台，因为进入淘宝中控台需要获取<strong>直播间ID</strong>
            </li>
          </ol>
        </AlertDescription>
      </Alert>
    )
  }
  return null
})

const StatusCard = React.memo(() => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>连接状态</CardTitle>
        <CardDescription>请先连接直播间再使用</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <ConnectState />
            <div className="flex items-center gap-2">
              <PlatformSelect />
              <ConnectToLiveControl />
            </div>
          </div>
          <StatusAlert />
        </div>
      </CardContent>
    </Card>
  )
})

const ConnectToLiveControl = React.memo(() => {
  const { setIsConnected, setAccountName } = useCurrentLiveControlActions()
  const platform = useCurrentLiveControl(context => context.platform)
  const isConnected = useCurrentLiveControl(context => context.isConnected)
  const chromePath = useCurrentChromeConfig(context => context.path)
  const storageState = useCurrentChromeConfig(context => context.storageState)
  const { enabled: devMode, testConnection } = useDevMode()
  const { toast } = useToast()

  const connectLiveControl = useMemoizedFn(async () => {
    try {
      setIsConnected('connecting')
      const result = await window.ipcRenderer.invoke(
        IPC_CHANNELS.tasks.liveControl.connect,
        {
          headless: !devMode, // 开发者模式下关闭无头模式
          chromePath,
          storageState,
          platform,
          testConnection
        },
      )

      if (result) {
        setIsConnected('connected')
        setAccountName(result.accountName || '')
        toast.success(testConnection ? '已连接到测试直播间' : '已连接到直播间')
      } else {
        throw new Error('连接直播间失败')
      }
    } catch (error) {
      setIsConnected('disconnected')
      toast.error(error instanceof Error ? error.message : '连接直播间失败')
    }
  })

  const disconnectLiveControl = useMemoizedFn(async () => {
    try {
      await window.ipcRenderer.invoke(IPC_CHANNELS.tasks.liveControl.disconnect)
      setAccountName('')
      toast.success('已断开连接')
    } catch (error) {
      toast.error(error instanceof Error ? error.message : '断开连接失败')
    } finally {
      setIsConnected('disconnected')
    }
  })

  const handleButtonClick = useMemoizedFn(() => {
    if (isConnected === 'connected') {
      disconnectLiveControl()
    } else {
      connectLiveControl()
    }
  })

  return isConnected === 'connected' ? (
    <DisconnectButton handleButtonClick={handleButtonClick} />
  ) : (
    <ConnectButton
      isLoading={isConnected === 'connecting'}
      handleButtonClick={handleButtonClick}
    />
  )
})

const ConnectButton = React.memo(
  ({
    isLoading,
    handleButtonClick,
  }: {
    isLoading: boolean
    handleButtonClick: () => void
  }) => {
    return (
      <Button
        variant={'default'}
        onClick={handleButtonClick}
        disabled={isLoading}
        size="sm"
      >
        <GlobeIcon className="mr-2 h-4 w-4" />
        {isLoading ? '连接中...' : '连接直播间'}
      </Button>
    )
  },
)

const DisconnectButton = React.memo(
  ({ handleButtonClick }: { handleButtonClick: () => void }) => {
    const [isHovered, setIsHovered] = useState(false)
    return (
      <Button
        variant="secondary"
        onClick={handleButtonClick}
        size="sm"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {isHovered ? (
          <>
            <XIcon className="mr-2 h-4 w-4" />
            断开连接
          </>
        ) : (
          <>
            <CheckIcon className="mr-2 h-4 w-4" />
            已连接
          </>
        )}
      </Button>
    )
  },
)

const ConnectState = React.memo(() => {
  const isConnected = useCurrentLiveControl(context => context.isConnected)
  const accountName = useCurrentLiveControl(context => context.accountName)
  return (
    <div className="flex items-center gap-4">
      <div
        className={`w-2 h-2 rounded-full ${isConnected === 'connected' ? 'bg-green-500' : 'bg-muted-foreground/50'}`}
      />
      <span className="text-sm text-muted-foreground">
        {isConnected === 'connected'
          ? `已连接${accountName ? ` (${accountName})` : ''}`
          : '未连接'}
      </span>
    </div>
  )
})

export default StatusCard
