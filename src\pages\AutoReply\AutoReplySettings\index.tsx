import { Button } from '@/components/ui/button'
import { Title } from '@/components/common/Title'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { useAutoReply } from '@/hooks/useAutoReply'
import KeywordReplySimple from './KeywordReplySimple'
import AIReplyConfig from './AIReplyConfig'
import BasicSettings from './BasicSettings'
import PassiveInteractionSettings from './PassiveInteractionSettings'
import AutoMessageSettings from './AutoMessageSettings'
import AutoPopUpSettings from './AutoPopUpSettings'
import AutoTimeAnnouncementSettings from './AutoTimeAnnouncementSettings'
import { type FC, useEffect } from 'react'
import { useTabPersistence } from '@/hooks/useTabPersistence'
import { useSearchParams } from 'react-router'

// 关键词回复设置组件
const KeywordReplySettings: FC = () => {
  const { config, updateKeywordReplyEnabled, updateKeywordReplySettings } = useAutoReply()
  const keywordReplyEnabled = config.comment.keywordReply.enable
  const autoSend = config.comment.keywordReply.autoSend
  const autoSendToVoice = config.comment.keywordReply.autoSendToVoice

  const handleKeywordEnabledChange = (checked: boolean) => {
    updateKeywordReplyEnabled(checked)
  }

  const handleAutoSendChange = (checked: boolean) => {
    updateKeywordReplySettings({ autoSend: checked })
  }

  const handleAutoSendToVoiceChange = (checked: boolean) => {
    updateKeywordReplySettings({ autoSendToVoice: checked })
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Switch
          id="keyword-reply"
          checked={keywordReplyEnabled}
          onCheckedChange={handleKeywordEnabledChange}
        />
        <Label htmlFor="keyword-reply">启用关键词回复</Label>
      </div>

      {keywordReplyEnabled && (
        <div className="space-y-4">
          <div className="flex items-center space-x-2 ">
            <Switch
              id="keyword-auto-send"
              checked={autoSend}
              onCheckedChange={handleAutoSendChange}
            />
            <Label htmlFor="keyword-auto-send">自动发送到直播间评论区</Label>
          </div>

          <div className="flex items-center space-x-2 ">
            <Switch
              id="keyword-auto-send-to-voice"
              checked={autoSendToVoice}
              onCheckedChange={handleAutoSendToVoiceChange}
            />
            <Label htmlFor="keyword-auto-send-to-voice">自动发送到语音输出列表</Label>
          </div>

          <div className="text-xs text-muted-foreground pl-2 -mt-2">
            <p>
              请注意：优先使用关键词回复，没有匹配的才调用AI回复。
            </p>
          </div>

          <Card className="border-dashed">
            <CardContent className="pt-4">
              <KeywordReplyManager />
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

const Settings = () => {
  const [searchParams] = useSearchParams()
  const [activeTab, setActiveTab] = useTabPersistence('live-control-settings-tab', 'basic')

  // 处理URL参数中的tab参数
  useEffect(() => {
    const tabParam = searchParams.get('tab')
    if (tabParam) {
      setActiveTab(tabParam)
    }
  }, [searchParams, setActiveTab])

  return (
    <div className="container space-y-4">
      <div className="mb-6">
        <Title title="直播互动" description="配置自动回复的行为和监听来源" />
      </div>

      <Card>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-7">
              <TabsTrigger value="basic">基础设置</TabsTrigger>
              <TabsTrigger value="keyword">关键词回复</TabsTrigger>
              <TabsTrigger value="ai">AI回复</TabsTrigger>
              <TabsTrigger value="passive-interaction">直播间事件</TabsTrigger>
              <TabsTrigger value="auto-message">自动评论</TabsTrigger>
              <TabsTrigger value="auto-popup">自动上品</TabsTrigger>
              <TabsTrigger value="auto-time">自动报时</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 mt-6">
              <BasicSettings />
            </TabsContent>

            <TabsContent value="keyword" className="space-y-4 mt-6">
              <KeywordReplySettings />
            </TabsContent>

            <TabsContent value="ai" className="space-y-4 mt-6">
              <AIReplyConfig />
            </TabsContent>

            <TabsContent value="passive-interaction" className="space-y-4 mt-6">
              <PassiveInteractionSettings />
            </TabsContent>

            <TabsContent value="auto-message" className="space-y-4 mt-6">
              <AutoMessageSettings />
            </TabsContent>

            <TabsContent value="auto-popup" className="space-y-4 mt-6">
              <AutoPopUpSettings />
            </TabsContent>

            <TabsContent value="auto-time" className="space-y-4 mt-6">
              <AutoTimeAnnouncementSettings />
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between">
          <p className="text-xs text-muted-foreground">设置会自动保存</p>
          {/*           <div className="flex space-x-2">
            <Button variant="outline" onClick={() => navigate(-1)}>
              返回
            </Button>
          </div> */}
        </CardFooter>
      </Card>
    </div>
  )
}

// 使用简化的关键词回复管理器
const KeywordReplyManager = KeywordReplySimple


export default Settings
