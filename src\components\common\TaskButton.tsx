import { useCurrentLiveControl } from '@/hooks/useLiveControl'
import { useDebounceFn } from 'ahooks'
import { CarbonPlayFilledAlt, CarbonStopFilledAlt } from '../icons/carbon'
import { Button } from '../ui/button'
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip'
import { GlobeIcon } from 'lucide-react'
import { useCurrentChromeConfig } from '@/hooks/useChromeConfig'
import { useCurrentLiveControlActions } from '@/hooks/useLiveControl'
import { useToast } from '@/hooks/useToast'
import { useDevMode } from '@/hooks/useDevMode'
import { useMemoizedFn } from 'ahooks'
import { IPC_CHANNELS } from 'shared/ipcChannels'

export function TaskButton({
  isTaskRunning,
  onStartStop,
  forbidden = false,
}: {
  isTaskRunning: boolean
  onStartStop: () => void
  forbidden?: boolean
}) {
  const isConnected = useCurrentLiveControl(context => context.isConnected)
  const debouncedFn = useDebounceFn(onStartStop, {
    wait: 500,
    leading: true,
    trailing: false,
  })
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span>
            <Button
              variant={isTaskRunning ? 'destructive' : 'default'}
              onClick={() => debouncedFn.run()}
              disabled={forbidden || isConnected !== 'connected'}
            >
              {isTaskRunning ? (
                <>
                  <CarbonStopFilledAlt className="h-4 w-4" />
                  停止
                </>
              ) : (
                <>
                  <CarbonPlayFilledAlt className="h-4 w-4" />
                  开始
                </>
              )}
            </Button>
          </span>
        </TooltipTrigger>
        {isConnected !== 'connected' && (
          <TooltipContent>
            <p>请先连接直播间</p>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  )
}

// 小的连接直播间按钮组件
export function ConnectLiveButton() {
  const isConnected = useCurrentLiveControl(context => context.isConnected)
  const platform = useCurrentLiveControl(context => context.platform)
  const { setIsConnected, setAccountName } = useCurrentLiveControlActions()
  const chromePath = useCurrentChromeConfig(context => context.path)
  const storageState = useCurrentChromeConfig(context => context.storageState)
  const { enabled: devMode, testConnection } = useDevMode()
  const { toast } = useToast()

  const connectLiveControl = useMemoizedFn(async () => {
    try {
      setIsConnected('connecting')
      const result = await window.ipcRenderer.invoke(
        IPC_CHANNELS.tasks.liveControl.connect,
        {
          headless: !devMode,
          chromePath,
          storageState,
          platform,
          testConnection
        },
      )

      if (result) {
        setIsConnected('connected')
        setAccountName(result.accountName || '')
        toast.success(testConnection ? '已连接到测试直播间' : '已连接到直播间')
      } else {
        throw new Error('连接直播间失败')
      }
    } catch (error) {
      setIsConnected('disconnected')
      toast.error(error instanceof Error ? error.message : '连接直播间失败')
    }
  })

  // 只在未连接时显示
  if (isConnected === 'connected') {
    return null
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={connectLiveControl}
      disabled={isConnected === 'connecting'}
      className="text-xs text-muted-foreground hover:text-foreground"
    >
      <GlobeIcon className="mr-1 h-3 w-3" />
      {isConnected === 'connecting' ? '连接中...' : '连接直播间'}
    </Button>
  )
}
