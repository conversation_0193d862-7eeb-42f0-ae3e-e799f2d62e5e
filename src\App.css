#root {
  width: 100%;
  /* max-width: none; */
  margin: 0;
  padding: 0;
  text-align: left;
}

.logo-box {
  position: relative;
  height: 9em;
}

.logo {
  position: absolute;
  left: calc(50% - 4.5em);
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  .logo.electron {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
