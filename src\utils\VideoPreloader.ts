/**
 * 视频预加载管理器
 * 用于预加载数字人视频，减少播放时的卡顿
 */

export interface PreloadedVideo {
  id: string
  url: string
  text: string
  videoElement: HTMLVideoElement
  isLoaded: boolean
  loadPromise: Promise<void>
}

export class VideoPreloader {
  private preloadedVideos = new Map<string, PreloadedVideo>()
  private maxPreloadCount = 3 // 最多预加载3个视频
  private preloadContainer: HTMLElement | null = null

  constructor() {
    this.createPreloadContainer()
  }

  /**
   * 创建隐藏的预加载容器
   */
  private createPreloadContainer() {
    this.preloadContainer = document.createElement('div')
    this.preloadContainer.style.position = 'fixed'
    this.preloadContainer.style.top = '-9999px'
    this.preloadContainer.style.left = '-9999px'
    this.preloadContainer.style.width = '1px'
    this.preloadContainer.style.height = '1px'
    this.preloadContainer.style.opacity = '0'
    this.preloadContainer.style.pointerEvents = 'none'
    document.body.appendChild(this.preloadContainer)
  }

  /**
   * 预加载视频
   */
  async preloadVideo(id: string, url: string, text: string): Promise<PreloadedVideo> {
    // 如果已经预加载过，直接返回
    if (this.preloadedVideos.has(id)) {
      return this.preloadedVideos.get(id)!
    }

    // 如果预加载数量超过限制，移除最旧的
    if (this.preloadedVideos.size >= this.maxPreloadCount) {
      const oldestId = this.preloadedVideos.keys().next().value
      if (oldestId) {
        this.removePreloadedVideo(oldestId)
      }
    }

    console.log(`开始预加载视频: ${text}`)

    const videoElement = document.createElement('video')
    videoElement.preload = 'auto'
    videoElement.muted = true // 预加载时静音
    videoElement.style.width = '1px'
    videoElement.style.height = '1px'

    const preloadedVideo: PreloadedVideo = {
      id,
      url,
      text,
      videoElement,
      isLoaded: false,
      loadPromise: this.loadVideo(videoElement, url)
    }

    this.preloadedVideos.set(id, preloadedVideo)
    this.preloadContainer?.appendChild(videoElement)

    try {
      await preloadedVideo.loadPromise
      preloadedVideo.isLoaded = true
      console.log(`视频预加载完成: ${text}`)
    } catch (error) {
      console.error(`视频预加载失败: ${text}`, error)
      this.removePreloadedVideo(id)
      throw error
    }

    return preloadedVideo
  }

  /**
   * 加载视频
   */
  private loadVideo(videoElement: HTMLVideoElement, url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const onCanPlayThrough = () => {
        videoElement.removeEventListener('canplaythrough', onCanPlayThrough)
        videoElement.removeEventListener('error', onError)
        resolve()
      }

      const onError = (error: Event) => {
        videoElement.removeEventListener('canplaythrough', onCanPlayThrough)
        videoElement.removeEventListener('error', onError)
        reject(error)
      }

      videoElement.addEventListener('canplaythrough', onCanPlayThrough)
      videoElement.addEventListener('error', onError)
      
      videoElement.src = url
      videoElement.load()
    })
  }

  /**
   * 获取预加载的视频
   */
  getPreloadedVideo(id: string): PreloadedVideo | null {
    return this.preloadedVideos.get(id) || null
  }

  /**
   * 移除预加载的视频
   */
  removePreloadedVideo(id: string) {
    const preloadedVideo = this.preloadedVideos.get(id)
    if (preloadedVideo) {
      if (this.preloadContainer?.contains(preloadedVideo.videoElement)) {
        this.preloadContainer.removeChild(preloadedVideo.videoElement)
      }
      this.preloadedVideos.delete(id)
      console.log(`移除预加载视频: ${preloadedVideo.text}`)
    }
  }

  /**
   * 批量预加载视频列表
   */
  async preloadVideoList(videos: Array<{ id: string; url: string; text: string }>) {
    const preloadPromises = videos.slice(0, this.maxPreloadCount).map(video =>
      this.preloadVideo(video.id, video.url, video.text).catch(error => {
        console.error(`预加载视频失败: ${video.text}`, error)
        return null
      })
    )

    const results = await Promise.allSettled(preloadPromises)
    const successCount = results.filter(result => result.status === 'fulfilled' && result.value).length
    console.log(`批量预加载完成，成功: ${successCount}/${videos.length}`)
  }

  /**
   * 清理所有预加载的视频
   */
  cleanup() {
    for (const id of this.preloadedVideos.keys()) {
      this.removePreloadedVideo(id)
    }
    
    if (this.preloadContainer && document.body.contains(this.preloadContainer)) {
      document.body.removeChild(this.preloadContainer)
      this.preloadContainer = null
    }
  }

  /**
   * 获取预加载状态
   */
  getPreloadStatus() {
    const total = this.preloadedVideos.size
    const loaded = Array.from(this.preloadedVideos.values()).filter(v => v.isLoaded).length
    return { total, loaded }
  }
}

// 全局单例
export const videoPreloader = new VideoPreloader()
