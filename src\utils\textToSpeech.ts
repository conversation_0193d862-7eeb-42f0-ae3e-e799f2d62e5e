// 默认的 ComfyUI 服务器地址
const DEFAULT_COMFYUI_SERVER = "http://127.0.0.1:8188";

// 获取 ComfyUI 服务器地址
function getComfyUIServer(): string {
  try {
    // 从localStorage中获取用户配置的服务器地址
    const voiceSettings = localStorage.getItem('voice-settings-storage');
    if (voiceSettings) {
      const parsed = JSON.parse(voiceSettings);
      return parsed.state?.comfyUIServer || DEFAULT_COMFYUI_SERVER;
    }
  } catch (error) {
    console.warn('获取ComfyUI服务器地址失败，使用默认地址:', error);
  }
  return DEFAULT_COMFYUI_SERVER;
}

// 从选中的音色列表中随机获取一个音色文件
function getRandomSelectedVoice(speaker?: 'host' | 'assistant'): string {
  try {
    // 从localStorage中获取当前选中的音色列表
    const autoVoiceStorage = localStorage.getItem('auto-voice-storage');
    if (autoVoiceStorage) {
      const parsed = JSON.parse(autoVoiceStorage);

      // 根据说话者类型选择对应的音色列表
      let selectedVoices: string[];
      if (speaker === 'assistant') {
        selectedVoices = parsed.state?.assistantSelectedVoices || ['默认音色.wav'];
        console.log(`使用助理音色列表: ${selectedVoices.length} 个音色`);
      } else {
        // 主播或未指定时使用主播音色
        selectedVoices = parsed.state?.hostSelectedVoices || parsed.state?.selectedVoices || ['默认音色.wav'];
        console.log(`使用主播音色列表: ${selectedVoices.length} 个音色`);
      }

      if (selectedVoices.length === 0) {
        return '默认音色.wav'; // 如果没有选中的音色，使用默认音色
      }

      // 从选中的音色中随机选择一个
      const randomIndex = Math.floor(Math.random() * selectedVoices.length);
      const selectedVoice = selectedVoices[randomIndex];
      console.log(`随机选择音色: ${selectedVoice} (${speaker || 'host'}从 ${selectedVoices.length} 个音色中选择)`);
      return selectedVoice;
    }
  } catch (error) {
    console.warn('获取随机音色失败，使用默认音色:', error);
  }
  return '默认音色.wav'; // 默认音色
}

// 获取自动微调设置
function getAutoTuningEnabled(): boolean {
  try {
    const voiceSettings = localStorage.getItem('voice-settings-storage');
    if (voiceSettings) {
      const parsed = JSON.parse(voiceSettings);
      return parsed.state?.autoTuningEnabled === true;
    }
  } catch (error) {
    console.warn('获取自动微调设置失败:', error);
  }
  return false;
}

/**
 * 应用随机参数微调以降低AI检测风险
 *
 * 该功能会随机调整TTS生成参数，包括：
 * - top_k: 控制采样的词汇数量 (25-35)
 * - top_p: 核采样概率阈值 (0.75-0.85)
 * - temperature: 生成随机性 (0.95-1.05)
 * - num_beams: 束搜索数量 (2-4)
 * - max_mel_tokens: 最大梅尔频谱令牌数 (750-820)
 * - max_text_tokens_per_sentence: 每句最大文本令牌数 (85-100)
 * - sentences_bucket_max_size: 句子桶最大大小 (3-5)
 *
 * 参数调整幅度较小，确保生成质量的一致性
 */
function applyRandomParameterTuning(workflow: any): void {
  if (!getAutoTuningEnabled()) {
    return; // 如果未启用自动微调，直接返回
  }

  const ttsNode = workflow['56'];
  if (!ttsNode || !ttsNode.inputs) {
    console.warn('未找到TTS节点(ID: 56)，跳过参数微调');
    return;
  }

  // 定义参数的基础值和调整范围
  const parameterRanges = {
    top_k: { base: 30, min: 25, max: 35 },
    top_p: { base: 0.8, min: 0.75, max: 0.85 },
    temperature: { base: 1.0, min: 0.95, max: 1.05 },
    num_beams: { base: 3, min: 2, max: 4 },
    max_mel_tokens: { base: 786, min: 750, max: 820 },
    max_text_tokens_per_sentence: { base: 94, min: 85, max: 100 },
    sentences_bucket_max_size: { base: 4, min: 3, max: 5 }
  };

  // 随机调整参数
  let adjustedCount = 0;

  Object.entries(parameterRanges).forEach(([param, range]) => {
    if (ttsNode.inputs.hasOwnProperty(param)) {
      let newValue;

      if (param === 'top_p' || param === 'temperature') {
        // 浮点数参数
        newValue = range.min + Math.random() * (range.max - range.min);
        newValue = Math.round(newValue * 100) / 100; // 保留两位小数
      } else {
        // 整数参数
        newValue = Math.floor(range.min + Math.random() * (range.max - range.min + 1));
      }

      // 确保新值在有效范围内并应用
      if (newValue >= range.min && newValue <= range.max) {
        ttsNode.inputs[param] = newValue;
        adjustedCount++;
      }
    }
  });

  if (adjustedCount > 0) {
    console.log(`✓ 已应用参数微调 (${adjustedCount}个参数)，降低AI检测风险`);
  }
}

// 直接嵌入 Index-TTS.json 工作流的内容
const TTS_WORKFLOW = {
  "11": {
    "inputs": {
      "multi_line_prompt": "朋友们，我去给你们发过去了，你们剩下你们的话有什么疑问问一下我姐妹就可以了。是的，家人们，所以大家感兴趣的我们都可以放心拍大胆臀刷新咱家一号链接，去秒拍秒付就可以了。宝宝们，包括咱们新进直播间，宝宝也是一样的哥姐姐下方小黄车我们给大家挂了很多个链接的。宝宝咱们是有河粉、米粉、鸡蛋面、面条和粉丝，宝宝们大家喜欢哪个链接，咱们就拍哪个链接就可以了。如果说哥哥姐姐你是第一次来到我直播间的，喜欢吃河粉的宝宝，那你一定不要错过咱们家一号链接。宝贝错过咱们家直播间真的就没没有这样的福利了，刚吃了一碗炒面，吃了一碗炒面炒粉。",
      "speak_and_recognation": {
        "__value__": [
          false,
          true
        ]
      }
    },
    "class_type": "MultiLinePromptIndex",
    "_meta": {
      "title": "Multi Line Text"
    }
  },
  "56": {
    "inputs": {
      "version": "v1.5",
      "top_k": 30,
      "top_p": 0.8,
      "temperature": 1,
      "num_beams": 3,
      "max_mel_tokens": 786,
      "max_text_tokens_per_sentence": 94,
      "sentences_bucket_max_size": 4,
      "fast_inference": true,
      "custom_cuda_kernel": false,
      "unload_model": false,
      "audio": [
        "62",
        0
      ],
      "text": [
        "11",
        0
      ]
    },
    "class_type": "IndexTTSRun",
    "_meta": {
      "title": "IndexTTS Run"
    }
  },
  "62": {
    "inputs": {
      "audio": "默认音色.wav",
      "audioUI": ""
    },
    "class_type": "LoadAudio",
    "_meta": {
      "title": "加载音频"
    }
  },
  "64": {
    "inputs": {
      "filename_prefix": "audio/ComfyUI",
      "audioUI": "",
      "audio": [
        "56",
        0
      ]
    },
    "class_type": "SaveAudio",
    "_meta": {
      "title": "保存音频"
    }
  },
  "65": {
    "inputs": {
      "audio": [
        "56",
        0
      ]
    },
    "class_type": "Audio Duration (mtb)",
    "_meta": {
      "title": "Audio Duration (mtb)"
    }
  },
  "66": {
    "inputs": {
      "text": "39594",
      "anything": [
        "65",
        0
      ]
    },
    "class_type": "easy showAnything",
    "_meta": {
      "title": "展示任何"
    }
  }
}

/**
 * 使用 ComfyUI 服务器将文本转换为语音。
 * @param text 要转换为语音的文本。
 * @param voiceFile 可选的音色文件名，如果不提供则使用当前选中的音色。
 * @param speaker 说话者类型，用于选择对应的音色列表。
 * @returns {Promise<{audioUrl: string, duration?: number, voiceFile?: string}>} 包含音频URL、可选时长和使用的音色文件名的Promise。
 */
export async function speakText(text: string, voiceFile?: string, speaker?: 'host' | 'assistant'): Promise<{ audioUrl: string; duration?: number; voiceFile?: string }> {
    if (!text.trim()) {
        console.warn('输入文本为空，无法进行文本转语音。');
        return { audioUrl: '' };
    }

    let result_url = "";
    let audioDuration: number | undefined;
    let usedVoiceFile: string;

    try {
        // 获取服务器地址
        const serverUrl = getComfyUIServer();

        // 深拷贝工作流对象，以免修改原始定义
        const workflow = JSON.parse(JSON.stringify(TTS_WORKFLOW));

        // 将输入文本赋值给工作流中的文本输入节点 (ID: 11, 字段: multi_line_prompt)
        if (workflow['11'] && workflow['11'].inputs && workflow['11'].inputs.multi_line_prompt !== undefined) {
            workflow['11'].inputs.multi_line_prompt = text.trim();
        } else {
            throw new Error("工作流中缺少用于输入文本的节点或字段（ID: 11, multi_line_prompt）。");
        }

        // 设置音色文件 (ID: 62, 字段: audio)
        const selectedVoice = voiceFile || getRandomSelectedVoice(speaker); // 使用传入的音色文件或根据说话者随机选择的音色
        usedVoiceFile = selectedVoice; // 记录使用的音色文件
        if (workflow['62'] && workflow['62'].inputs && workflow['62'].inputs.audio !== undefined) {
            workflow['62'].inputs.audio = selectedVoice;
            console.log(`使用音色文件: ${selectedVoice}`);
        } else {
            throw new Error("工作流中缺少用于设置音色的节点或字段（ID: 62, audio）。");
        }

        // 应用随机参数微调（如果启用）
        applyRandomParameterTuning(workflow);

        console.log("正在发送 TTS 任务到 ComfyUI...");
        const promptData = {
            prompt: workflow,
            client_id: `comfyui_tts_client_${Date.now()}`,
        };

        const promptResponse = await fetch(`${serverUrl}/prompt`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(promptData)
        });

        if (!promptResponse.ok) {
            throw new Error(`发送 prompt 请求失败: ${promptResponse.status} ${promptResponse.statusText}`);
        }

        const promptResult = await promptResponse.json();
        const prompt_id = promptResult.prompt_id;
        console.log(`TTS 任务已发送，prompt_id: ${prompt_id}`);

        // 轮询直到任务完成并获取结果
        console.log("开始轮询 TTS 任务状态...");
        const pollingInterval = 1000; // 轮询间隔，1秒
        const maxAttempts = 60*60; // 最多轮询 60 次 (60秒)
        let attempts = 0;
        let taskCompleted = false;

        while (attempts < maxAttempts && !taskCompleted) {
            attempts++;
            await new Promise(resolve => setTimeout(resolve, pollingInterval));

            const historyUrl = `${serverUrl}/history/${prompt_id}`;
            const historyResponse = await fetch(historyUrl);

            if (!historyResponse.ok) {
                console.warn(`获取 TTS 任务历史失败 (${historyResponse.status})，继续重试...`);
                if (attempts === maxAttempts) {
                    throw new Error(`获取任务历史超时或失败(${historyResponse.status})`);
                }
                continue;
            }

            const history = await historyResponse.json();
            const taskOutput = history[prompt_id];

            // 检查任务是否出错或被取消
            if (taskOutput?.status?.status_str === "error" || taskOutput?.status?.status_str === "canceled") {
                const errorMessage = taskOutput.status.messages ?
                    JSON.stringify(taskOutput.status.messages) : `任务被${taskOutput.status.status_str === "error" ? "服务器报告错误" : "取消"}`;
                throw new Error(`TTS 任务 ${taskOutput.status.status_str}: ${errorMessage}`);
            }

            // 如果任务有输出，检查音频和时长
            if (taskOutput?.outputs) {
                console.log("TTS 任务完成或有输出，检查输出...");

                // 获取音频输出 (节点 ID: 64)
                const audioNodeOutput = taskOutput.outputs['64'];
                if (audioNodeOutput && audioNodeOutput.audio && audioNodeOutput.audio.length > 0) {
                    const audioData = audioNodeOutput.audio[0];
                    result_url = `${serverUrl}/view?filename=${encodeURIComponent(audioData.filename)}&type=${encodeURIComponent(audioData.type)}&subfolder=${encodeURIComponent(audioData.subfolder || '')}`;
                    console.log('成功获取音频输出 URL:', result_url);
                }

                // 获取音频时长 (节点 ID: 66)
                const durationNodeOutput = taskOutput.outputs['66'];
                if (durationNodeOutput && durationNodeOutput.text !== undefined) {
                    audioDuration = parseFloat(durationNodeOutput.text) || undefined;
                    if (audioDuration !== undefined) {
                        console.log(`提取到音频时长: ${audioDuration}秒`);
                    }
                }

                if (result_url) { // 只要获取到音频URL，就认为任务完成
                    taskCompleted = true;
                } else {
                    console.warn("任务完成但未在输出中找到预期的音频输出，请检查工作流输出节点（ID: 64）。");
                    throw new Error("TTS 任务完成但未找到音频输出。");
                }
            } else {
                // console.log(`TTS 任务 ${prompt_id} 仍在进行中... (尝试 ${attempts}/${maxAttempts})`);
            }
        }

        if (!taskCompleted) {
            throw new Error(`TTS 任务 ${prompt_id} 轮询超时，未获取到结果。`);
        }

        return { audioUrl: result_url, duration: audioDuration, voiceFile: usedVoiceFile };

    } catch (error: any) {
        console.error("处理 ComfyUI TTS 任务时发生错误:", error);
        alert(`文本转语音失败: ${error.message}`); // 弹窗提示用户
        throw error; // 向上抛出错误，让调用方处理
    }
}

/**
 * 使用随机选择的音色将文本转换为语音的便捷函数
 * @param text 要转换为语音的文本
 * @param speaker 说话者类型，用于选择对应的音色列表
 * @returns {Promise<{audioUrl: string, duration?: number, voiceFile?: string}>} 包含音频URL、可选时长和使用的音色文件名的Promise
 */
export async function speakTextWithRandomVoice(text: string, speaker?: 'host' | 'assistant'): Promise<{ audioUrl: string; duration?: number; voiceFile?: string }> {
    return speakText(text, getRandomSelectedVoice(speaker), speaker);
}

