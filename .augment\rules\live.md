---
type: "always_apply"
---

这是一个名为 OBA Live Tool 的直播带货工具，基于 React + TypeScript + Electron 开发。

主要功能
🍟 多账号管理 - 支持多组账号配置，针对不同直播间使用不同配置

🎯 智能消息助手 - 自动发送消息，告别重复机械喊话

📦 商品自动讲解 - 自动商品弹窗，随心所欲弹讲解

💃 AI 自动回复 - 实时监听直播互动评论，自动生成回复内容

🤖 AI 智能助理 - 接入 DeepSeek 等多种 AI 服务商

支持平台
抖音小店/巨量百应
抖音团购
小红书
视频号
快手小店
技术栈
前端: React + TypeScript + Vite
桌面: Electron
UI: Tailwind CSS + shadcn/ui
路由: React Router
构建: electron-builder
这是一个专为直播带货场景设计的桌面应用，通过自动化和 AI 技术提升直播效率。

请务必遵循以下要求：
1.不要安装jsdom进行测试，不需要打开浏览器，不需要npm run build测试编译，也无需创建测试代码测试，直接使用start.ps1运行编译调试即可发现错误。
2.中文交流。