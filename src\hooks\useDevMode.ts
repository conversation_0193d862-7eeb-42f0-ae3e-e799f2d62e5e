import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'

interface DevMode {
  enabled: boolean
  testConnection: boolean
  setEnabled: (enabled: boolean) => void
  setTestConnection: (testConnection: boolean) => void
}

export const useDevMode = create<DevMode>()(
  persist(
    set => ({
      enabled: false,
      testConnection: false,
      setEnabled: enabled => set({ enabled }),
      setTestConnection: testConnection => set({ testConnection }),
    }),
    {
      name: 'dev-mode-storage',
      storage: createJSONStorage(() => localStorage),
    },
  ),
)
