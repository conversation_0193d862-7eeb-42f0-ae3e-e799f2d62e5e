import AIModelInfo from '@/components/ai-chat/AIModelInfo'
import { Button } from '@/components/ui/button'
import { useAIChatStore } from '@/hooks/useAIChat'
import { TrashIcon } from 'lucide-react'
import ChatBox from '@/pages/AIChat/components/ChatBox'
import SystemPromptSettings from './SystemPromptSettings'

export default function AIChatSettings() {
  const { messages, clearMessages } = useAIChatStore()

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h3 className="text-lg font-medium">AI 助手调试</h3>
          <p className="text-sm text-muted-foreground">
            你可以在此页面设置系统提示词来测试你的AI模型
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={clearMessages}
            disabled={messages.length === 0}
            className="text-muted-foreground hover:text-destructive"
          >
            <TrashIcon className="mr-2 h-4 w-4" />
            清空对话
          </Button>
        </div>
      </div>

      <AIModelInfo />

      <SystemPromptSettings />

      <ChatBox />
    </div>
  )
}
