import React, { useRef, useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'

export interface VideoItem {
  id: string
  url: string
  text: string
  duration?: number
}

export interface SimpleMediaSourcePlayerProps {
  onVideoEnded?: (videoId: string) => void
  onError?: (error: Error, videoId?: string) => void
  className?: string
  autoPlay?: boolean
  muted?: boolean
  volume?: number
}

export interface SimpleMediaSourcePlayerRef {
  playVideo: (video: VideoItem) => Promise<void>
  stop: () => void
  getCurrentText: () => string
  isPlaying: () => boolean
}

/**
 * 简化的MediaSource播放器
 * 专门解决本地视频文件的无缝播放问题
 */
export const SimpleMediaSourcePlayer = forwardRef<SimpleMediaSourcePlayerRef, SimpleMediaSourcePlayerProps>(({
  onVideoEnded,
  onError,
  className = '',
  autoPlay = true,
  muted = false,
  volume = 1
}, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const mediaSourceRef = useRef<MediaSource | null>(null)
  const sourceBufferRef = useRef<SourceBuffer | null>(null)
  
  const [currentVideoId, setCurrentVideoId] = useState<string | null>(null)
  const [currentText, setCurrentText] = useState<string>('')
  const [isPlaying, setIsPlaying] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)

  /**
   * 检查MediaSource支持
   */
  const checkSupport = useCallback(() => {
    if (!('MediaSource' in window)) {
      return { supported: false, reason: 'MediaSource API不支持' }
    }

    // 检查基本的MP4支持
    const basicCodec = 'video/mp4; codecs="avc1.42E01E,mp4a.40.2"'
    if (!MediaSource.isTypeSupported(basicCodec)) {
      return { supported: false, reason: '不支持H.264编解码器' }
    }

    return { supported: true, codec: basicCodec }
  }, [])

  /**
   * 初始化MediaSource
   */
  const initializeMediaSource = useCallback((): Promise<void> => {
    return new Promise((resolve, reject) => {
      const support = checkSupport()
      if (!support.supported) {
        reject(new Error(support.reason))
        return
      }

      if (!videoRef.current) {
        reject(new Error('视频元素不存在'))
        return
      }

      console.log('初始化简化MediaSource播放器')

      const mediaSource = new MediaSource()
      mediaSourceRef.current = mediaSource
      
      const objectURL = URL.createObjectURL(mediaSource)
      videoRef.current.src = objectURL

      mediaSource.addEventListener('sourceopen', () => {
        console.log('MediaSource已打开')
        
        try {
          const codec = support.codec!
          const sourceBuffer = mediaSource.addSourceBuffer(codec)
          sourceBufferRef.current = sourceBuffer
          
          sourceBuffer.addEventListener('updateend', () => {
            console.log('SourceBuffer更新完成')
          })

          sourceBuffer.addEventListener('error', (e) => {
            console.error('SourceBuffer错误:', e)
            onError?.(new Error('SourceBuffer错误'))
          })

          setIsInitialized(true)
          resolve()

        } catch (error) {
          console.error('创建SourceBuffer失败:', error)
          reject(error)
        }
      })

      mediaSource.addEventListener('error', (e) => {
        console.error('MediaSource错误:', e)
        reject(new Error('MediaSource错误'))
      })
    })
  }, [checkSupport, onError])

  /**
   * 播放视频
   */
  const playVideo = useCallback(async (video: VideoItem): Promise<void> => {
    console.log('简化播放器播放视频:', video.text)

    try {
      // 如果还没初始化，先初始化
      if (!isInitialized) {
        await initializeMediaSource()
      }

      // 获取视频数据
      console.log('获取视频数据:', video.url)
      const response = await fetch(video.url)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      console.log('视频数据大小:', arrayBuffer.byteLength)

      // 添加到SourceBuffer
      const sourceBuffer = sourceBufferRef.current
      const mediaSource = mediaSourceRef.current

      if (!sourceBuffer || !mediaSource || mediaSource.readyState !== 'open') {
        throw new Error('MediaSource未准备就绪')
      }

      // 等待SourceBuffer空闲
      if (sourceBuffer.updating) {
        await new Promise<void>((resolve) => {
          const checkUpdate = () => {
            if (!sourceBuffer.updating) {
              resolve()
            } else {
              setTimeout(checkUpdate, 10)
            }
          }
          checkUpdate()
        })
      }

      // 清空现有内容
      if (sourceBuffer.buffered.length > 0) {
        const start = sourceBuffer.buffered.start(0)
        const end = sourceBuffer.buffered.end(sourceBuffer.buffered.length - 1)
        sourceBuffer.remove(start, end)
        
        // 等待清空完成
        await new Promise<void>((resolve) => {
          const onUpdateEnd = () => {
            sourceBuffer.removeEventListener('updateend', onUpdateEnd)
            resolve()
          }
          sourceBuffer.addEventListener('updateend', onUpdateEnd)
        })
      }

      // 添加新视频数据
      sourceBuffer.appendBuffer(arrayBuffer)

      // 等待添加完成
      await new Promise<void>((resolve) => {
        const onUpdateEnd = () => {
          sourceBuffer.removeEventListener('updateend', onUpdateEnd)
          resolve()
        }
        sourceBuffer.addEventListener('updateend', onUpdateEnd)
      })

      // 开始播放
      if (videoRef.current) {
        videoRef.current.currentTime = 0
        await videoRef.current.play()
        
        setCurrentVideoId(video.id)
        setCurrentText(video.text)
        setIsPlaying(true)
        
        console.log('视频播放成功:', video.text)
      }

    } catch (error) {
      console.error('播放视频失败:', error)
      onError?.(error as Error, video.id)
      throw error
    }
  }, [isInitialized, initializeMediaSource, onError])

  /**
   * 停止播放
   */
  const stop = useCallback(() => {
    if (videoRef.current) {
      videoRef.current.pause()
    }
    
    setIsPlaying(false)
    setCurrentVideoId(null)
    setCurrentText('')
    
    // 清理MediaSource
    if (mediaSourceRef.current && mediaSourceRef.current.readyState === 'open') {
      try {
        mediaSourceRef.current.endOfStream()
      } catch (error) {
        console.warn('结束MediaSource失败:', error)
      }
    }
  }, [])

  // 监听视频事件
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleEnded = () => {
      console.log('视频播放结束')
      setIsPlaying(false)
      
      if (currentVideoId) {
        onVideoEnded?.(currentVideoId)
      }
    }

    const handleError = (error: Event) => {
      console.error('视频播放出错:', error)
      setIsPlaying(false)
      onError?.(new Error('视频播放出错'))
    }

    video.addEventListener('ended', handleEnded)
    video.addEventListener('error', handleError)

    return () => {
      video.removeEventListener('ended', handleEnded)
      video.removeEventListener('error', handleError)
    }
  }, [currentVideoId, onVideoEnded, onError])

  // 设置视频属性
  useEffect(() => {
    const video = videoRef.current
    if (video) {
      video.muted = muted
      video.volume = volume
      video.autoplay = autoPlay
    }
  }, [muted, volume, autoPlay])

  // 暴露方法
  useImperativeHandle(ref, () => ({
    playVideo,
    stop,
    getCurrentText: () => currentText,
    isPlaying: () => isPlaying
  }), [playVideo, stop, currentText, isPlaying])

  const support = checkSupport()

  if (!support.supported) {
    return (
      <div className={`relative w-full h-full ${className} flex items-center justify-center bg-gray-800`}>
        <div className="text-white text-center">
          <div className="text-lg font-semibold mb-2">MediaSource不支持</div>
          <div className="text-sm text-gray-300">
            {support.reason}<br/>
            请使用支持MediaSource的现代浏览器
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative w-full h-full ${className}`}>
      <video
        ref={videoRef}
        className="w-full h-full object-contain bg-black"
        autoPlay={autoPlay}
        muted={muted}
        playsInline
        controls={false}
      />

      {/* 状态指示器 */}
      {isPlaying && (
        <div className="absolute top-4 right-4 bg-green-600 text-white text-xs px-2 py-1 rounded">
          简化MediaSource播放中
        </div>
      )}

      {/* 当前播放信息 */}
      {currentText && (
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white p-2 rounded">
          <div className="text-sm">{currentText}</div>
        </div>
      )}

      {/* 调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs p-2 rounded">
          <div>视频ID: {currentVideoId}</div>
          <div>初始化: {isInitialized ? '是' : '否'}</div>
          <div>播放中: {isPlaying ? '是' : '否'}</div>
          <div>编解码器: {support.codec}</div>
        </div>
      )}
    </div>
  )
})

SimpleMediaSourcePlayer.displayName = 'SimpleMediaSourcePlayer'

export default SimpleMediaSourcePlayer
