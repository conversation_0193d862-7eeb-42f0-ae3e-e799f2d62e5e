import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useAIChatStore } from '@/hooks/useAIChat'
import { useToast } from '@/hooks/useToast'
import { useMemoizedFn } from 'ahooks'
import { RotateCcw, Save, ChevronDown, ChevronUp } from 'lucide-react'
import { useState, useEffect } from 'react'

export default function SystemPromptSettings() {
  const { config, setSystemPrompt } = useAIChatStore()
  const [tempPrompt, setTempPrompt] = useState(config.systemPrompt)
  const [hasChanges, setHasChanges] = useState(false)
  const [isExpanded, setIsExpanded] = useState(() => {
    const saved = localStorage.getItem('system-prompt-expanded')
    return saved ? JSON.parse(saved) : false
  })
  const { toast } = useToast()

  // 监听配置变化，同步临时状态
  useEffect(() => {
    setTempPrompt(config.systemPrompt)
    setHasChanges(false)
  }, [config.systemPrompt])

  // 监听临时提示词变化
  useEffect(() => {
    setHasChanges(tempPrompt !== config.systemPrompt)
  }, [tempPrompt, config.systemPrompt])

  const handleSave = useMemoizedFn(() => {
    setSystemPrompt(tempPrompt)
    setHasChanges(false)
    toast.success('系统提示词已保存')
  })

  const handleReset = useMemoizedFn(() => {
    setTempPrompt(config.systemPrompt)
    setHasChanges(false)
  })

  const handleClear = useMemoizedFn(() => {
    setTempPrompt('')
  })

  const toggleExpanded = useMemoizedFn(() => {
    const newExpanded = !isExpanded
    setIsExpanded(newExpanded)
    localStorage.setItem('system-prompt-expanded', JSON.stringify(newExpanded))
  })

  return (
    <Card>
      <CardHeader
        className="cursor-pointer select-none"
        onClick={toggleExpanded}
      >
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>系统提示词设置</CardTitle>
            <CardDescription>
              配置AI助手的系统提示词，定义AI的角色、行为和回复风格。系统提示词会在每次对话开始时发送给AI。
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm">
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      {isExpanded && (
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="system-prompt">系统提示词内容</Label>
              <Textarea
                id="system-prompt"
                value={tempPrompt}
                onChange={(e) => setTempPrompt(e.target.value)}
                placeholder="输入系统提示词，定义AI的角色和行为..."
                className="min-h-[200px] resize-none font-mono text-sm mt-2"
              />
              <p className="text-xs text-muted-foreground mt-2">
                系统提示词将在每次对话开始时发送给AI，用于设定AI的角色、语气和行为规范。留空则不使用系统提示词。
              </p>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center gap-2">
              <Button
                onClick={handleSave}
                disabled={!hasChanges}
                size="sm"
              >
                <Save className="mr-2 h-4 w-4" />
                保存
              </Button>
              <Button
                variant="outline"
                onClick={handleReset}
                disabled={!hasChanges}
                size="sm"
              >
                <RotateCcw className="mr-2 h-4 w-4" />
                重置
              </Button>
              <Button
                variant="outline"
                onClick={handleClear}
                size="sm"
              >
                清空
              </Button>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}
