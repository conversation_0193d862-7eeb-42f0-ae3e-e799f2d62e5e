{"compilerOptions": {"target": "ESNext", "jsx": "react-jsx", "lib": ["DOM", "DOM.Iterable", "ESNext"], "useDefineForClassFields": true, "baseUrl": "./", "module": "ESNext", "moduleResolution": "Node", "paths": {"@/*": ["src/*"], "#/*": ["electron/main/*"], "shared/*": ["./shared/*"]}, "resolveJsonModule": true, "allowJs": false, "strict": true, "noEmit": true, "allowSyntheticDefaultImports": true, "esModuleInterop": false, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "references": [{"path": "./tsconfig.node.json"}], "include": ["src", "electron", "shared"]}