import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import {
  Volume2,
  VolumeX,
  Video,
  Monitor,
  Smartphone,
  User
} from 'lucide-react'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { useOBSPlayerSettings } from '@/hooks/useOBSPlayerSettings'

interface VideoPayload {
  videoUrl: string
  text: string
  id: string
  speaker: string
}

export default function HostPlayerPage() {
  const [isMuted, setIsMuted] = useState(false)
  const [volume, setVolume] = useState(1)
  const [currentVideo, setCurrentVideo] = useState<VideoPayload | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 使用持久化的横竖屏设置
  const { aspectRatio, setAspectRatio } = useOBSPlayerSettings()
  const videoRef = useRef<HTMLVideoElement>(null)

  // 监听来自主进程的播放请求
  useEffect(() => {
    const handlePlayVideo = (payload: VideoPayload) => {
      console.log('主播播放器收到播放请求:', payload)
      setCurrentVideo(payload)
      setError(null)
      playVideo(payload)
    }

    // 注册IPC监听器
    if (typeof window !== 'undefined' && window.require) {
      const { ipcRenderer } = window.require('electron')
      
      // 监听播放请求
      ipcRenderer.on('PLAY_DIGITAL_HUMAN_VIDEO', (_event: any, payload: VideoPayload) => {
        handlePlayVideo(payload)
      })

      // 清理监听器
      return () => {
        ipcRenderer.removeAllListeners('PLAY_DIGITAL_HUMAN_VIDEO')
      }
    }
  }, [])

  // 播放视频
  const playVideo = async (payload: VideoPayload) => {
    if (!videoRef.current) return

    try {
      setIsPlaying(true)
      setError(null)

      // 设置视频源
      videoRef.current.src = payload.videoUrl
      videoRef.current.muted = isMuted
      videoRef.current.volume = volume

      // 开始播放
      await videoRef.current.play()
      console.log('主播视频开始播放:', payload.text)

    } catch (error) {
      console.error('主播视频播放失败:', error)
      setError('视频播放失败: ' + (error as Error).message)
      setIsPlaying(false)
      notifyVideoEnded()
    }
  }

  // 视频播放结束处理
  const handleVideoEnded = () => {
    console.log('主播视频播放结束')
    setIsPlaying(false)
    notifyVideoEnded()
  }

  // 通知主进程视频播放结束
  const notifyVideoEnded = () => {
    if (typeof window !== 'undefined' && window.require) {
      const { ipcRenderer } = window.require('electron')
      ipcRenderer.send(IPC_CHANNELS.obsPlayer.videoEndedHost)
    }
  }

  // 视频播放错误处理
  const handleVideoError = () => {
    console.error('主播视频播放出错')
    setError('视频播放出错')
    setIsPlaying(false)
    notifyVideoEnded()
  }

  // 静音/取消静音
  const toggleMute = () => {
    const newMuted = !isMuted
    setIsMuted(newMuted)
    if (videoRef.current) {
      videoRef.current.muted = newMuted
    }
  }

  // 音量控制
  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value)
    setVolume(newVolume)
    if (videoRef.current) {
      videoRef.current.volume = newVolume
    }
  }

  // 切换窗口比例
  const toggleAspectRatio = async () => {
    const newRatio = aspectRatio === '16:9' ? '9:16' : '16:9'

    try {
      if (typeof window !== 'undefined' && window.require) {
        const { ipcRenderer } = window.require('electron')
        await ipcRenderer.invoke(IPC_CHANNELS.obsPlayer.setAspectRatio, newRatio)
        setAspectRatio(newRatio)
      }
    } catch (error) {
      console.error('切换窗口比例失败:', error)
    }
  }

  return (
    <div className="h-screen bg-black text-white flex flex-col" data-obs-capture="host-digital-human-player">
      {/* 顶部信息栏 */}
      <div className="bg-blue-900 p-2 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <User className="h-5 w-5 text-blue-300" />
          <span className="text-sm font-medium">主播数字人播放器</span>
          <span className="text-xs px-2 py-1 rounded text-white bg-blue-600">
            主播
          </span>

          {/* 比例切换按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleAspectRatio}
            className="text-white hover:bg-white hover:bg-opacity-20 h-6 px-2"
            title={`当前: ${aspectRatio}, 点击切换`}
          >
            {aspectRatio === '16:9' ? (
              <Monitor className="h-3 w-3" />
            ) : (
              <Smartphone className="h-3 w-3" />
            )}
            <span className="ml-1 text-xs">{aspectRatio}</span>
          </Button>
        </div>
        <div className="text-xs text-blue-300">
          {isPlaying ? '播放中' : '等待播放'}
        </div>
      </div>

      {/* 视频播放区域 */}
      <div className="flex-1 relative bg-black min-h-0">
        <video
          ref={videoRef}
          className="w-full h-full object-contain"
          onEnded={handleVideoEnded}
          onError={handleVideoError}
          playsInline
        />

        {/* 错误提示 */}
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-80">
            <div className="text-red-400 text-center p-4">
              <Video className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>{error}</p>
            </div>
          </div>
        )}

        {/* 等待播放提示 */}
        {!currentVideo && !error && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-80">
            <div className="text-blue-400 text-center p-4">
              <User className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>等待主播视频播放...</p>
            </div>
          </div>
        )}

        {/* 控制栏 */}
        <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-70 rounded-lg p-3 backdrop-blur-sm">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMute}
              className="text-white hover:bg-white hover:bg-opacity-20 flex-shrink-0"
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>
            
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={handleVolumeChange}
              className="flex-1 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
            />
            
            <span className="text-xs text-gray-300 min-w-[3rem]">
              {Math.round(volume * 100)}%
            </span>
          </div>

          {/* 当前播放信息 */}
          {currentVideo && (
            <div className="mt-2 pt-2 border-t border-gray-600">
              <div className="text-sm text-blue-300 font-medium truncate">
                {currentVideo.text}
              </div>
              <div className="text-xs text-gray-400 mt-1">
                主播 • {isPlaying ? '播放中' : '已暂停'}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
