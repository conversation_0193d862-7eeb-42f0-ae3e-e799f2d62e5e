import { TaskButton } from '@/components/common/TaskButton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  useAutoPopUpActions,
  useCurrentAutoPopUp,
  useShortcutListener,
} from '@/hooks/useAutoPopUp'
import { useToast } from '@/hooks/useToast'
import GoodsListCard from '@/pages/AutoPopUp/components/GoodsListCard'
import PopUpSettingsCard from '@/pages/AutoPopUp/components/PopUpSettingsCard'
import { useMemoizedFn } from 'ahooks'
import { useCallback, useState } from 'react'
import { IPC_CHANNELS } from 'shared/ipcChannels'

interface TaskControl {
  isRunning: boolean
  onStartTask: () => void
  onStopTask: () => void
}

// 实现useTaskControl hook的功能
function useTaskControl(): TaskControl {
  const isRunning = useCurrentAutoPopUp(context => context.isRunning)
  const config = useCurrentAutoPopUp(context => context.config)
  const { setIsRunning } = useAutoPopUpActions()
  const { toast } = useToast()

  const onStartTask = useCallback(async () => {
    const result = await window.ipcRenderer.invoke(
      IPC_CHANNELS.tasks.autoPopUp.start,
      config,
    )
    if (result) {
      setIsRunning(true)
      toast.success('自动弹窗任务已启动')
    } else {
      setIsRunning(false)
      toast.error('自动弹窗任务启动失败')
    }
  }, [config, setIsRunning, toast])

  const onStopTask = useCallback(async () => {
    await window.ipcRenderer.invoke(IPC_CHANNELS.tasks.autoPopUp.stop)
    setIsRunning(false)
  }, [setIsRunning])

  return {
    isRunning,
    onStartTask,
    onStopTask,
  }
}

export default function AutoPopUpSettings() {
  const { isRunning, onStartTask, onStopTask } = useTaskControl()
  const [validationError] = useState<string | null>(null)

  const handleTaskButtonClick = useMemoizedFn(() => {
    if (!isRunning) onStartTask()
    else onStopTask()
  })

  useShortcutListener()

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h3 className="text-lg font-medium">自动上品</h3>
          <p className="text-sm text-muted-foreground">
            配置自动弹出商品的规则
          </p>
        </div>
        <TaskButton
          isTaskRunning={isRunning}
          onStartStop={handleTaskButtonClick}
        />
      </div>

      {validationError && (
        <Alert variant="destructive">
          <AlertDescription>{validationError}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-6">
        <GoodsListCard />
        <PopUpSettingsCard />
      </div>
    </div>
  )
}
