import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import React from 'react'
import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'

const instructions = [
  '声音：1.用真实麦克风传输声音，或者购买声卡硬件传输声音。2.加点实时环境音。3.使用未被互联网标记的声音，多上传几个声色。4.开启声音模型自动微调。',
  '画面：1.用手机拍摄4k屏。2.用采集卡传输画面',
  '环境：电脑开播必须用纯净电脑或者虚拟机，防止直播伴侣检测进程。',
  '互动：1.每场话术清空使用后必须重新泛化，回复内容也要重新泛化；总之能泛化的地方都清空重新泛化。2.开启自动报时、主动评论、自动回复。2.开启小号制造互动',
]

interface InstructionsStore {
  checkedItems: Record<number, boolean>
  toggleItem: (index: number) => void
}

const useInstructionsStore = create<InstructionsStore>()(
  persist(
    (set) => ({
      checkedItems: {},
      toggleItem: (index: number) => {
        set((state) => ({
          checkedItems: {
            ...state.checkedItems,
            [index]: !state.checkedItems[index]
          }
        }))
      }
    }),
    {
      name: 'instructions-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
)

const InstructionsCard = React.memo(() => {
  const { checkedItems, toggleItem } = useInstructionsStore()

  return (
    <Card>
      <CardHeader>
        <CardTitle>注意事项</CardTitle>
        <CardDescription>自动回复功能目前仅对抖音小店和巨量百应开放。</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {instructions.map((instruction, index) => (
            // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
            <div className="flex gap-3 items-start" key={index}>
              <Checkbox
                checked={checkedItems[index] || false}
                onCheckedChange={() => toggleItem(index)}
                className="mt-1"
              />
              <p className="text-sm text-muted-foreground leading-6 flex-1">
                {instruction}
              </p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
})

export default InstructionsCard
