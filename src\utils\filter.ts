import { endsWith, startsWith } from 'lodash-es'

export type StringFilter = {
  eq?: string[]
  includes?: string[]
  startsWith?: string[]
  endsWith?: string[]
}

export type StringFilterConfig = {
  [field: string]: StringFilter
}

const conditionFunc: {
  [K in keyof StringFilter]: (value: string, pattern: string) => boolean
} = {
  eq: (value, pattern) => Object.is(value, pattern),
  includes: (value, pattern) => value.includes(pattern),
  startsWith: (value, pattern) => value.startsWith(pattern),
  endsWith: (value, pattern) => value.endsWith(pattern),
}

export function matchString(value: string, condition: StringFilter): boolean {
  for (const [key, patterns] of Object.entries(condition)) {
    const check = conditionFunc[key as keyof StringFilter]
    if (check && patterns?.every(pattern => !check(value, pattern))) {
      return false
    }
  }

  return true
}

export function matchObject(
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  obj: Record<string, any>,
  config: StringFilterConfig,
): boolean {
  return Object.entries(config).every(([field, condition]) =>
    matchString(String(obj[field]), condition),
  )
}

/**
 * 检查文本是否包含违禁词
 * @param text 要检查的文本
 * @param forbiddenWords 违禁词列表
 * @returns 是否包含违禁词
 */
export function containsForbiddenWords(text: string, forbiddenWords: string[]): boolean {
  if (!text || !forbiddenWords.length) return false

  const lowerText = text.toLowerCase()
  return forbiddenWords.some(word => {
    const lowerWord = word.toLowerCase()
    return lowerText.includes(lowerWord)
  })
}

/**
 * 获取文本中包含的违禁词列表
 * @param text 要检查的文本
 * @param forbiddenWords 违禁词列表
 * @returns 包含的违禁词列表
 */
export function getForbiddenWordsInText(text: string, forbiddenWords: string[]): string[] {
  if (!text || !forbiddenWords.length) return []

  const lowerText = text.toLowerCase()
  return forbiddenWords.filter(word => {
    const lowerWord = word.toLowerCase()
    return lowerText.includes(lowerWord)
  })
}
