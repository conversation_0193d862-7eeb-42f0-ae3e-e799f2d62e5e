import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useToast } from '@/hooks/useToast'
import { useHeygemServerUrl, useDigitalHumanScriptPath, useVoiceActions } from '@/hooks/useVoiceSettings'
import { Play, Square, User, FolderOpen } from 'lucide-react'
import { useEffect, useState } from 'react'
import { IPC_CHANNELS } from 'shared/ipcChannels'

interface DigitalHumanServiceStatus {
  isRunning: boolean
  pid?: number
}

const DigitalHumanServiceDialog = () => {
  const [serviceStatus, setServiceStatus] = useState<DigitalHumanServiceStatus>({ isRunning: false })
  const [isStarting, setIsStarting] = useState(false)
  const [isStopping, setIsStopping] = useState(false)
  const { toast } = useToast()

  // 数字人服务器配置
  const heygemServerUrl = useHeygemServerUrl()
  const digitalHumanScriptPath = useDigitalHumanScriptPath()
  const { setHeygemServerUrl, setDigitalHumanScriptPath } = useVoiceActions()

  // 初始化时获取服务状态，并设置定时检查
  useEffect(() => {
    checkServiceStatus()

    // 每3秒自动检查一次状态
    const interval = setInterval(checkServiceStatus, 3000)

    return () => clearInterval(interval)
  }, [])

  const checkServiceStatus = async () => {
    try {
      const status = await window.ipcRenderer.invoke(IPC_CHANNELS.digitalHumanService.status)
      setServiceStatus(status)
    } catch (error) {
      console.error('获取数字人服务状态失败:', error)
    }
  }

  const handleSelectPath = async () => {
    try {
      const selectedPath = await window.ipcRenderer.invoke(IPC_CHANNELS.digitalHumanService.selectScriptPath)
      if (selectedPath) {
        setDigitalHumanScriptPath(selectedPath)
        toast.success('数字人脚本路径已更新')
      }
    } catch (error) {
      console.error('选择脚本路径失败:', error)
      toast.error('选择脚本路径失败')
    }
  }

  const handleStartService = async () => {
    try {
      setIsStarting(true)
      const result = await window.ipcRenderer.invoke(IPC_CHANNELS.digitalHumanService.start, digitalHumanScriptPath)
      if (result.success) {
        setServiceStatus({ isRunning: true, pid: result.pid })
        toast.success(`数字人服务已启动 (PID: ${result.pid})`)
        // 启动后立即检查状态
        setTimeout(checkServiceStatus, 1000)
      } else {
        toast.error(`启动数字人服务失败: ${result.error}`)
      }
    } catch (error) {
      console.error('启动数字人服务失败:', error)
      toast.error('启动数字人服务失败')
    } finally {
      setIsStarting(false)
    }
  }

  const handleStopService = async () => {
    try {
      setIsStopping(true)
      // 使用 success 来显示信息，因为 toast 没有 info 方法
      toast.success('正在强制终止数字人服务进程...')

      const result = await window.ipcRenderer.invoke(IPC_CHANNELS.digitalHumanService.stop)
      if (result.success) {
        setServiceStatus({ isRunning: false })
        toast.success('数字人服务已停止，进程已终止')
        // 停止后立即检查状态
        setTimeout(checkServiceStatus, 1000)
      } else {
        toast.error(`停止数字人服务失败: ${result.error}`)
      }
    } catch (error) {
      console.error('停止数字人服务失败:', error)
      toast.error('停止数字人服务失败')
    } finally {
      setIsStopping(false)
    }
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          title="数字人服务器"
          className={serviceStatus.isRunning ? 'border-green-500 text-green-600' : ''}
        >
          <User className="h-4 w-4" />
          <span>数字人服务器</span>
          {serviceStatus.isRunning && (
            <div className="w-2 h-2 bg-green-500 rounded-full ml-1" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[500px]" align="center">
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <h4 className="font-medium leading-none">数字人服务器</h4>
              <p className="text-sm text-muted-foreground">
                启动和管理HeyGen数字人服务器
              </p>
            </div>

            {/* 右侧：状态和控制按钮 */}
            <div className="flex flex-col items-center gap-2">
              <div className="text-xs text-muted-foreground flex items-center gap-1">
                <span className={`inline-block w-2 h-2 rounded-full ${serviceStatus.isRunning ? 'bg-green-500' : 'bg-gray-400'}`}></span>
                <span>服务器</span>
              </div>
              {!serviceStatus.isRunning ? (
                <Button
                  size="default"
                  onClick={handleStartService}
                  disabled={isStarting || !digitalHumanScriptPath.trim()}
                  className="h-9 px-4 text-sm gap-2"
                >
                  <Play className="h-4 w-4" />
                  {isStarting ? '启动中' : '启动'}
                </Button>
              ) : (
                <Button
                  size="default"
                  variant="destructive"
                  onClick={handleStopService}
                  disabled={isStopping}
                  className="h-9 px-4 text-sm gap-2"
                >
                  <Square className="h-4 w-4" />
                  {isStopping ? '停止中' : '停止'}
                </Button>
              )}
            </div>
          </div>



          {/* 脚本路径选择 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">脚本路径</Label>
            <div className="flex items-center gap-2">
              <Input
                value={digitalHumanScriptPath}
                onChange={(e) => setDigitalHumanScriptPath(e.target.value)}
                placeholder="选择数字人服务脚本文件路径"
                className="flex-1"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectPath}
                className="shrink-0"
              >
                <FolderOpen className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Separator />

          {/* 数字人服务器配置 */}
          <div className="space-y-3">
            <div className="space-y-2">
              <Label htmlFor="heygem-server" className="text-sm font-medium">HeyGem服务器地址</Label>
              <Input
                id="heygem-server"
                type="url"
                placeholder="http://localhost:8383"
                value={heygemServerUrl}
                onChange={(e) => setHeygemServerUrl(e.target.value)}
                className="text-sm"
              />
              <p className="text-xs text-muted-foreground">
                用于数字人对口型和视频生成
              </p>
            </div>
          </div>



          {/* 说明文字 */}
          <div className="text-xs text-muted-foreground">
            <p>• 支持 Batch (.bat) 和 PowerShell (.ps1) 脚本</p>
            <p>• 脚本将在其所在目录执行</p>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}

export default DigitalHumanServiceDialog
