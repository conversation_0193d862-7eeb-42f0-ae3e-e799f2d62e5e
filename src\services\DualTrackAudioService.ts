import { VoiceItem } from '../hooks/useAutoVoice'
import { useAutoVoice } from '../hooks/useAutoVoice'
import { isAudioOutputSupported, setAudioOutputDevice } from '@/utils/audioDevices'
import { IPC_CHANNELS } from 'shared/ipcChannels'



// 基础音频播放服务类
abstract class BaseAudioService {
  protected isPlaybackRunning = false
  protected stopPlaybackSignal = false
  protected currentAudioRef: HTMLAudioElement | null = null
  protected currentVoiceId: string | null = null
  protected progressUpdateInterval: NodeJS.Timeout | null = null
  protected playbackLoop: Promise<void> | null = null


  abstract getSpeakerType(): 'host' | 'assistant'
  abstract getVoiceList(): VoiceItem[]

  async startPlayback() {
    if (this.isPlaybackRunning) {
      console.log(`${this.getSpeakerType()}语音播放已在运行中`)
      return
    }

    this.isPlaybackRunning = true
    this.stopPlaybackSignal = false
    console.log(`🎵 开始${this.getSpeakerType()}语音播放服务`)

    // 不等待播放循环，让它在后台运行
    this.playbackLoop = this.playVoiceListLoop()
  }

  async stopPlayback() {
    console.log(`⏹️ 停止${this.getSpeakerType()}语音播放服务`)
    this.stopPlaybackSignal = true

    // 清除进度更新定时器
    if (this.progressUpdateInterval) {
      clearInterval(this.progressUpdateInterval)
      this.progressUpdateInterval = null
    }

    if (this.currentAudioRef) {
      this.currentAudioRef.pause()
      this.currentAudioRef.currentTime = 0
      this.currentAudioRef = null
    }

    this.currentVoiceId = null



    // 立即设置为停止状态，不等待播放循环
    this.isPlaybackRunning = false
    console.log(`${this.getSpeakerType()}语音播放服务已停止`)

    // 异步等待播放循环结束，但不阻塞停止操作
    if (this.playbackLoop) {
      this.playbackLoop.catch(error => {
        console.error(`${this.getSpeakerType()}播放循环结束时出错:`, error)
      }).finally(() => {
        this.playbackLoop = null
      })
    }
  }

  protected async playVoiceListLoop(): Promise<void> {
    try {
      while (this.isPlaybackRunning && !this.stopPlaybackSignal) {
        // 检查停止信号
        if (this.stopPlaybackSignal) {
          console.log(`${this.getSpeakerType()}收到停止信号，退出播放循环`)
          break
        }

        // 获取当前说话者的语音列表
        const currentVoiceList = this.getVoiceList()

        if (currentVoiceList.length === 0) {
          // 等待添加语音
          for (let i = 0; i < 10 && !this.stopPlaybackSignal; i++) {
            await new Promise(resolve => setTimeout(resolve, 100))
          }
          continue
        }

        // 首先处理数字人视频项目
        const digitalHumanItems = currentVoiceList.filter(voice =>
          voice.outputType === 'digital-human' && voice.videoUrl
        )

        if (digitalHumanItems.length > 0) {
          const digitalHumanToPlay = digitalHumanItems[0]
          console.log(`${this.getSpeakerType()}开始播放数字人视频: "${digitalHumanToPlay.text}"`)

          // 播放数字人视频
          const playResult = await this.playDigitalHumanVideo(digitalHumanToPlay)

          // 播放完成后移除项目
          if (playResult === 'completed' || playResult === 'timeout') {
            console.log(`${this.getSpeakerType()}数字人视频播放完成，移除项目: ${digitalHumanToPlay.id}`)
            useAutoVoice.getState().removeVoiceItem(digitalHumanToPlay.id)
          } else {
            console.log(`${this.getSpeakerType()}数字人视频播放失败，移除项目: ${digitalHumanToPlay.id}`)
            useAutoVoice.getState().removeVoiceItem(digitalHumanToPlay.id)
          }
          continue
        }

        // 找到第一个有音频URL的语音项
        const voiceToPlay = currentVoiceList.find(voice =>
          voice.audioUrl &&
          voice.audioUrl.length > 0 &&
          voice.outputType !== 'digital-human'
        )

        if (!voiceToPlay) {
          // 等待语音生成完成
          for (let i = 0; i < 5 && !this.stopPlaybackSignal; i++) {
            await new Promise(resolve => setTimeout(resolve, 200))
          }
          continue
        }

        if (!this.isPlaybackRunning || this.stopPlaybackSignal) {
          console.log(`${this.getSpeakerType()}播放任务已中断`)
          return
        }

        // 播放语音
        await this.playVoice(voiceToPlay)

        // 在每个语音播放后检查停止信号
        if (this.stopPlaybackSignal) {
          console.log(`${this.getSpeakerType()}检测到停止信号，退出播放循环`)
          return
        }
      }
    } catch (error) {
      console.error(`${this.getSpeakerType()}播放循环出错:`, error)
    } finally {
      this.isPlaybackRunning = false
      console.log(`${this.getSpeakerType()}播放循环已结束`)
    }
  }

  protected async playVoice(voice: VoiceItem): Promise<void> {
    console.log(`🎤 ${this.getSpeakerType()}开始播放: "${voice.text}"`)

    // 设置当前播放的语音ID
    this.currentVoiceId = voice.id

    // 获取音频设置
    const state = useAutoVoice.getState()
    const audio = new Audio(voice.audioUrl)

    // 根据说话者类型获取对应的音量和播放速度设置
    let volume: number, playbackSpeed: number, deviceId: string

    if (this.getSpeakerType() === 'host') {
      volume = state.hostVolume
      playbackSpeed = state.hostPlaybackSpeed
      deviceId = state.hostDeviceId
      console.log(`🎛️ 主播音频设置: 音量${volume}%, 速度${playbackSpeed}x`)
    } else {
      volume = state.assistantVolume
      playbackSpeed = state.assistantPlaybackSpeed
      deviceId = state.assistantDeviceId
      console.log(`🎛️ 助理音频设置: 音量${volume}%, 速度${playbackSpeed}x`)
    }

    audio.volume = volume / 100
    audio.playbackRate = playbackSpeed

    if (isAudioOutputSupported() && deviceId !== 'default') {
      await setAudioOutputDevice(audio, deviceId)
    }

    this.currentAudioRef = audio

    // 开始进度更新
    this.startProgressUpdate()



    try {
      await new Promise<void>((resolve, reject) => {
        // 添加停止信号检查
        const checkStopSignal = () => {
          if (this.stopPlaybackSignal) {
            audio.pause()
            audio.currentTime = 0
            this.currentAudioRef = null
            this.currentVoiceId = null
            this.stopProgressUpdate()
            console.log(`${this.getSpeakerType()}播放被中断`)
            resolve()
            return true
          }
          return false
        }

        // 定期检查停止信号
        const stopCheckInterval = setInterval(() => {
          if (checkStopSignal()) {
            clearInterval(stopCheckInterval)
          }
        }, 100)

        audio.onended = () => {
          clearInterval(stopCheckInterval)

          this.currentAudioRef = null
          this.currentVoiceId = null
          this.stopProgressUpdate()



          console.log(`${this.getSpeakerType()}语音播放完成: "${voice.text}"`)
          resolve()
        }

        audio.onerror = (e) => {
          clearInterval(stopCheckInterval)

          this.currentAudioRef = null
          this.currentVoiceId = null
          this.stopProgressUpdate()
          console.error(`${this.getSpeakerType()}音频播放失败:`, e)
          reject(e)
        }

        audio.play().then(() => {
          // 播放开始后也检查一次停止信号
          if (checkStopSignal()) {
            clearInterval(stopCheckInterval)
          }
        }).catch(e => {
          clearInterval(stopCheckInterval)

          console.error(`${this.getSpeakerType()}音频播放API调用失败:`, e)
          this.currentAudioRef = null
          this.currentVoiceId = null
          this.stopProgressUpdate()
          reject(e)
        })
      })

      // 语音播放完成后自动移除
      if (!this.stopPlaybackSignal && this.isPlaybackRunning) {
        console.log(`${this.getSpeakerType()}语音 "${voice.text}" 播放完成，正在从列表中移除`)
        useAutoVoice.getState().removeVoiceItem(voice.id)
      }

    } catch (e) {
      console.error(`${this.getSpeakerType()}播放单个语音时发生错误或中断:`, e)
      // 播放失败时也移除这个语音项，避免卡住
      if (this.isPlaybackRunning) {
        console.log(`${this.getSpeakerType()}语音 "${voice.text}" 播放失败，从列表中移除`)
        useAutoVoice.getState().removeVoiceItem(voice.id)
      }
    }
  }

  protected startProgressUpdate() {
    if (this.progressUpdateInterval) {
      clearInterval(this.progressUpdateInterval)
    }

    this.progressUpdateInterval = setInterval(() => {
      if (this.currentAudioRef && this.currentVoiceId) {
        const currentTime = this.currentAudioRef.currentTime * 1000 // 转换为毫秒
        const duration = this.currentAudioRef.duration * 1000 // 转换为毫秒
        const progress = duration > 0 ? (currentTime / duration) * 100 : 0

        // 只有主播服务更新全局播放状态（保持向后兼容）
        if (this.getSpeakerType() === 'host') {
          useAutoVoice.getState().updatePlaybackStatus({
            isRunning: this.isPlaybackRunning,
            currentVoiceId: this.currentVoiceId,
            currentTime,
            duration,
            progress
          })
        }
      }
    }, 100) // 每100ms更新一次
  }

  protected stopProgressUpdate() {
    if (this.progressUpdateInterval) {
      clearInterval(this.progressUpdateInterval)
      this.progressUpdateInterval = null
    }

    // 只有主播服务清除全局播放状态（保持向后兼容）
    if (this.getSpeakerType() === 'host') {
      useAutoVoice.getState().updatePlaybackStatus({
        isRunning: false,
        currentVoiceId: null,
        currentTime: 0,
        duration: 0,
        progress: 0
      })
    }
  }

  // 获取当前播放状态
  getPlaybackStatus() {
    return {
      isRunning: this.isPlaybackRunning,
      currentVoiceId: this.currentVoiceId,
      currentAudio: this.currentAudioRef
    }
  }

  // 播放数字人视频
  protected async playDigitalHumanVideo(voiceItem: VoiceItem): Promise<'completed' | 'timeout' | 'failed'> {
    if (!voiceItem.videoUrl) {
      console.error(`${this.getSpeakerType()}数字人视频URL为空`)
      return 'failed'
    }

    console.log(`${this.getSpeakerType()}准备播放数字人视频:`, {
      text: voiceItem.text,
      videoUrl: voiceItem.videoUrl,
      id: voiceItem.id
    })

    try {
      // 检查IPC是否可用
      if (!(window as any).ipcRenderer) {
        console.error('IPC渲染器不可用')
        return 'failed'
      }

      // 检查是否收到停止信号
      if (this.stopPlaybackSignal) {
        console.log(`${this.getSpeakerType()}收到停止信号，跳过数字人视频播放`)
        return 'failed'
      }

      // 确保对应的播放器窗口已打开
      await this.ensurePlayerWindowOpen()



      // 通过IPC发送播放请求到对应的播放器
      const ipcChannel = this.getSpeakerType() === 'host'
        ? IPC_CHANNELS.obsPlayer.playVideoHost
        : IPC_CHANNELS.obsPlayer.playVideoAssistant

      const videoUrl = String(voiceItem.videoUrl || '')
      const text = String(voiceItem.text || '')
      const id = String(voiceItem.id || '')

      console.log(`${this.getSpeakerType()}发送播放请求:`, { videoUrl, text, id })

      // 添加超时保护
      const playPromise = (window as any).ipcRenderer.invoke(ipcChannel, videoUrl, text, id)
      const timeoutPromise = new Promise<'timeout'>((resolve) => {
        setTimeout(() => {
          console.log(`${this.getSpeakerType()}数字人视频播放超时，强制继续`)
          resolve('timeout')
        }, 35000)
      })

      const result = await Promise.race([playPromise, timeoutPromise])



      console.log(`${this.getSpeakerType()}数字人视频播放结果:`, result)
      return result === 'timeout' ? 'timeout' : 'completed'

    } catch (error) {
      console.error(`${this.getSpeakerType()}数字人视频播放失败:`, error)



      return 'failed'
    }
  }

  // 确保播放器窗口已打开
  protected async ensurePlayerWindowOpen(): Promise<void> {
    try {
      const ipcChannel = this.getSpeakerType() === 'host'
        ? IPC_CHANNELS.obsPlayer.openHost
        : IPC_CHANNELS.obsPlayer.openAssistant

      await (window as any).ipcRenderer.invoke(ipcChannel, '9:16')
      console.log(`${this.getSpeakerType()}播放器窗口已确保打开`)

      // 等待窗口完全加载
      await new Promise(resolve => setTimeout(resolve, 2000))
    } catch (error) {
      console.error(`${this.getSpeakerType()}打开播放器窗口失败:`, error)
    }
  }
}

// 主播音频服务
class HostAudioService extends BaseAudioService {
  private static instance: HostAudioService

  static getInstance(): HostAudioService {
    if (!HostAudioService.instance) {
      HostAudioService.instance = new HostAudioService()
    }
    return HostAudioService.instance
  }

  getSpeakerType(): 'host' {
    return 'host'
  }

  getVoiceList(): VoiceItem[] {
    const allVoices = useAutoVoice.getState().voiceList
    return allVoices.filter(voice => {
      const speaker = voice.speaker || 'host' // 默认为主播
      return speaker === 'host'
    })
  }

  // 监听闪避状态变化，动态调整音量
  protected async playVoice(voice: VoiceItem): Promise<void> {
    console.log(`🎤 主播开始播放: "${voice.text}"`)

    // 设置当前播放的语音ID
    this.currentVoiceId = voice.id

    // 获取音频设置
    const state = useAutoVoice.getState()
    const { hostDeviceId, hostVolume, hostPlaybackSpeed } = state
    const audio = new Audio(voice.audioUrl)

    // 设置音量和播放速度
    audio.volume = hostVolume / 100
    audio.playbackRate = hostPlaybackSpeed

    if (isAudioOutputSupported() && hostDeviceId !== 'default') {
      await setAudioOutputDevice(audio, hostDeviceId)
    }

    this.currentAudioRef = audio

    // 开始进度更新
    this.startProgressUpdate()



    try {
      await new Promise<void>((resolve, reject) => {
        // 添加停止信号检查
        const checkStopSignal = () => {
          if (this.stopPlaybackSignal) {
            audio.pause()
            audio.currentTime = 0
            this.currentAudioRef = null
            this.currentVoiceId = null
            this.stopProgressUpdate()

            console.log('主播播放被中断')
            resolve()
            return true
          }
          return false
        }

        // 定期检查停止信号
        const stopCheckInterval = setInterval(() => {
          if (checkStopSignal()) {
            clearInterval(stopCheckInterval)
          }
        }, 100)

        audio.onended = () => {
          clearInterval(stopCheckInterval)

          this.currentAudioRef = null
          this.currentVoiceId = null
          this.stopProgressUpdate()
          console.log(`主播语音播放完成: "${voice.text}"`)
          resolve()
        }

        audio.onerror = (e) => {
          clearInterval(stopCheckInterval)

          this.currentAudioRef = null
          this.currentVoiceId = null
          this.stopProgressUpdate()
          console.error('主播音频播放失败:', e)
          reject(e)
        }

        audio.play().then(() => {
          // 播放开始后也检查一次停止信号
          if (checkStopSignal()) {
            clearInterval(stopCheckInterval)
          }
        }).catch(e => {
          clearInterval(stopCheckInterval)

          console.error('主播音频播放API调用失败:', e)
          this.currentAudioRef = null
          this.currentVoiceId = null
          this.stopProgressUpdate()
          reject(e)
        })
      })

      // 语音播放完成后自动移除
      if (!this.stopPlaybackSignal && this.isPlaybackRunning) {
        console.log(`主播语音 "${voice.text}" 播放完成，正在从列表中移除`)
        useAutoVoice.getState().removeVoiceItem(voice.id)
      }

    } catch (e) {
      console.error('主播播放单个语音时发生错误或中断:', e)
      // 播放失败时也移除这个语音项，避免卡住
      if (this.isPlaybackRunning) {
        console.log(`主播语音 "${voice.text}" 播放失败，从列表中移除`)
        useAutoVoice.getState().removeVoiceItem(voice.id)
      }
    }
  }
}

// 助理音频服务
class AssistantAudioService extends BaseAudioService {
  private static instance: AssistantAudioService

  static getInstance(): AssistantAudioService {
    if (!AssistantAudioService.instance) {
      AssistantAudioService.instance = new AssistantAudioService()
    }
    return AssistantAudioService.instance
  }

  getSpeakerType(): 'assistant' {
    return 'assistant'
  }

  getVoiceList(): VoiceItem[] {
    const allVoices = useAutoVoice.getState().voiceList
    return allVoices.filter(voice => {
      const speaker = voice.speaker || 'host' // 默认为主播
      return speaker === 'assistant'
    })
  }
}

// 双轨音频播放管理器
class DualTrackAudioManager {
  private static instance: DualTrackAudioManager
  private hostService = HostAudioService.getInstance()
  private assistantService = AssistantAudioService.getInstance()


  static getInstance(): DualTrackAudioManager {
    if (!DualTrackAudioManager.instance) {
      DualTrackAudioManager.instance = new DualTrackAudioManager()
    }
    return DualTrackAudioManager.instance
  }

  // 启动双轨播放
  async startDualTrackPlayback() {
    console.log('🎵 启动双轨音频播放系统')
    await Promise.all([
      this.hostService.startPlayback(),
      this.assistantService.startPlayback()
    ])
  }

  // 停止双轨播放
  async stopDualTrackPlayback() {
    console.log('⏹️ 停止双轨音频播放系统')
    await Promise.all([
      this.hostService.stopPlayback(),
      this.assistantService.stopPlayback()
    ])
  }

  // 获取主播服务
  getHostService(): HostAudioService {
    return this.hostService
  }

  // 获取助理服务
  getAssistantService(): AssistantAudioService {
    return this.assistantService
  }

  // 获取播放状态
  getPlaybackStatus() {
    return {
      host: this.hostService.getPlaybackStatus(),
      assistant: this.assistantService.getPlaybackStatus()
    }
  }
}

export {
  BaseAudioService,
  HostAudioService,
  AssistantAudioService,
  DualTrackAudioManager
}

// 导出双轨音频播放管理器实例
export const dualTrackAudioManager = DualTrackAudioManager.getInstance()

// 将双轨播放服务暴露到全局，以便其他组件访问
;(window as any).__dualTrackAudioManager = dualTrackAudioManager

// 双轨音频管理器初始化完成
console.log('🎛️ 双轨音频管理器初始化完成')
