import { getRandomSelectedDigitalHuman } from '@/hooks/useDigitalHumanSettings'

// 获取ComfyUI视频服务器地址的函数
function getComfyUIVideoServer(): string {
  try {
    // 尝试从localStorage中获取用户配置的ComfyUI视频服务器地址
    const voiceSettings = localStorage.getItem('voice-settings-storage')
    if (voiceSettings) {
      const parsed = JSON.parse(voiceSettings)
      const comfyUIVideoServer = parsed.state?.comfyUIVideoServer
      if (comfyUIVideoServer) {
        return comfyUIVideoServer
      }
    }
  } catch (error) {
    console.warn('获取ComfyUI视频服务器地址失败，使用默认地址:', error)
  }

  // 默认地址
  return 'http://127.0.0.1:8188'
}

/**
 * 形象视频片段信息
 */
export interface AvatarVideoSegment {
  id: string
  url: string
  startTime: number // 在原始形象视频中的开始时间（秒）
  endTime: number   // 在原始形象视频中的结束时间（秒）
  duration: number  // 片段时长（秒）
  data?: ArrayBuffer // 视频数据缓存
}

/**
 * 形象视频信息
 */
export interface AvatarVideoInfo {
  filename: string
  url: string
  duration: number
  data?: ArrayBuffer
}

/**
 * 连续播放配置
 */
export interface ContinuousPlaybackConfig {
  enabled: boolean
  seamlessTransition: boolean // 是否启用无缝切换
}

/**
 * 形象视频管理器
 * 负责管理形象视频的拼接、切割和循环
 */
export class AvatarVideoManager {
  private avatarVideos: Map<string, AvatarVideoInfo> = new Map()
  private concatenatedVideo: ArrayBuffer | null = null
  private concatenatedDuration: Map<string, number> = new Map() // 分别存储主播和助理的时长
  private currentOffset: Map<string, number> = new Map() // 分别存储主播和助理的偏移量
  private config: ContinuousPlaybackConfig

  // 缓存预处理的视频信息列表（按说话者分别存储）
  private avatarInfoCache = new Map<string, Array<{ filename: string; info: any }>>()

  constructor(config: ContinuousPlaybackConfig = {
    enabled: true,
    seamlessTransition: true
  }) {
    this.config = config
  }

  /**
   * 获取视频时长
   */
  private async getVideoDuration(url: string): Promise<number> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      video.preload = 'metadata'
      
      const onLoadedMetadata = () => {
        const duration = video.duration
        video.removeEventListener('loadedmetadata', onLoadedMetadata)
        video.removeEventListener('error', onError)
        video.src = ''
        resolve(duration)
      }

      const onError = () => {
        video.removeEventListener('loadedmetadata', onLoadedMetadata)
        video.removeEventListener('error', onError)
        video.src = ''
        reject(new Error('获取视频时长失败'))
      }

      video.addEventListener('loadedmetadata', onLoadedMetadata)
      video.addEventListener('error', onError)
      video.src = url
    })
  }

  /**
   * 获取视频数据
   */
  private async fetchVideoData(url: string): Promise<ArrayBuffer> {
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      return await response.arrayBuffer()
    } catch (error) {
      console.error('获取视频数据失败:', error)
      throw error
    }
  }

  /**
   * 加载形象视频信息
   */
  private async loadAvatarVideo(filename: string): Promise<AvatarVideoInfo> {
    if (this.avatarVideos.has(filename)) {
      return this.avatarVideos.get(filename)!
    }

    try {
      const url = this.getAvatarVideoUrl(filename)
      const duration = await this.getVideoDuration(url)
      
      const avatarVideo: AvatarVideoInfo = {
        filename,
        url,
        duration
      }

      this.avatarVideos.set(filename, avatarVideo)
      console.log(`加载形象视频: ${filename}, 时长: ${duration}秒`)
      
      return avatarVideo
    } catch (error) {
      console.error(`加载形象视频失败: ${filename}`, error)
      throw error
    }
  }

  /**
   * 获取形象视频URL
   */
  private getAvatarVideoUrl(filename: string): string {
    if (filename.startsWith('http://') || filename.startsWith('https://')) {
      return filename
    }

    const comfyUIServerUrl = getComfyUIVideoServer()
    
    // 优先使用ComfyUI服务器的input目录
    return `${comfyUIServerUrl}/view?filename=${encodeURIComponent(filename)}&type=input&subfolder=`
  }

  /**
   * 预处理选中的形象视频
   * 加载所有选中的视频信息并计算正确的总时长
   */
  async preprocessAvatarVideos(speaker?: 'host' | 'assistant'): Promise<void> {
    try {
      console.log('开始预处理形象视频...')

      const speakerKey = speaker || 'host'

      // 获取选中的形象视频列表
      const selectedAvatars = this.getSelectedAvatars(speaker)
      if (selectedAvatars.length === 0) {
        throw new Error('没有选中的形象视频')
      }

      console.log(`选中的形象视频: ${selectedAvatars.join(', ')}`)

      // 加载所有选中的形象视频信息并计算总时长
      let totalDuration = 0
      const avatarInfos: Array<{ filename: string; info: any }> = []

      for (const avatarFilename of selectedAvatars) {
        const info = await this.loadAvatarVideo(avatarFilename)
        avatarInfos.push({ filename: avatarFilename, info })
        totalDuration += info.duration
      }

      // 缓存视频信息以提高性能
      this.avatarInfoCache.set(speakerKey, avatarInfos)

      // 设置正确的总时长
      this.concatenatedDuration.set(speakerKey, totalDuration)
      this.currentOffset.set(speakerKey, 0)

      console.log(`${speakerKey}形象视频预处理完成，总时长: ${totalDuration}秒 (${selectedAvatars.length}个视频)`)
      console.log(`${speakerKey}当前偏移量: ${this.currentOffset.get(speakerKey) || 0}秒`)

    } catch (error) {
      console.error('预处理形象视频失败:', error)
      throw error
    }
  }

  /**
   * 获取选中的形象视频列表
   */
  private getSelectedAvatars(speaker?: 'host' | 'assistant'): string[] {
    try {
      // 直接使用随机选择的形象，避免复杂的状态管理
      const randomAvatar = getRandomSelectedDigitalHuman(speaker)
      console.log(`使用随机选择的${speaker || 'host'}形象:`, randomAvatar)
      return [randomAvatar]
    } catch (error) {
      console.error('获取选中形象列表失败:', error)
      // 使用默认形象
      return ['9.mp4']
    }
  }

  /**
   * 创建简化的切割视频URL
   * @param startTime 开始时间（秒）
   * @param endTime 结束时间（秒）
   * @param speaker 说话者类型
   * @returns 切割后的视频URL
   */
  private async createSimpleSegmentUrl(startTime: number, endTime: number, speaker?: 'host' | 'assistant'): Promise<string> {
    try {
      // 获取原始形象视频
      const selectedAvatars = this.getSelectedAvatars(speaker)
      const avatarInfo = await this.loadAvatarVideo(selectedAvatars[0])

      // 计算在原始视频中的实际位置（处理循环）
      const videoDuration = avatarInfo.duration
      const actualStartTime = startTime % videoDuration
      const actualEndTime = Math.min(actualStartTime + (endTime - startTime), videoDuration)

      // 生成带有切割信息的URL
      const url = new URL(avatarInfo.url)
      url.searchParams.set('segment_start', actualStartTime.toString())
      url.searchParams.set('segment_end', actualEndTime.toString())
      url.searchParams.set('segment_duration', (actualEndTime - actualStartTime).toString())
      url.searchParams.set('continuous_mode', 'true')
      url.searchParams.set('original_filename', avatarInfo.filename)

      console.log(`生成简化切割URL: ${url.toString()}`)
      console.log(`实际切割范围: ${actualStartTime}s - ${actualEndTime}s`)

      return url.toString()
    } catch (error) {
      console.error('创建简化切割URL失败:', error)
      // 回退到随机选择的完整视频
      const randomAvatar = getRandomSelectedDigitalHuman(speaker)
      const avatarInfo = await this.loadAvatarVideo(randomAvatar)
      return avatarInfo.url
    }
  }

  /**
   * 根据音频时长切割形象视频片段
   * @param audioDuration 音频时长（秒）
   * @param speaker 说话者类型
   * @returns 切割后的视频片段信息
   */
  async getAvatarSegmentForAudio(audioDuration: number, speaker?: 'host' | 'assistant'): Promise<AvatarVideoSegment> {
    const speakerKey = speaker || 'host'

    console.log(`🎭 获取${speakerKey}形象视频片段，音频时长: ${audioDuration}秒`)
    console.log(`🎭 连续播放配置: ${this.config.enabled ? '启用' : '禁用'}`)

    if (!this.config.enabled) {
      // 如果未启用连续播放，返回随机选择的完整形象视频
      console.log(`🎭 连续播放未启用，使用随机${speakerKey}形象`)
      const randomAvatar = getRandomSelectedDigitalHuman(speaker)
      const avatarInfo = await this.loadAvatarVideo(randomAvatar)

      return {
        id: `${randomAvatar}-${Date.now()}`,
        url: avatarInfo.url,
        startTime: 0,
        endTime: Math.min(audioDuration, avatarInfo.duration),
        duration: Math.min(audioDuration, avatarInfo.duration)
      }
    }

    // 确保形象视频已预处理
    const currentDuration = this.concatenatedDuration.get(speakerKey) || 0
    if (currentDuration === 0) {
      await this.preprocessAvatarVideos(speaker)
    }

    // 获取当前偏移量和时长
    const currentOffset = this.currentOffset.get(speakerKey) || 0
    const concatenatedDuration = this.concatenatedDuration.get(speakerKey) || 0

    // 动态扩展视频时长以适应音频需求
    if (currentOffset + audioDuration > concatenatedDuration) {
      const newDuration = currentOffset + audioDuration + 60 // 额外增加60秒缓冲
      console.log(`需要扩展${speakerKey}形象视频时长，当前: ${concatenatedDuration}s，需要: ${newDuration}s`)
      this.concatenatedDuration.set(speakerKey, newDuration)
    }

    // 计算切割片段
    const startTime = currentOffset
    const endTime = currentOffset + audioDuration

    // 获取切割后的URL
    const segmentUrl = await this.createSimpleSegmentUrl(startTime, endTime, speaker)

    const segment: AvatarVideoSegment = {
      id: `segment-${Date.now()}`,
      url: segmentUrl,
      startTime,
      endTime,
      duration: audioDuration
    }

    // 更新当前偏移量
    this.currentOffset.set(speakerKey, endTime)

    console.log(`${speakerKey}切割形象视频片段: ${startTime}s - ${endTime}s (时长: ${audioDuration}s)`)
    console.log(`${speakerKey}更新后偏移量: ${endTime}s`)
    console.log(`切割后的视频URL: ${segmentUrl}`)

    return segment
  }

  /**
   * 获取切割后的视频片段URL
   * @param startTime 开始时间（秒）
   * @param endTime 结束时间（秒）
   * @param speaker 说话者类型
   * @returns 包含形象视频信息和切割后URL的对象
   */
  private async getSegmentVideoUrl(startTime: number, endTime: number, speaker?: 'host' | 'assistant'): Promise<{
    avatarInfo: AvatarVideoInfo
    segmentUrl: string
  }> {
    const speakerKey = speaker || 'host'
    const selectedAvatars = this.getSelectedAvatars(speaker)
    const totalDuration = this.concatenatedDuration.get(speakerKey) || 0

    // 如果总时长为0，使用第一个视频
    if (totalDuration === 0) {
      const avatarInfo = await this.loadAvatarVideo(selectedAvatars[0])
      const segmentUrl = await this.createSegmentUrl(avatarInfo.url, startTime, endTime)
      return { avatarInfo, segmentUrl }
    }

    // 使用模运算处理循环，确保时间连续性
    const normalizedStartTime = startTime % totalDuration
    const normalizedEndTime = endTime % totalDuration

    // 计算在拼接视频中的位置对应到哪个原始视频
    let currentTime = 0
    let targetAvatarInfo: AvatarVideoInfo | null = null
    let relativeStartTime = normalizedStartTime
    let relativeEndTime = normalizedEndTime

    // 使用缓存的视频信息（如果可用）
    const cachedInfos = this.avatarInfoCache.get(speakerKey)
    if (cachedInfos) {
      // 使用缓存的信息
      for (const { info } of cachedInfos) {
        if (normalizedStartTime >= currentTime && normalizedStartTime < currentTime + info.duration) {
          targetAvatarInfo = info
          relativeStartTime = normalizedStartTime - currentTime
          relativeEndTime = Math.min(normalizedEndTime - currentTime, info.duration)
          break
        }
        currentTime += info.duration
      }
    } else {
      // 遍历所有选中的形象视频，找到对应的视频和相对时间
      for (const avatarFilename of selectedAvatars) {
        const avatarInfo = await this.loadAvatarVideo(avatarFilename)

        if (normalizedStartTime >= currentTime && normalizedStartTime < currentTime + avatarInfo.duration) {
          // 找到了包含开始时间的视频
          targetAvatarInfo = avatarInfo
          relativeStartTime = normalizedStartTime - currentTime
          relativeEndTime = Math.min(normalizedEndTime - currentTime, avatarInfo.duration)
          break
        }

        currentTime += avatarInfo.duration
      }
    }

    // 如果没有找到对应的视频，使用第一个视频
    if (!targetAvatarInfo) {
      targetAvatarInfo = await this.loadAvatarVideo(selectedAvatars[0])
      relativeStartTime = startTime % targetAvatarInfo.duration
      relativeEndTime = Math.min(relativeStartTime + (endTime - startTime), targetAvatarInfo.duration)
    }

    // 生成切割后的视频URL，并添加原始文件名信息
    const segmentUrl = await this.createSegmentUrl(targetAvatarInfo.url, relativeStartTime, relativeEndTime, targetAvatarInfo.filename)

    return {
      avatarInfo: targetAvatarInfo,
      segmentUrl
    }
  }

  /**
   * 创建带有切割信息的视频URL
   * @param originalUrl 原始视频URL
   * @param startTime 开始时间（秒）
   * @param endTime 结束时间（秒）
   * @param originalFilename 原始文件名
   * @returns 带有切割信息的URL
   */
  private async createSegmentUrl(originalUrl: string, startTime: number, endTime: number, originalFilename?: string): Promise<string> {
    console.log(`准备切割视频: ${originalUrl}`)
    console.log(`时间范围: ${startTime}s - ${endTime}s (时长: ${endTime - startTime}s)`)

    // 对于连续播放，我们需要一个不同的策略
    // 由于直接切割视频在浏览器中比较复杂，我们采用以下方案：
    // 1. 在URL中添加时间信息，供后续处理使用
    // 2. 在数字人视频生成时，传递这些时间信息给ComfyUI

    try {
      const url = new URL(originalUrl)

      // 添加切割时间信息作为URL参数
      url.searchParams.set('segment_start', startTime.toString())
      url.searchParams.set('segment_end', endTime.toString())
      url.searchParams.set('segment_duration', (endTime - startTime).toString())
      url.searchParams.set('continuous_mode', 'true')

      // 如果提供了原始文件名，添加到URL参数中
      if (originalFilename) {
        url.searchParams.set('original_filename', originalFilename)
      }

      const segmentUrl = url.toString()
      console.log(`生成带切割信息的URL: ${segmentUrl}`)

      return segmentUrl
    } catch (error) {
      console.error('创建切割视频URL失败:', error)
      return originalUrl
    }
  }

  /**
   * 扩展拼接视频以满足更长的音频需求
   */
  private async extendConcatenatedVideo(requiredDuration: number, speaker?: 'host' | 'assistant'): Promise<void> {
    const speakerKey = speaker || 'host'
    const selectedAvatars = this.getSelectedAvatars(speaker)
    const avatarInfos: AvatarVideoInfo[] = []

    for (const filename of selectedAvatars) {
      const info = await this.loadAvatarVideo(filename)
      avatarInfos.push(info)
    }

    const singleLoopDuration = avatarInfos.reduce((sum, info) => sum + info.duration, 0)
    const additionalLoops = Math.ceil(requiredDuration / singleLoopDuration)

    const currentDuration = this.concatenatedDuration.get(speakerKey) || 0
    const newDuration = currentDuration + singleLoopDuration * additionalLoops
    this.concatenatedDuration.set(speakerKey, newDuration)

    console.log(`扩展${speakerKey}拼接视频，新增 ${additionalLoops} 次循环，总时长: ${newDuration}秒`)
  }

  /**
   * 重置偏移量，重新开始切割
   */
  resetOffset(speaker?: 'host' | 'assistant'): void {
    if (speaker) {
      this.currentOffset.set(speaker, 0)
      console.log(`重置${speaker}形象视频偏移量`)
    } else {
      // 重置所有
      this.currentOffset.clear()
      console.log('重置所有形象视频偏移量')
    }
  }

  /**
   * 获取当前状态信息
   */
  getStatus(speaker?: 'host' | 'assistant'): {
    concatenatedDuration: number
    currentOffset: number
    remainingDuration: number
    config: ContinuousPlaybackConfig
  } {
    const speakerKey = speaker || 'host'
    const concatenatedDuration = this.concatenatedDuration.get(speakerKey) || 0
    const currentOffset = this.currentOffset.get(speakerKey) || 0

    return {
      concatenatedDuration,
      currentOffset,
      remainingDuration: Math.max(0, concatenatedDuration - currentOffset),
      config: { ...this.config }
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ContinuousPlaybackConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.log('更新连续播放配置:', this.config)
  }

  /**
   * 清理缓存
   */
  cleanup(): void {
    this.avatarVideos.clear()
    this.concatenatedVideo = null
    this.concatenatedDuration.clear()
    this.currentOffset.clear()
    console.log('清理形象视频管理器缓存')
  }
}

// 全局形象视频管理器实例
let globalAvatarVideoManager: AvatarVideoManager | null = null

/**
 * 获取全局形象视频管理器实例
 */
export function getAvatarVideoManager(): AvatarVideoManager {
  if (!globalAvatarVideoManager) {
    globalAvatarVideoManager = new AvatarVideoManager()
  }
  return globalAvatarVideoManager
}

/**
 * 重置全局形象视频管理器
 */
export function resetAvatarVideoManager(config?: ContinuousPlaybackConfig): void {
  if (globalAvatarVideoManager) {
    globalAvatarVideoManager.cleanup()
  }
  globalAvatarVideoManager = new AvatarVideoManager(config)
}
