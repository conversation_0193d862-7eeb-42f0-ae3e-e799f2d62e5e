import type { Page } from 'playwright'
import { emitter } from '#/event/eventBus'
import { createLogger } from '../logger'
import type { BaseConfig, Scheduler } from '../tasks/scheduler'
import { accountManager } from './AccountManager'
import { contextManager } from './BrowserContextManager'

export class TaskManager {
  private tasks = new Map<string, Record<string, Scheduler>>()
  private logger = createLogger('PageManager')

  constructor() {
    emitter.on('page-closed', ({ accountId }) => {
      const tasks = this.tasks.get(accountId)
      if (tasks) {
        for (const task of Object.values(tasks)) {
          task.stop()
        }
      }
    })
  }

  register(
    taskName: string,
    creator: (page: Page, account: Account) => Scheduler,
  ) {
    const account = accountManager.getActiveAccount()
    const context = contextManager.getContext(account.id)

    let tasks = this.tasks.get(account.id)
    if (!tasks) {
      tasks = {}
      this.tasks.set(account.id, tasks)
    }

    // 任务重复？当前任务正在运行——马上停止

    if (tasks[taskName]?.isRunning) {
      tasks[taskName].stop()
    }

    const task = creator(context.page, account)
    tasks[taskName] = task
  }

  cleanup() {
    for (const tasks of this.tasks.values()) {
      for (const task of Object.values(tasks)) task.stop()
    }
    this.tasks.clear()
  }

  contains(taskName: string) {
    const account = accountManager.getActiveAccount()
    const tasks = this.tasks.get(account.id)
    return tasks && !!tasks[taskName]
  }

  async startTask(taskName: string) {
    const account = accountManager.getActiveAccount()
    const tasks = this.tasks.get(account.id)
    if (tasks?.[taskName].isRunning) {
      this.logger.warn(
        `任务 <${taskName}> 正在运行，无法重新开始 - <${account.name}>`,
      )
      return
    }
    if (tasks) {
      tasks[taskName].start()
      this.logger.info(`启动任务 <${taskName}> - <${account.name}>`)

      // 启动任务时自动启动推流
      this.startTaskStreaming()
    }
  }

  stopTask(taskName: string) {
    const account = accountManager.getActiveAccount()
    this.logger.info(`停止任务 <${taskName}> - <${account.name}>`)
    const tasks = this.tasks.get(account.id)
    if (tasks) {
      tasks[taskName].stop()
      delete tasks[taskName]

      // 检查是否还有其他任务在运行，如果没有则停止推流
      const hasRunningTasks = Object.values(tasks).some(task => task.isRunning)
      if (!hasRunningTasks) {
        this.stopTaskStreaming()
      }
    }
  }

  updateTaskConfig(taskName: string, newConfig: BaseConfig) {
    const account = accountManager.getActiveAccount()
    const tasks = this.tasks.get(account.id)
    if (tasks) {
      tasks[taskName].updateConfig(newConfig)
    }
  }

  private async startTaskStreaming() {
    try {
      // 直接导入streamServerManager
      const { streamServerManager } = await import('../ipc/streamServer')

      // 使用主播推流地址作为默认，用户可以在设置中配置
      const rtmpUrl = 'rtmp://localhost:1935/live/host'

      // 直接调用推流启动方法
      const result = await streamServerManager.startTaskStreaming(rtmpUrl)
      if (result.success) {
        this.logger.info('任务推流已启动')
      } else {
        this.logger.warn(`启动推流失败: ${result.error}`)
      }
    } catch (error) {
      this.logger.warn(`启动推流异常: ${error}`)
    }
  }

  private async stopTaskStreaming() {
    try {
      // 直接导入streamServerManager
      const { streamServerManager } = await import('../ipc/streamServer')

      // 直接调用推流停止方法
      const result = await streamServerManager.stopTaskStreaming()
      if (result.success) {
        this.logger.info('任务推流已停止')
      } else {
        this.logger.warn(`停止推流失败: ${result.error}`)
      }
    } catch (error) {
      this.logger.warn(`停止推流异常: ${error}`)
    }
  }
}

export const taskManager = new TaskManager()
