/* 主题适配的 Markdown 样式覆盖 */

/* 基础文本颜色 */
.markdown-body {
  color: inherit;
}

/* 链接颜色 */
.markdown-body a {
  color: hsl(var(--primary));
}

.markdown-body a:hover,
.markdown-body a:focus,
.markdown-body a:active {
  color: hsl(var(--primary));
  opacity: 0.8;
}

/* 标题颜色 */
.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  color: inherit;
}

.markdown-body h6 {
  color: hsl(var(--muted-foreground));
}

/* 分隔线 */
.markdown-body hr {
  background-color: hsl(var(--border));
  border-bottom: 1px solid hsl(var(--border));
}

.markdown-body h1,
.markdown-body h2 {
  border-bottom: 1px solid hsl(var(--border));
}

/* 引用块 */
.markdown-body blockquote {
  color: hsl(var(--muted-foreground));
  border-left: 4px solid hsl(var(--border));
}

/* 表格 */
.markdown-body table th,
.markdown-body table td {
  border: 1px solid hsl(var(--border));
}

.markdown-body table tr {
  background-color: hsl(var(--background));
  border-top: 1px solid hsl(var(--border));
}

.markdown-body table tr:nth-child(2n) {
  background-color: hsl(var(--muted) / 0.3);
}

/* 代码块 */
.markdown-body code {
  background-color: hsl(var(--muted) / 0.5);
  color: inherit;
}

.markdown-body .highlight pre,
.markdown-body pre {
  background-color: hsl(var(--muted) / 0.3);
  color: inherit;
}

/* 键盘按键 */
.markdown-body kbd {
  background-color: hsl(var(--muted));
  background-image: none;
  border: 1px solid hsl(var(--border));
  color: inherit;
}

/* 代码高亮主题适配 */
.dark .markdown-body .highlight {
  background: hsl(var(--background));
}

/* 暗色主题下的代码高亮颜色调整 */
.dark .markdown-body .highlight .n,
.dark .markdown-body .highlight .nn,
.dark .markdown-body .highlight .gp {
  color: hsl(var(--foreground));
}

.dark .markdown-body .highlight .c,
.dark .markdown-body .highlight .cm,
.dark .markdown-body .highlight .c1,
.dark .markdown-body .highlight .cp,
.dark .markdown-body .highlight .cs,
.dark .markdown-body .highlight .gh,
.dark .markdown-body .highlight .bp,
.dark .markdown-body .highlight .gc {
  color: hsl(var(--muted-foreground));
}

.dark .markdown-body .highlight .go,
.dark .markdown-body .highlight .w {
  color: hsl(var(--muted-foreground) / 0.7);
}

/* 错误和差异显示 */
.dark .markdown-body .highlight .err {
  color: hsl(var(--destructive-foreground));
  background-color: hsl(var(--destructive) / 0.3);
}

.dark .markdown-body .highlight .gd {
  color: hsl(var(--foreground));
  background-color: hsl(var(--destructive) / 0.2);
}

.dark .markdown-body .highlight .gi {
  color: hsl(var(--foreground));
  background-color: hsl(120 50% 50% / 0.2);
}

/* 链接图标颜色 */
.dark .markdown-body h1 .octicon-link,
.dark .markdown-body h2 .octicon-link,
.dark .markdown-body h3 .octicon-link,
.dark .markdown-body h4 .octicon-link,
.dark .markdown-body h5 .octicon-link,
.dark .markdown-body h6 .octicon-link {
  color: hsl(var(--foreground));
}

/* Highlight.js 主题切换 */
/* 在亮色主题下隐藏暗色主题样式 */
:root:not(.dark) .hljs-dark {
  display: none;
}

/* 在暗色主题下隐藏亮色主题样式并显示暗色主题样式 */
.dark .hljs {
  background: hsl(var(--muted) / 0.3) !important;
  color: hsl(var(--foreground)) !important;
}
