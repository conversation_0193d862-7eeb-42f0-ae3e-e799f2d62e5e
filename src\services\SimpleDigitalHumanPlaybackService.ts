import { IPC_CHANNELS } from 'shared/ipcChannels'

export interface DigitalHumanVideo {
  id: string
  videoUrl: string
  text: string
  speaker?: 'host' | 'assistant'
}

/**
 * 简化的数字人播放服务
 * 直接播放主播或助理的数字人视频，不需要复杂的播放列表管理
 */
export class SimpleDigitalHumanPlaybackService {
  private static instance: SimpleDigitalHumanPlaybackService
  private isInitialized = false

  static getInstance(): SimpleDigitalHumanPlaybackService {
    if (!SimpleDigitalHumanPlaybackService.instance) {
      SimpleDigitalHumanPlaybackService.instance = new SimpleDigitalHumanPlaybackService()
    }
    return SimpleDigitalHumanPlaybackService.instance
  }

  /**
   * 初始化服务
   */
  async initialize() {
    if (this.isInitialized) return

    console.log('初始化简化数字人播放服务')
    this.isInitialized = true
  }

  /**
   * 播放数字人视频
   * @param video 视频信息
   * @param speaker 说话者类型，如果video中没有指定则使用此参数
   */
  async playVideo(video: <PERSON><PERSON><PERSON><PERSON>ide<PERSON>, speaker?: 'host' | 'assistant'): Promise<void> {
    const actualSpeaker = video.speaker || speaker || 'host'
    
    console.log(`播放${actualSpeaker}数字人视频:`, {
      id: video.id,
      text: video.text,
      videoUrl: video.videoUrl,
      speaker: actualSpeaker
    })

    try {
      if (actualSpeaker === 'host') {
        await this.playHostVideo(video)
      } else {
        await this.playAssistantVideo(video)
      }
      
      console.log(`${actualSpeaker}数字人视频播放完成`)
    } catch (error) {
      console.error(`${actualSpeaker}数字人视频播放失败:`, error)
      throw error
    }
  }

  /**
   * 播放主播视频
   */
  private async playHostVideo(video: DigitalHumanVideo): Promise<void> {
    if (!window.ipcRenderer) {
      throw new Error('IPC渲染器不可用')
    }

    // 确保主播播放器窗口已打开
    await this.ensureHostPlayerOpen()

    // 播放视频
    await window.ipcRenderer.invoke(
      IPC_CHANNELS.obsPlayer.playVideoHost,
      video.videoUrl,
      video.text,
      video.id
    )
  }

  /**
   * 播放助理视频
   */
  private async playAssistantVideo(video: DigitalHumanVideo): Promise<void> {
    if (!window.ipcRenderer) {
      throw new Error('IPC渲染器不可用')
    }

    // 确保助理播放器窗口已打开
    await this.ensureAssistantPlayerOpen()

    // 播放视频
    await window.ipcRenderer.invoke(
      IPC_CHANNELS.obsPlayer.playVideoAssistant,
      video.videoUrl,
      video.text,
      video.id
    )
  }

  /**
   * 确保主播播放器窗口已打开
   */
  private async ensureHostPlayerOpen(): Promise<void> {
    try {
      await window.ipcRenderer.invoke(IPC_CHANNELS.obsPlayer.openHost, '9:16')
      console.log('主播播放器窗口已确保打开')
    } catch (error) {
      console.error('打开主播播放器窗口失败:', error)
      throw error
    }
  }

  /**
   * 确保助理播放器窗口已打开
   */
  private async ensureAssistantPlayerOpen(): Promise<void> {
    try {
      await window.ipcRenderer.invoke(IPC_CHANNELS.obsPlayer.openAssistant, '9:16')
      console.log('助理播放器窗口已确保打开')
    } catch (error) {
      console.error('打开助理播放器窗口失败:', error)
      throw error
    }
  }

  /**
   * 打开主播播放器窗口
   */
  async openHostPlayer(): Promise<void> {
    await this.ensureHostPlayerOpen()
  }

  /**
   * 打开助理播放器窗口
   */
  async openAssistantPlayer(): Promise<void> {
    await this.ensureAssistantPlayerOpen()
  }

  /**
   * 关闭主播播放器窗口
   */
  async closeHostPlayer(): Promise<void> {
    try {
      await window.ipcRenderer.invoke(IPC_CHANNELS.obsPlayer.closeHost)
      console.log('主播播放器窗口已关闭')
    } catch (error) {
      console.error('关闭主播播放器窗口失败:', error)
    }
  }

  /**
   * 关闭助理播放器窗口
   */
  async closeAssistantPlayer(): Promise<void> {
    try {
      await window.ipcRenderer.invoke(IPC_CHANNELS.obsPlayer.closeAssistant)
      console.log('助理播放器窗口已关闭')
    } catch (error) {
      console.error('关闭助理播放器窗口失败:', error)
    }
  }

  /**
   * 批量播放视频（按顺序播放）
   */
  async playVideos(videos: DigitalHumanVideo[]): Promise<void> {
    console.log(`开始批量播放${videos.length}个数字人视频`)
    
    for (const video of videos) {
      try {
        await this.playVideo(video)
      } catch (error) {
        console.error('批量播放中的视频播放失败:', error)
        // 继续播放下一个视频，不中断整个播放流程
      }
    }
    
    console.log('批量播放完成')
  }

  /**
   * 根据说话者类型分组播放视频
   */
  async playVideosBySpeaker(videos: DigitalHumanVideo[]): Promise<void> {
    const hostVideos = videos.filter(v => (v.speaker || 'host') === 'host')
    const assistantVideos = videos.filter(v => (v.speaker || 'host') === 'assistant')

    console.log(`分组播放: 主播视频${hostVideos.length}个, 助理视频${assistantVideos.length}个`)

    // 可以并行播放不同说话者的视频
    const promises: Promise<void>[] = []

    if (hostVideos.length > 0) {
      promises.push(this.playVideos(hostVideos))
    }

    if (assistantVideos.length > 0) {
      promises.push(this.playVideos(assistantVideos))
    }

    await Promise.all(promises)
    console.log('分组播放完成')
  }
}

// 全局单例
export const simpleDigitalHumanPlaybackService = SimpleDigitalHumanPlaybackService.getInstance()
