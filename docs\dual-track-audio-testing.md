# 双轨音频播放系统测试指南

## 功能概述

双轨音频播放系统实现了主播和助理音频的分离播放，支持以下特性：

1. **独立播放轨道**：主播和助理音频在不同的播放服务中独立播放
2. **音频闪避**：当助理播放时，主播音频音量自动降低到30%
3. **实时状态显示**：UI实时显示主播和助理的播放状态
4. **无缝切换**：助理停止播放时，主播音频音量自动恢复

## 测试步骤

### 1. 基础功能测试

#### 1.1 启用助理功能
1. 进入"场控设置"页面
2. 开启"助理功能"开关
3. 选择助理模式（语音助理或数字人助理）

#### 1.2 验证UI状态显示
1. 进入"语音输出"页面
2. 检查输出列表标题是否显示主播和助理状态指示器
3. 验证选项卡是否显示播放状态（蓝色=主播，绿色=助理）

### 2. 双轨播放测试

#### 2.1 主播音频播放
1. 在话术列表中添加一些话术
2. 点击话术项的"生成语音"按钮
3. 启动语音播放服务
4. 验证主播音频正常播放
5. 检查UI中主播状态指示器是否显示为播放状态（蓝色闪烁）

#### 2.2 助理音频播放
1. 确保助理功能已开启
2. 触发AI自动回复或被动互动功能
3. 验证助理音频生成并播放
4. 检查UI中助理状态指示器是否显示为播放状态（绿色闪烁）

#### 2.3 音频闪避测试
1. 让主播音频开始播放
2. 在主播播放过程中触发助理音频
3. 验证以下行为：
   - 主播音频继续播放但音量降低
   - 助理音频正常播放
   - UI显示"闪避"状态（橙色音量图标）
4. 等待助理音频播放完成
5. 验证主播音频音量恢复正常

### 3. 并发播放测试

#### 3.1 同时播放测试
1. 准备多个主播话术和助理回复
2. 快速触发多个音频生成
3. 验证主播和助理音频队列独立管理
4. 检查播放顺序是否正确

#### 3.2 频繁切换测试
1. 快速连续触发主播和助理音频
2. 验证音频闪避机制的稳定性
3. 检查UI状态更新是否及时准确

### 4. 边界情况测试

#### 4.1 助理功能关闭测试
1. 关闭助理功能
2. 验证所有音频都分配给主播
3. 检查UI是否隐藏助理相关显示

#### 4.2 音频播放中断测试
1. 在播放过程中停止语音播放服务
2. 验证主播和助理播放都正确停止
3. 检查闪避状态是否正确重置

#### 4.3 音频设备切换测试
1. 在播放过程中切换音频输出设备
2. 验证主播和助理音频都切换到新设备
3. 检查音频闪避功能是否正常

## 预期结果

### 正常情况
- 主播和助理音频可以同时播放
- 助理播放时主播音量降低到30%
- 助理停止时主播音量恢复100%
- UI实时显示播放状态和闪避状态

### 异常处理
- 音频播放失败时自动移除队列项
- 服务停止时正确清理所有状态
- 设备切换时平滑过渡

## 调试信息

系统会在控制台输出详细的调试信息：
- `🎵 开始主播/助理语音播放服务`
- `🎤 主播/助理开始播放: "文本内容"`
- `🔇 开始音频闪避：主播音量降低`
- `🔊 停止音频闪避：主播音量恢复`
- `⏹️ 停止主播/助理语音播放服务`

## 故障排除

### 常见问题
1. **音频闪避不生效**：检查助理功能是否开启
2. **UI状态不更新**：检查双轨播放管理器是否正确初始化
3. **音频播放冲突**：确保旧的VoicePlaybackService已停用

### 检查点
1. 浏览器控制台是否有错误信息
2. 双轨播放管理器是否正确暴露到全局
3. 音频设备权限是否正常
