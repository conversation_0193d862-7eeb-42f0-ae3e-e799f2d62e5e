import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Input } from '@/components/ui/input'

import { useToast } from '@/hooks/useToast'
import { useComfyUIServer, useComfyUIVideoServer, useScriptPaths, useVoiceActions, type ScriptPathItem } from '@/hooks/useVoiceSettings'
import { Play, Square, FolderOpen, Server, Plus, Trash2, Edit, X } from 'lucide-react'
import { useEffect, useState } from 'react'
import { IPC_CHANNELS } from 'shared/ipcChannels'

interface AudioServiceStatus {
  isRunning: boolean
  pid?: number
}

const AudioServiceDialog = () => {
  const [serviceStatus, setServiceStatus] = useState<AudioServiceStatus>({ isRunning: false })
  const [isStarting, setIsStarting] = useState(false)
  const [isStopping, setIsStopping] = useState(false)
  const [editingScript, setEditingScript] = useState<ScriptPathItem | null>(null)
  const [newScriptName, setNewScriptName] = useState('')
  const [newScriptPath, setNewScriptPath] = useState('')
  const [showAddScript, setShowAddScript] = useState(false)
  const { toast } = useToast()

  // ComfyUI服务器配置
  const comfyUIServer = useComfyUIServer()
  const comfyUIVideoServer = useComfyUIVideoServer()
  const scriptPaths = useScriptPaths()
  const { setComfyUIServer, setComfyUIVideoServer, addScriptPath, removeScriptPath, updateScriptPath, updateScriptPathStatus } = useVoiceActions()

  // 初始化时获取服务状态，并设置定时检查
  useEffect(() => {
    checkServiceStatus()

    // 每3秒自动检查一次状态
    const interval = setInterval(checkServiceStatus, 3000)

    return () => clearInterval(interval)
  }, [])

  const checkServiceStatus = async () => {
    try {
      // 检查全局状态
      const globalStatus = await window.ipcRenderer.invoke(IPC_CHANNELS.audioService.status)
      setServiceStatus(globalStatus)

      // 检查每个脚本的状态
      for (const script of scriptPaths) {
        try {
          const scriptStatus = await window.ipcRenderer.invoke(IPC_CHANNELS.audioService.status as any, script.path)
          if (scriptStatus.isRunning !== script.isRunning) {
            updateScriptPathStatus(script.id, scriptStatus.isRunning, scriptStatus.pid)
          }
        } catch (error) {
          console.warn(`检查脚本状态失败 (${script.name}):`, error)
        }
      }
    } catch (error) {
      console.error('获取音频服务状态失败:', error)
    }
  }

  const handleSelectPath = async (scriptId?: string) => {
    try {
      const selectedPath = await window.ipcRenderer.invoke(IPC_CHANNELS.audioService.selectScriptPath)
      if (selectedPath) {
        if (scriptId) {
          // 更新现有脚本路径
          const script = scriptPaths.find(s => s.id === scriptId)
          if (script) {
            updateScriptPath(scriptId, script.name, selectedPath)
            toast.success('脚本路径已更新')
          }
        } else {
          // 添加新脚本路径
          setNewScriptPath(selectedPath)
        }
      }
    } catch (error) {
      console.error('选择脚本路径失败:', error)
      toast.error('选择脚本路径失败')
    }
  }

  const handleStartService = async (scriptId: string) => {
    const script = scriptPaths.find(s => s.id === scriptId)
    if (!script || !script.path.trim()) {
      toast.error('请先选择脚本路径')
      return
    }

    try {
      setIsStarting(true)
      const result = await window.ipcRenderer.invoke(IPC_CHANNELS.audioService.start, script.path)
      if (result.success) {
        setServiceStatus({ isRunning: true, pid: result.pid })
        updateScriptPathStatus(scriptId, true, result.pid)
        toast.success(`音频服务已启动 (PID: ${result.pid})`)
        // 启动后立即检查状态
        setTimeout(checkServiceStatus, 1000)
      } else {
        toast.error(`启动音频服务失败: ${result.error}`)
      }
    } catch (error) {
      console.error('启动音频服务失败:', error)
      toast.error('启动音频服务失败')
    } finally {
      setIsStarting(false)
    }
  }

  const handleStopService = async (scriptId?: string) => {
    try {
      setIsStopping(true)

      if (scriptId) {
        // 停止特定脚本
        const script = scriptPaths.find(s => s.id === scriptId)
        if (!script) {
          toast.error('脚本不存在')
          return
        }

        toast.success(`正在停止脚本: ${script.name}`)
        const result = await window.ipcRenderer.invoke(IPC_CHANNELS.audioService.stop as any, script.path)

        if (result.success) {
          updateScriptPathStatus(scriptId, false)
          toast.success(`脚本 ${script.name} 已停止`)
        } else {
          toast.error(`停止脚本失败: ${result.error}`)
        }
      } else {
        // 停止所有服务
        toast.success('正在强制终止所有音频服务进程...')
        const result = await window.ipcRenderer.invoke(IPC_CHANNELS.audioService.stop)

        if (result.success) {
          setServiceStatus({ isRunning: false })
          // 更新所有脚本路径的状态为未运行
          scriptPaths.forEach(script => {
            if (script.isRunning) {
              updateScriptPathStatus(script.id, false)
            }
          })
          toast.success('所有音频服务已停止')
        } else {
          toast.error(`停止音频服务失败: ${result.error}`)
        }
      }

      // 停止后立即检查状态
      setTimeout(checkServiceStatus, 1000)
    } catch (error) {
      console.error('停止音频服务失败:', error)
      toast.error('停止音频服务失败')
    } finally {
      setIsStopping(false)
    }
  }

  // 添加脚本路径
  const handleAddScript = () => {
    if (!newScriptName.trim() || !newScriptPath.trim()) {
      toast.error('请输入脚本名称和路径')
      return
    }
    addScriptPath(newScriptName.trim(), newScriptPath.trim())
    setNewScriptName('')
    setNewScriptPath('')
    setShowAddScript(false)
    toast.success('脚本路径已添加')
  }

  // 删除脚本路径
  const handleRemoveScript = (scriptId: string) => {
    const script = scriptPaths.find(s => s.id === scriptId)
    if (script?.isRunning) {
      toast.error('无法删除正在运行的脚本')
      return
    }
    removeScriptPath(scriptId)
    toast.success('脚本路径已删除')
  }

  // 编辑脚本路径
  const handleEditScript = (script: ScriptPathItem) => {
    setEditingScript(script)
    setNewScriptName(script.name)
    setNewScriptPath(script.path)
  }

  // 保存编辑
  const handleSaveEdit = () => {
    if (!editingScript || !newScriptName.trim() || !newScriptPath.trim()) {
      toast.error('请输入脚本名称和路径')
      return
    }
    updateScriptPath(editingScript.id, newScriptName.trim(), newScriptPath.trim())
    setEditingScript(null)
    setNewScriptName('')
    setNewScriptPath('')
    toast.success('脚本路径已更新')
  }

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingScript(null)
    setNewScriptName('')
    setNewScriptPath('')
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          title="ComfyUI服务器"
          className={serviceStatus.isRunning ? 'border-green-500 text-green-600' : ''}
        >
          <Server className="h-4 w-4" />
          <span>ComfyUI服务器</span>
          {serviceStatus.isRunning && (
            <div className="w-2 h-2 bg-green-500 rounded-full ml-1" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[600px]" align="center">
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <h4 className="font-medium leading-none">ComfyUI服务器</h4>
              <p className="text-sm text-muted-foreground">
                启动和管理ComfyUI服务器，配置脚本路径
              </p>
            </div>

            {/* 右侧：全局停止按钮 */}
            {scriptPaths.some(script => script.isRunning) && (
              <Button
                size="default"
                variant="destructive"
                onClick={() => handleStopService()}
                disabled={isStopping}
                className="h-9 px-4 text-sm gap-2"
              >
                <Square className="h-4 w-4" />
                {isStopping ? '停止中' : '停止所有'}
              </Button>
            )}
          </div>

          {/* 主要内容区域：左右布局 */}
          <div className="flex gap-6">
            {/* 左侧：ComfyUI服务器配置 */}
            <div className="flex-1 space-y-3">
              <Label className="text-sm font-medium">ComfyUI服务器配置</Label>

              <div className="space-y-3">
                <div className="space-y-2">
                  <Label htmlFor="comfyui-server" className="text-sm font-medium">服务器地址（声音克隆）</Label>
                  <Input
                    id="comfyui-server"
                    type="url"
                    placeholder="http://127.0.0.1:8188"
                    value={comfyUIServer}
                    onChange={(e) => setComfyUIServer(e.target.value)}
                    className="text-sm"
                  />
                  <p className="text-xs text-muted-foreground">
                    用于音色文件上传和文本转音频
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="comfyui-video-server" className="text-sm font-medium">服务器地址（口型同步）</Label>
                  <Input
                    id="comfyui-video-server"
                    type="url"
                    placeholder="http://127.0.0.1:8188"
                    value={comfyUIVideoServer}
                    onChange={(e) => setComfyUIVideoServer(e.target.value)}
                    className="text-sm"
                  />
                  <p className="text-xs text-muted-foreground">
                    用于音频转视频的ComfyUI服务器
                  </p>
                </div>
              </div>
            </div>

            {/* 右侧：脚本路径管理 */}
            <div className="flex-1 space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">脚本路径管理</Label>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowAddScript(true)}
                  className="h-8 px-3 text-xs gap-1 opacity-70 hover:opacity-100"
                >
                  <Plus className="h-3 w-3" />
                  添加脚本
                </Button>
              </div>

              {/* 脚本路径列表 */}
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {scriptPaths.map((script) => (
                  <div key={script.id} className="border rounded-lg p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className={`inline-block w-2 h-2 rounded-full ${script.isRunning ? 'bg-green-500' : 'bg-gray-400'}`}></span>
                        <span className="font-medium text-sm">{script.name}</span>
                        {script.isRunning && script.pid && (
                          <span className="text-xs text-muted-foreground">(PID: {script.pid})</span>
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        {!script.isRunning ? (
                          <Button
                            size="sm"
                            onClick={() => handleStartService(script.id)}
                            disabled={isStarting || script.isRunning}
                            className="h-7 px-2 text-xs gap-1"
                          >
                            <Play className="h-3 w-3" />
                            启动
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleStopService(script.id)}
                            disabled={isStopping}
                            className="h-7 px-2 text-xs gap-1"
                          >
                            <Square className="h-3 w-3" />
                            停止
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleEditScript(script)}
                          disabled={script.isRunning}
                          className="h-7 w-7 p-0"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleRemoveScript(script.id)}
                          disabled={script.isRunning}
                          className="h-7 w-7 p-0 text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground truncate">
                      {script.path}
                    </div>
                  </div>
                ))}
              </div>

              {/* 添加/编辑脚本对话框 */}
              {(showAddScript || editingScript) && (
                <div className="border rounded-lg p-3 bg-muted/50 space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">
                      {editingScript ? '编辑脚本' : '添加脚本'}
                    </Label>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={editingScript ? handleCancelEdit : () => setShowAddScript(false)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="space-y-2">
                    <Input
                      placeholder="脚本名称"
                      value={newScriptName}
                      onChange={(e) => setNewScriptName(e.target.value)}
                      className="text-sm"
                    />
                    <div className="flex items-center gap-2">
                      <Input
                        placeholder="脚本路径"
                        value={newScriptPath}
                        onChange={(e) => setNewScriptPath(e.target.value)}
                        className="flex-1 text-sm"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSelectPath()}
                        className="shrink-0"
                      >
                        <FolderOpen className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={editingScript ? handleSaveEdit : handleAddScript}
                        disabled={!newScriptName.trim() || !newScriptPath.trim()}
                        className="h-8 px-3 text-xs"
                      >
                        {editingScript ? '保存' : '添加'}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={editingScript ? handleCancelEdit : () => setShowAddScript(false)}
                        className="h-8 px-3 text-xs"
                      >
                        取消
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>



          {/* 说明文字 */}
          <div className="text-xs text-muted-foreground">
            <p>• 支持 PowerShell 脚本 (.ps1)</p>
            <p>• 脚本将在其所在目录执行</p>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}

export default AudioServiceDialog
