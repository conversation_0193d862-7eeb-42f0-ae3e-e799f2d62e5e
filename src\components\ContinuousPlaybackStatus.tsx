import React, { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/useToast'
import { 
  Video, 
  Clock, 
  Play, 
  RotateCcw, 
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface ContinuousPlaybackStatusProps {
  speaker?: 'host' | 'assistant'
}

export function ContinuousPlaybackStatus({ speaker = 'host' }: ContinuousPlaybackStatusProps) {
  const { toast } = useToast()
  const [status, setStatus] = useState<any>(null)
  const [config, setConfig] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  // 获取状态信息
  const refreshStatus = async () => {
    try {
      setIsLoading(true)
      const { getContinuousPlaybackStatus } = await import('@/utils/continuousDigitalHuman')
      const currentStatus = getContinuousPlaybackStatus()
      setStatus(currentStatus)
    } catch (error) {
      console.error('获取连续播放状态失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 获取配置信息
  const loadConfig = () => {
    try {
      const saved = localStorage.getItem('continuous-playback-config')
      if (saved) {
        const parsedConfig = JSON.parse(saved)
        setConfig(parsedConfig)
      }
    } catch (error) {
      console.error('加载连续播放配置失败:', error)
    }
  }

  // 重置播放状态
  const resetPlayback = async () => {
    try {
      const { resetContinuousPlayback } = await import('@/utils/continuousDigitalHuman')
      resetContinuousPlayback()
      await refreshStatus()
      toast.success('连续播放状态已重置')
    } catch (error) {
      console.error('重置连续播放状态失败:', error)
      toast.error('重置失败')
    }
  }

  // 初始化
  useEffect(() => {
    loadConfig()
    refreshStatus()
  }, [])

  // 格式化时长显示
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 计算进度百分比
  const getProgress = () => {
    if (!status || status.concatenatedDuration === 0) return 0
    return (status.currentOffset / status.concatenatedDuration) * 100
  }

  // 如果连续播放未启用，不显示状态
  if (!config?.enabled) {
    return null
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2 h-8"
          disabled={isLoading}
        >
          <Video className="h-3 w-3" />
          <span className="text-xs">连续播放</span>
          {status && (
            <Badge 
              variant={status.remainingDuration > 0 ? "default" : "secondary"}
              className="text-xs px-1 py-0"
            >
              {formatDuration(status.remainingDuration)}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none flex items-center gap-2">
              <Video className="h-4 w-4" />
              连续播放状态
              <Badge variant="outline" className="text-xs">
                {speaker === 'host' ? '主播' : '助理'}
              </Badge>
            </h4>
            <p className="text-sm text-muted-foreground">
              形象视频连续播放的当前状态
            </p>
          </div>

          {status ? (
            <div className="space-y-3">
              {/* 进度条 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>播放进度</span>
                  <span>{Math.round(getProgress())}%</span>
                </div>
                <Progress value={getProgress()} className="h-2" />
              </div>

              {/* 状态信息 */}
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="space-y-1">
                  <div className="text-muted-foreground">总时长</div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {formatDuration(status.concatenatedDuration)}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">当前位置</div>
                  <div className="flex items-center gap-1">
                    <Play className="h-3 w-3" />
                    {formatDuration(status.currentOffset)}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">剩余时长</div>
                  <div className="flex items-center gap-1">
                    {status.remainingDuration > 0 ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <XCircle className="h-3 w-3 text-orange-500" />
                    )}
                    {formatDuration(status.remainingDuration)}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">状态</div>
                  <Badge variant={status.remainingDuration > 0 ? "default" : "secondary"}>
                    {status.remainingDuration > 0 ? '正常' : '需循环'}
                  </Badge>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center gap-2 pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshStatus}
                  disabled={isLoading}
                  className="flex items-center gap-1"
                >
                  <Info className="h-3 w-3" />
                  刷新
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetPlayback}
                  className="flex items-center gap-1"
                >
                  <RotateCcw className="h-3 w-3" />
                  重置
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              <Video className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">暂无状态信息</p>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshStatus}
                disabled={isLoading}
                className="mt-2"
              >
                {isLoading ? '加载中...' : '获取状态'}
              </Button>
            </div>
          )}

          {/* 配置信息 */}
          {config && (
            <div className="pt-2 border-t">
              <div className="text-xs text-muted-foreground space-y-1">
                <div>预加载: {config.preloadDuration}s</div>
                <div>最大拼接: {formatDuration(config.maxConcatenatedDuration)}</div>
                <div>无缝切换: {config.seamlessTransition ? '启用' : '禁用'}</div>
              </div>
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}
