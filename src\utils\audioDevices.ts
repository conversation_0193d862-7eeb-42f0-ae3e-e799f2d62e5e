// 音频设备管理工具

export interface AudioDevice {
  deviceId: string;
  label: string;
  kind: 'audiooutput' | 'audioinput';
}

/**
 * 获取所有音频输出设备
 */
export async function getAudioOutputDevices(): Promise<AudioDevice[]> {
  try {
    // 请求媒体权限以获取设备标签
    await navigator.mediaDevices.getUserMedia({ audio: true });
    
    const devices = await navigator.mediaDevices.enumerateDevices();
    
    return devices
      .filter(device => device.kind === 'audiooutput')
      .map(device => ({
        deviceId: device.deviceId,
        label: device.label || `音频输出设备 ${device.deviceId.slice(0, 8)}`,
        kind: device.kind as 'audiooutput'
      }));
  } catch (error) {
    console.error('获取音频设备失败:', error);
    return [];
  }
}

/**
 * 设置音频元素的输出设备
 */
export async function setAudioOutputDevice(audioElement: HTMLAudioElement, deviceId: string): Promise<boolean> {
  try {
    if ('setSinkId' in audioElement) {
      await (audioElement as any).setSinkId(deviceId);
      return true;
    } else {
      console.warn('当前浏览器不支持设置音频输出设备');
      return false;
    }
  } catch (error) {
    console.error('设置音频输出设备失败:', error);
    return false;
  }
}

/**
 * 检查浏览器是否支持音频输出设备选择
 */
export function isAudioOutputSupported(): boolean {
  return 'setSinkId' in HTMLAudioElement.prototype;
}
