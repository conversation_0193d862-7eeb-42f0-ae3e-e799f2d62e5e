import React, { useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import MediaSourceAdapter, { MediaSourceSeamlessPlayerRef } from '@/components/MediaSourceAdapter'
import { VideoItem } from '@/components/MediaSourceSeamlessPlayer'

/**
 * MediaSource调试页面
 * 用于调试和测试MediaSource播放器的问题
 */
export default function MediaSourceDebug() {
  const playerRef = useRef<MediaSourceSeamlessPlayerRef>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [logs, setLogs] = useState<string[]>([])

  // 使用本地测试视频
  const testVideo: VideoItem = {
    id: 'debug-test',
    url: 'http://localhost:8383/easy/file/d1f69717-a03b-4367-b521-2260c7bb9c9a-r.mp4',
    text: '调试测试视频',
    duration: 2
  }

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)])
  }

  const checkMediaSourceSupport = () => {
    addLog('=== MediaSource支持检查 ===')
    
    if (!('MediaSource' in window)) {
      addLog('❌ MediaSource API不支持')
      return
    }
    
    addLog('✅ MediaSource API支持')
    
    const codecs = [
      'video/mp4; codecs="avc1.42E01E,mp4a.40.2"',
      'video/mp4; codecs="avc1.4D401E,mp4a.40.2"',
      'video/mp4; codecs="avc1.64001E,mp4a.40.2"',
      'video/mp4',
      'video/webm; codecs="vp8,vorbis"',
      'video/webm; codecs="vp9,opus"'
    ]
    
    codecs.forEach(codec => {
      const supported = MediaSource.isTypeSupported(codec)
      addLog(`${supported ? '✅' : '❌'} ${codec}`)
    })
  }

  const testVideoAccess = async () => {
    addLog('=== 测试视频访问 ===')
    
    try {
      addLog(`测试URL: ${testVideo.url}`)
      
      const response = await fetch(testVideo.url, { method: 'HEAD' })
      addLog(`HTTP状态: ${response.status} ${response.statusText}`)
      
      const contentType = response.headers.get('content-type')
      addLog(`Content-Type: ${contentType}`)
      
      const contentLength = response.headers.get('content-length')
      addLog(`Content-Length: ${contentLength} bytes`)
      
      const acceptRanges = response.headers.get('accept-ranges')
      addLog(`Accept-Ranges: ${acceptRanges}`)
      
    } catch (error) {
      addLog(`❌ 访问失败: ${error}`)
    }
  }

  const testPlayVideo = async () => {
    if (!playerRef.current) return
    
    try {
      addLog('=== 开始播放测试 ===')
      await playerRef.current.playVideo(testVideo)
      setIsPlaying(true)
      addLog('✅ 播放开始成功')
    } catch (error) {
      addLog(`❌ 播放失败: ${error}`)
      setIsPlaying(false)
    }
  }

  const stopPlayback = () => {
    if (!playerRef.current) return
    
    playerRef.current.stop()
    setIsPlaying(false)
    addLog('播放已停止')
  }

  const clearLogs = () => {
    setLogs([])
  }

  const handleVideoEnded = (videoId: string) => {
    addLog(`视频播放结束: ${videoId}`)
    setIsPlaying(false)
  }

  const handleSegmentAdded = (videoId: string, duration: number) => {
    addLog(`✅ 片段添加成功: ${videoId} (${duration.toFixed(2)}s)`)
  }

  const handleError = (error: Error, videoId?: string) => {
    addLog(`❌ 播放错误: ${error.message} ${videoId || ''}`)
    setIsPlaying(false)
  }

  return (
    <div className="h-screen bg-gray-900 text-white flex">
      {/* 左侧控制面板 */}
      <div className="w-96 bg-gray-800 p-4 border-r border-gray-700 flex flex-col">
        <div className="mb-4">
          <h1 className="text-xl font-bold mb-2">MediaSource调试工具</h1>
          <p className="text-sm text-gray-400">
            调试和测试MediaSource播放器问题
          </p>
        </div>

        {/* 测试按钮 */}
        <div className="mb-4 space-y-2">
          <Button
            onClick={checkMediaSourceSupport}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            检查MediaSource支持
          </Button>
          
          <Button
            onClick={testVideoAccess}
            className="w-full bg-green-600 hover:bg-green-700"
          >
            测试视频访问
          </Button>
          
          <Button
            onClick={testPlayVideo}
            disabled={isPlaying}
            className="w-full bg-purple-600 hover:bg-purple-700"
          >
            测试播放视频
          </Button>
          
          <Button
            onClick={stopPlayback}
            disabled={!isPlaying}
            variant="destructive"
            className="w-full"
          >
            停止播放
          </Button>
          
          <Button
            onClick={clearLogs}
            variant="outline"
            className="w-full"
          >
            清空日志
          </Button>
        </div>

        {/* 测试信息 */}
        <div className="mb-4 p-3 bg-gray-700 rounded text-xs">
          <h4 className="font-semibold mb-2">测试视频信息：</h4>
          <div className="space-y-1 text-gray-300">
            <div>ID: {testVideo.id}</div>
            <div>文本: {testVideo.text}</div>
            <div>URL: {testVideo.url}</div>
            <div>时长: {testVideo.duration}s</div>
          </div>
        </div>

        {/* 状态显示 */}
        <div className="mb-4 p-3 bg-gray-700 rounded">
          <div className="text-sm">
            <div>播放状态: {isPlaying ? '播放中' : '已停止'}</div>
            <div>MediaSource: {'MediaSource' in window ? '支持' : '不支持'}</div>
          </div>
        </div>

        {/* 调试日志 */}
        <div className="flex-1">
          <h3 className="text-sm font-semibold mb-2">调试日志</h3>
          <div className="bg-gray-700 rounded p-2 h-full overflow-y-auto font-mono text-xs">
            {logs.map((log, index) => (
              <div key={index} className="mb-1">
                {log}
              </div>
            ))}
            {logs.length === 0 && (
              <div className="text-gray-400">点击上方按钮开始调试...</div>
            )}
          </div>
        </div>
      </div>

      {/* 右侧播放器 */}
      <div className="flex-1 relative bg-black">
        <MediaSourceAdapter
          ref={playerRef}
          className="w-full h-full"
          autoPlay={true}
          muted={false}
          volume={0.7}
          onVideoEnded={handleVideoEnded}
          onError={handleError}
          onSegmentAdded={handleSegmentAdded}
        />

        {/* 状态指示器 */}
        {isPlaying && (
          <div className="absolute top-4 right-4 bg-green-600 text-white text-sm px-3 py-1 rounded-full animate-pulse">
            调试播放中
          </div>
        )}

        {/* 调试信息覆盖层 */}
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white p-4 rounded max-w-md">
          <h3 className="font-semibold mb-2">调试说明</h3>
          <div className="text-sm text-gray-300 space-y-1">
            <p>1. 先检查MediaSource支持情况</p>
            <p>2. 测试视频文件访问是否正常</p>
            <p>3. 尝试播放视频并观察日志</p>
            <p>4. 查看控制台了解详细错误信息</p>
          </div>
        </div>
      </div>
    </div>
  )
}
