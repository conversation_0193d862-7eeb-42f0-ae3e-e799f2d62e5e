import React, { useRef, useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'

export interface VideoItem {
  id: string
  url: string
  text: string
  duration?: number
}

export interface DirectMediaSourcePlayerProps {
  onVideoEnded?: (videoId: string) => void
  onError?: (error: Error, videoId?: string) => void
  className?: string
  autoPlay?: boolean
  muted?: boolean
  volume?: number
}

export interface DirectMediaSourcePlayerRef {
  playVideo: (video: VideoItem) => Promise<void>
  stop: () => void
  getCurrentText: () => string
  isPlaying: () => boolean
}

/**
 * 直接MediaSource播放器
 * 最简单的实现，专门解决SourceBuffer兼容性问题
 */
export const DirectMediaSourcePlayer = forwardRef<DirectMediaSourcePlayerRef, DirectMediaSourcePlayerProps>(({
  onVideoEnded,
  onError,
  className = '',
  autoPlay = true,
  muted = false,
  volume = 1
}, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null)

  const [currentVideoId, setCurrentVideoId] = useState<string | null>(null)
  const [currentText, setCurrentText] = useState<string>('')
  const [isPlaying, setIsPlaying] = useState(false)

  /**
   * 直接播放视频 - 不使用MediaSource，避免兼容性问题
   */
  const playVideo = useCallback(async (video: VideoItem): Promise<void> => {
    console.log('直接播放视频:', video.text)

    if (!videoRef.current) {
      throw new Error('视频元素不存在')
    }

    try {
      // 直接设置视频源，不使用MediaSource
      videoRef.current.src = video.url
      videoRef.current.currentTime = 0

      // 等待视频准备就绪
      await new Promise<void>((resolve, reject) => {
        const onCanPlay = () => {
          videoRef.current!.removeEventListener('canplay', onCanPlay)
          videoRef.current!.removeEventListener('error', onError)
          resolve()
        }

        const onError = () => {
          videoRef.current!.removeEventListener('canplay', onCanPlay)
          videoRef.current!.removeEventListener('error', onError)
          reject(new Error('视频加载失败'))
        }

        videoRef.current!.addEventListener('canplay', onCanPlay)
        videoRef.current!.addEventListener('error', onError)
      })

      // 开始播放
      await videoRef.current.play()

      setCurrentVideoId(video.id)
      setCurrentText(video.text)
      setIsPlaying(true)

      console.log('视频播放成功:', video.text)

    } catch (error) {
      console.error('播放视频失败:', error)
      onError?.(error as Error, video.id)
      throw error
    }
  }, [onError])

  /**
   * 停止播放
   */
  const stop = useCallback(() => {
    if (videoRef.current) {
      videoRef.current.pause()
      videoRef.current.src = ''
    }

    setIsPlaying(false)
    setCurrentVideoId(null)
    setCurrentText('')
  }, [])

  // 监听视频事件
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleEnded = () => {
      console.log('视频播放结束')
      setIsPlaying(false)

      if (currentVideoId) {
        onVideoEnded?.(currentVideoId)
      }
    }

    const handleError = (error: Event) => {
      console.error('视频播放出错:', error)
      setIsPlaying(false)
      onError?.(new Error('视频播放出错'))
    }

    const handleTimeUpdate = () => {
      // 可以在这里添加进度监控
    }

    video.addEventListener('ended', handleEnded)
    video.addEventListener('error', handleError)
    video.addEventListener('timeupdate', handleTimeUpdate)

    return () => {
      video.removeEventListener('ended', handleEnded)
      video.removeEventListener('error', handleError)
      video.removeEventListener('timeupdate', handleTimeUpdate)
    }
  }, [currentVideoId, onVideoEnded, onError])

  // 设置视频属性
  useEffect(() => {
    const video = videoRef.current
    if (video) {
      video.muted = muted
      video.volume = volume
      video.autoplay = autoPlay
    }
  }, [muted, volume, autoPlay])

  // 暴露方法
  useImperativeHandle(ref, () => ({
    playVideo,
    stop,
    getCurrentText: () => currentText,
    isPlaying: () => isPlaying
  }), [playVideo, stop, currentText, isPlaying])

  return (
    <div className={`relative w-full h-full ${className} flex items-center justify-center`}>
      <video
        ref={videoRef}
        className="w-full h-full object-contain bg-black"
        autoPlay={autoPlay}
        muted={muted}
        playsInline
        controls={false}
        style={{ maxWidth: '100%', maxHeight: '100%' }}
      />

      {/* 状态指示器 */}
      {isPlaying && (
        <div className="absolute top-4 right-4 bg-blue-600 text-white text-xs px-2 py-1 rounded">
          直接播放中
        </div>
      )}

      {/* 当前播放信息 */}
      {currentText && (
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white p-2 rounded">
          <div className="text-sm">{currentText}</div>
        </div>
      )}

      {/* 调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs p-2 rounded">
          <div>视频ID: {currentVideoId}</div>
          <div>播放中: {isPlaying ? '是' : '否'}</div>
          <div>模式: 直接播放</div>
        </div>
      )}
    </div>
  )
})

DirectMediaSourcePlayer.displayName = 'DirectMediaSourcePlayer'

export default DirectMediaSourcePlayer
