import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import type { AIProvider } from '@/hooks/useAIChat'

export interface ModelsByProvider {
  [provider: string]: string[]
}

interface ModelStore {
  // 按厂商分类的模型列表
  modelsByProvider: ModelsByProvider
  // 最后更新时间
  lastUpdated: Record<string, number>
  
  // 设置某个provider的模型列表
  setModelsForProvider: (provider: AIProvider, models: string[]) => void
  
  // 获取某个provider的模型列表
  getModelsForProvider: (provider: AIProvider) => string[]
  
  // 清空某个provider的模型列表
  clearModelsForProvider: (provider: AIProvider) => void
  
  // 清空所有模型列表
  clearAllModels: () => void
  
  // 检查是否有缓存的模型
  hasModelsForProvider: (provider: AIProvider) => boolean
  
  // 获取最后更新时间
  getLastUpdated: (provider: AIProvider) => number | undefined
}

export const useModelStore = create<ModelStore>()(
  persist(
    immer((set, get) => ({
      modelsByProvider: {},
      lastUpdated: {},
      
      setModelsForProvider: (provider: AIProvider, models: string[]) => {
        set(state => {
          state.modelsByProvider[provider] = models
          state.lastUpdated[provider] = Date.now()
        })
      },
      
      getModelsForProvider: (provider: AIProvider) => {
        const state = get()
        return state.modelsByProvider[provider] || []
      },
      
      clearModelsForProvider: (provider: AIProvider) => {
        set(state => {
          delete state.modelsByProvider[provider]
          delete state.lastUpdated[provider]
        })
      },
      
      clearAllModels: () => {
        set(state => {
          state.modelsByProvider = {}
          state.lastUpdated = {}
        })
      },
      
      hasModelsForProvider: (provider: AIProvider) => {
        const state = get()
        const models = state.modelsByProvider[provider]
        return models && models.length > 0
      },
      
      getLastUpdated: (provider: AIProvider) => {
        const state = get()
        return state.lastUpdated[provider]
      },
    })),
    {
      name: 'model-store',
      partialize: (state) => ({
        modelsByProvider: state.modelsByProvider,
        lastUpdated: state.lastUpdated,
      }),
    }
  )
)
