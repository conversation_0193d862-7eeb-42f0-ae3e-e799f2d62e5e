import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { type TimeAnnouncementMessage } from '@/hooks/useAutoTimeAnnouncement'
import { Trash2Icon } from 'lucide-react'
import { useMemoizedFn } from 'ahooks'
import React from 'react'

interface TimeAnnouncementMessageCompProps {
  message: TimeAnnouncementMessage
  onChange: (message: TimeAnnouncementMessage) => void
  onDelete: (id: string) => void
}

const TimeAnnouncementMessageComp = React.memo<TimeAnnouncementMessageCompProps>(
  ({ message, onChange, onDelete }) => {
    const handleContentChange = useMemoizedFn((content: string) => {
      onChange({ ...message, content })
    })

    const handleEnabledChange = useMemoizedFn((enabled: boolean) => {
      onChange({ ...message, enabled })
    })

    const handleDelete = useMemoizedFn(() => {
      onDelete(message.id)
    })

    return (
      <div className="flex items-center gap-3 p-4 border rounded-lg">
        <Switch
          checked={message.enabled}
          onCheckedChange={handleEnabledChange}
          className="shrink-0"
        />
        
        <Input
          value={message.content}
          onChange={e => handleContentChange(e.target.value)}
          placeholder="输入报时消息内容，可使用 {时间} 占位符"
          className="flex-1"
        />
        
        <Button
          variant="ghost"
          size="icon"
          onClick={handleDelete}
          className="shrink-0 text-destructive hover:text-destructive"
        >
          <Trash2Icon className="h-4 w-4" />
        </Button>
      </div>
    )
  },
)

export default TimeAnnouncementMessageComp
