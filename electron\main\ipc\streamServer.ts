import { spawn, type ChildProcess, exec } from 'node:child_process'
import { promisify } from 'node:util'
import { existsSync } from 'node:fs'
import { join } from 'node:path'
import { createConnection } from 'node:net'
import { ipcMain } from 'electron'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { createLogger } from '#/logger'
import windowManager from '../windowManager'

const execAsync = promisify(exec)
const logger = createLogger('StreamServer')

// RTMP服务器管理器
class StreamServerManager {
  private static instance: StreamServerManager
  private rtmpServer: ChildProcess | null = null
  private streamProcesses: Map<string, ChildProcess> = new Map() // 按说话人分组的推流进程
  private streamTimeouts: Map<string, NodeJS.Timeout> = new Map() // 按说话人分组的超时定时器
  private streamBuffers: Map<string, any[]> = new Map() // 按说话人分组的推流缓冲队列
  private preStreamProcesses: Map<string, ChildProcess> = new Map() // 预推流进程
  private serverPort: number = 1935
  private isStreaming: boolean = false
  private voiceListMonitorInterval: NodeJS.Timeout | null = null
  private currentVoiceList: any[] = []
  private currentStreamIndex: number = 0

  private constructor() {}

  public static getInstance() {
    if (!StreamServerManager.instance) {
      StreamServerManager.instance = new StreamServerManager()
    }
    return StreamServerManager.instance
  }

  public getStatus() {
    return {
      isRunning: this.rtmpServer !== null && !this.rtmpServer.killed,
      pid: this.rtmpServer?.pid
    }
  }

  public getStreamStatus() {
    const activePids = Array.from(this.streamProcesses.values())
      .filter(process => process && !process.killed)
      .map(process => process.pid)
      .filter(pid => pid !== undefined)

    return {
      isStreaming: this.isStreaming,
      activePids,
      activeProcessCount: activePids.length
    }
  }

  public async startServer(options: { port: number }): Promise<{ success: boolean; error?: string; pid?: number }> {
    try {
      // 如果已有服务器在运行，先停止
      if (this.rtmpServer && !this.rtmpServer.killed) {
        logger.info('停止现有RTMP服务器')
        await this.stopServer()
      }

      this.serverPort = options.port || 1935
      logger.info(`启动RTMP服务器模拟器，端口: ${this.serverPort}`)

      // 启动简化的RTMP服务器模拟器
      const serverScript = this.createRtmpServerScript()

      // 启动Node.js进程运行RTMP服务器模拟器
      this.rtmpServer = spawn('node', ['-e', serverScript], {
        detached: false,
        stdio: ['ignore', 'pipe', 'pipe'],
        env: {
          ...process.env,
          RTMP_PORT: this.serverPort.toString()
        }
      })

      const pid = this.rtmpServer.pid

      // 监听进程事件
      this.rtmpServer.on('error', (error) => {
        logger.error('RTMP服务器进程错误:', error)
        this.rtmpServer = null
      })

      this.rtmpServer.on('exit', (code, signal) => {
        logger.info(`RTMP服务器进程退出: code=${code}, signal=${signal}`)
        this.rtmpServer = null
        // 停止推流监控
        this.isStreaming = false
        if (this.voiceListMonitorInterval) {
          clearInterval(this.voiceListMonitorInterval)
          this.voiceListMonitorInterval = null
        }
      })

      // 监听输出
      if (this.rtmpServer.stdout) {
        this.rtmpServer.stdout.on('data', (data) => {
          logger.info('RTMP服务器输出:', data.toString())
        })
      }

      if (this.rtmpServer.stderr) {
        this.rtmpServer.stderr.on('data', (data) => {
          logger.warn('RTMP服务器错误输出:', data.toString())
        })
      }

      logger.info(`RTMP服务器模拟器已启动，PID: ${pid}，端口: ${this.serverPort}`)

      return { success: true, pid }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error('启动RTMP服务器失败:', errorMessage)
      this.rtmpServer = null
      return { success: false, error: errorMessage }
    }
  }



  public async stopServer(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.rtmpServer || this.rtmpServer.killed) {
        return { success: true }
      }

      const pid = this.rtmpServer.pid
      logger.info(`停止RTMP服务器进程，PID: ${pid}`)

      // 在 Windows 上使用 taskkill 强制终止进程
      if (process.platform === 'win32' && pid) {
        try {
          await execAsync(`taskkill /F /T /PID ${pid}`)
          logger.info(`已强制终止RTMP服务器进程，PID: ${pid}`)
        } catch (killError) {
          logger.warn(`taskkill 失败，尝试其他方法: ${killError}`)
          this.rtmpServer.kill('SIGTERM')
        }
      } else {
        this.rtmpServer.kill('SIGTERM')
      }

      this.rtmpServer = null
      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error('停止RTMP服务器失败:', errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  public async startVoiceListMonitoring(rtmpUrl: string): Promise<{ success: boolean; error?: string }> {
    try {
      // 检查FFmpeg是否可用
      const ffmpegPath = await this.findFFmpeg()
      if (!ffmpegPath) {
        throw new Error('未找到FFmpeg，请确保FFmpeg已安装并在PATH中')
      }

      // 首先测试RTMP连接
      const canConnect = await this.testRtmpConnection(rtmpUrl)
      if (!canConnect) {
        throw new Error('无法连接到RTMP服务器，请确保RTMP服务器已启动')
      }

      this.isStreaming = true
      logger.info('开始监听输出列表变化')

      // 开始监听输出列表
      this.startVoiceListMonitor(rtmpUrl, ffmpegPath)

      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error('开始推流监听失败:', errorMessage)
      this.isStreaming = false
      return { success: false, error: errorMessage }
    }
  }



  public async stopStream(): Promise<{ success: boolean; error?: string }> {
    try {
      this.isStreaming = false

      // 停止监控
      if (this.voiceListMonitorInterval) {
        clearInterval(this.voiceListMonitorInterval)
        this.voiceListMonitorInterval = null
      }

      // 停止所有推流超时
      for (const [speaker, timeout] of this.streamTimeouts.entries()) {
        if (timeout) {
          clearTimeout(timeout)
          logger.info(`清除${speaker}的推流超时`)
        }
      }
      this.streamTimeouts.clear()

      // 停止所有推流进程
      for (const [speaker, streamProcess] of this.streamProcesses.entries()) {
        if (streamProcess && !streamProcess.killed) {
          const pid = streamProcess.pid
          logger.info(`停止${speaker}推流进程，PID: ${pid}`)

          if (process.platform === 'win32' && pid) {
            try {
              await execAsync(`taskkill /F /T /PID ${pid}`)
              logger.info(`已强制终止${speaker}推流进程，PID: ${pid}`)
            } catch (killError) {
              logger.warn(`taskkill 失败，尝试其他方法: ${killError}`)
              streamProcess.kill('SIGTERM')
            }
          } else {
            streamProcess.kill('SIGTERM')
          }
        }
      }
      this.streamProcesses.clear()

      // 清空所有缓冲队列
      this.streamBuffers.clear()

      logger.info('推流已停止')
      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error('停止推流失败:', errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  // 任务开始时启动推流监听
  public async startTaskStreaming(rtmpUrl: string): Promise<{ success: boolean; error?: string }> {
    try {
      logger.info(`开始启动任务推流，目标URL: ${rtmpUrl}`)

      // 检查RTMP服务器是否运行
      if (!this.rtmpServer || this.rtmpServer.killed) {
        const error = 'RTMP服务器未运行，请先启动推流服务器'
        logger.error(error)
        throw new Error(error)
      }
      logger.info('RTMP服务器状态检查通过')

      // 检查FFmpeg是否可用
      const ffmpegPath = await this.findFFmpeg()
      if (!ffmpegPath) {
        const error = '未找到FFmpeg，请确保FFmpeg已安装并在PATH中'
        logger.error(error)
        throw new Error(error)
      }
      logger.info(`FFmpeg路径检查通过: ${ffmpegPath}`)

      // 首先测试RTMP连接
      logger.info('开始测试RTMP连接...')
      const canConnect = await this.testRtmpConnection(rtmpUrl)
      if (!canConnect) {
        const error = '无法连接到RTMP服务器，请确保RTMP服务器已启动'
        logger.error(error)
        throw new Error(error)
      }
      logger.info('RTMP连接测试通过')

      this.isStreaming = true
      logger.info('任务开始，启动推流监听')

      // 开始监听输出列表
      this.startVoiceListMonitor(rtmpUrl, ffmpegPath)

      logger.info('任务推流启动成功')
      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error('启动任务推流失败:', errorMessage)
      this.isStreaming = false
      return { success: false, error: errorMessage }
    }
  }

  // 任务停止时停止推流
  public async stopTaskStreaming(): Promise<{ success: boolean; error?: string }> {
    logger.info('任务停止，停止推流监听')
    return await this.stopStream()
  }

  private startVoiceListMonitor(rtmpUrl: string, ffmpegPath: string) {
    logger.info('启动输出列表推流监控')

    // 每2秒检查一次输出列表
    this.voiceListMonitorInterval = setInterval(() => {
      this.checkAndStreamNext(rtmpUrl, ffmpegPath)
    }, 2000)

    // 立即开始第一次检查
    this.checkAndStreamNext(rtmpUrl, ffmpegPath)
  }

  private async checkAndStreamNext(rtmpUrl: string, ffmpegPath: string) {
    if (!this.isStreaming) {
      logger.debug('推流已停止，跳过检查')
      return
    }

    try {
      // 获取当前输出列表（通过IPC从主窗口获取）
      const voiceList = await this.getVoiceListFromMain()

      const activeProcesses = Array.from(this.streamProcesses.entries())
        .filter(([speaker, process]) => process && !process.killed)

      logger.info(`检查输出列表，项目数量: ${voiceList.length}，活跃推流进程: ${activeProcesses.length} 个`)
      // if (activeProcesses.length > 0) {
      //   logger.info(`当前推流进程: ${activeProcesses.map(([speaker]) => speaker).join(', ')}`)
      // }

      if (!voiceList || voiceList.length === 0) {
        // 没有内容时推流测试图案（只有在没有任何推流进程时才推流测试图案）
        if (activeProcesses.length === 0) {
          logger.info('输出列表为空，开始推流测试图案')
          await this.streamTestPattern(rtmpUrl, ffmpegPath)
        } else {
          logger.debug('输出列表为空，但有推流进程正在运行，等待当前推流完成')
        }
        return
      }

      // 更新缓冲队列
      await this.updateStreamBuffers(voiceList)

      // 按说话人分组检查和推流（支持并行推流）
      const speakers = ['host', 'assistant'] as const
      for (const speaker of speakers) {
        const currentProcess = this.streamProcesses.get(speaker)
        const isProcessRunning = currentProcess && !currentProcess.killed

        if (!isProcessRunning) {
          // 从缓冲队列获取下一个要推流的项目
          const nextItem = await this.getNextBufferedItem(speaker)
          if (nextItem) {
            logger.info(`找到${speaker}的下一个推流项目: ${nextItem.text}，输出类型: ${nextItem.outputType}，音频URL: ${nextItem.audioUrl ? '已生成' : '未生成'}，视频URL: ${nextItem.videoUrl ? '已生成' : '未生成'}`)
            await this.streamVoiceItem(rtmpUrl, ffmpegPath, nextItem)
          }
        } else {
          logger.debug(`${speaker}推流进程正在运行，等待当前项目完成`)
        }
      }

      // 如果没有任何推流进程在运行，且没有可推流的项目，则推流测试图案
      const hasAnyActiveProcess = Array.from(this.streamProcesses.values()).some(process => process && !process.killed)
      if (!hasAnyActiveProcess) {
        const waitingItems = voiceList.filter(item => item && !item.isStreamed)
        if (waitingItems.length > 0) {
          const waitingInfo = waitingItems.map(item => ({
            text: item.text?.substring(0, 20) + '...',
            speaker: item.speaker || 'host',
            outputType: item.outputType,
            hasAudio: !!item.audioUrl,
            hasVideo: !!item.videoUrl,
            ready: item.outputType === 'digital-human' ? (!!item.audioUrl && !!item.videoUrl) : !!item.audioUrl
          }))
          logger.info(`有 ${waitingItems.length} 个项目等待生成完成:`, waitingInfo)
        } else {
          logger.info('没有可推流的项目，开始推流测试图案')
          await this.streamTestPattern(rtmpUrl, ffmpegPath)
        }
      }

    } catch (error) {
      logger.error('检查输出列表失败:', error)
    }
  }

  private async getVoiceListFromMain(): Promise<any[]> {
    try {
      // 返回当前缓存的输出列表
      return this.currentVoiceList
    } catch (error) {
      logger.error('获取输出列表失败:', error)
      return []
    }
  }

  public updateVoiceList(voiceList: any[]) {
    this.currentVoiceList = voiceList
    logger.info(`更新输出列表，项目数量: ${voiceList.length}`)
  }

  // 更新推流缓冲队列
  private async updateStreamBuffers(voiceList: any[]) {
    const speakers = ['host', 'assistant'] as const

    for (const speaker of speakers) {
      // 获取该说话人的准备好推流的项目
      const readyItems = voiceList.filter(item => {
        if (!item || item.isStreamed) {
          return false
        }

        // 检查说话人匹配（默认为主播）
        const itemSpeaker = item.speaker || 'host'
        if (itemSpeaker !== speaker) {
          return false
        }

        // 根据输出类型判断是否准备好推流
        if (item.outputType === 'digital-human') {
          // 数字人类型：需要同时有音频和视频URL
          return item.audioUrl && item.videoUrl
        } else {
          // 纯音频类型：只需要有音频URL
          return item.audioUrl
        }
      })

      // 更新缓冲队列（保持最多5个项目的缓冲以减少切换延迟）
      const currentBuffer = this.streamBuffers.get(speaker) || []
      const newBuffer = readyItems.slice(0, 5) // 增加缓冲数量

      if (JSON.stringify(currentBuffer) !== JSON.stringify(newBuffer)) {
        this.streamBuffers.set(speaker, newBuffer)
        logger.debug(`更新${speaker}缓冲队列，项目数量: ${newBuffer.length}`)
      }
    }
  }

  // 从缓冲队列获取下一个推流项目
  private async getNextBufferedItem(speaker: string): Promise<any | null> {
    const buffer = this.streamBuffers.get(speaker) || []
    if (buffer.length === 0) {
      return null
    }

    // 返回第一个项目并从缓冲队列中移除
    const nextItem = buffer.shift()
    this.streamBuffers.set(speaker, buffer)

    return nextItem
  }

  private getNextStreamItem(voiceList: any[]): any | null {
    // 根据输出类型筛选准备好推流的项目
    const validItems = voiceList.filter(item => {
      if (!item || item.isStreamed) {
        return false
      }

      // 根据输出类型判断是否准备好推流
      if (item.outputType === 'digital-human') {
        // 数字人类型：需要同时有音频和视频URL
        return item.audioUrl && item.videoUrl
      } else {
        // 纯音频类型：只需要有音频URL
        return item.audioUrl
      }
    })

    if (validItems.length === 0) {
      return null
    }

    // 返回第一个准备好推流的项目
    return validItems[0]
  }



  private async streamVoiceItem(rtmpUrl: string, ffmpegPath: string, voiceItem: any) {
    const mediaUrl = voiceItem.videoUrl || voiceItem.audioUrl
    if (!mediaUrl) {
      logger.warn('语音项目没有可用的媒体URL，跳过推流')
      return
    }

    // 获取说话人信息
    const speaker = voiceItem.speaker || 'host'

    // 检查该说话人是否已有推流进程在运行
    const existingProcess = this.streamProcesses.get(speaker)
    if (existingProcess && !existingProcess.killed) {
      logger.warn(`${speaker}已有推流进程在运行，跳过此项目`)
      return
    }

    // 获取媒体时长，确保单位统一（毫秒）
    let baseDuration = voiceItem.duration || 5000
    // 如果时长小于100，可能是秒为单位，转换为毫秒
    if (baseDuration < 100) {
      baseDuration = baseDuration * 1000
    }
    // 添加足够的缓冲时间，确保音频播放完整
    const bufferTime = 3000 // 3秒缓冲时间
    const duration = baseDuration + bufferTime

    // 主播和助理推流到不同的路径，确保完全独立
    const baseUrl = rtmpUrl.replace(/\/live\/.*$/, '')
    const speakerRtmpUrl = `${baseUrl}/live/${speaker}`

    logger.info(`开始推流${speaker}输出项目: ${voiceItem.text}, 媒体URL: ${mediaUrl}, 推流路径: ${speakerRtmpUrl}, 基础时长: ${baseDuration}ms, 总时长: ${duration}ms`)

    try {
      const ffmpegArgs = this.createStreamArgsForMedia(speakerRtmpUrl, mediaUrl)

      const streamProcess = spawn(ffmpegPath, ffmpegArgs, {
        detached: false,
        stdio: ['ignore', 'pipe', 'pipe']
      })

      // 将进程存储到对应说话人的映射中
      this.streamProcesses.set(speaker, streamProcess)

      const pid = streamProcess.pid
      logger.info(`${speaker}推流已开始，PID: ${pid}`)

      // 监听进程事件
      streamProcess.on('error', (error) => {
        logger.error(`${speaker}推流进程错误:`, error)
        // 清理超时定时器
        const timeout = this.streamTimeouts.get(speaker)
        if (timeout) {
          clearTimeout(timeout)
          this.streamTimeouts.delete(speaker)
        }
        this.streamProcesses.delete(speaker)
        this.markItemAsStreamed(voiceItem.id)
      })

      streamProcess.on('exit', (code, signal) => {
        logger.info(`${speaker}推流进程退出: code=${code}, signal=${signal}`)
        // 清理超时定时器
        const timeout = this.streamTimeouts.get(speaker)
        if (timeout) {
          clearTimeout(timeout)
          this.streamTimeouts.delete(speaker)
        }
        this.streamProcesses.delete(speaker)

        // 只有在正常退出或被信号终止时才标记为已推流
        if (code === 0 || signal === 'SIGTERM') {
          this.markItemAsStreamed(voiceItem.id)
        }

        // 立即尝试推流下一个项目以减少间隙
        setTimeout(async () => {
          const nextItem = await this.getNextBufferedItem(speaker)
          if (nextItem) {
            logger.info(`立即推流下一个${speaker}项目以减少切换间隙: ${nextItem.text}`)
            await this.streamVoiceItem(rtmpUrl, ffmpegPath, nextItem)
          }
        }, 100) // 100ms后立即开始下一个
      })

      // 监听FFmpeg输出，用于调试
      if (streamProcess.stderr) {
        streamProcess.stderr.on('data', (data) => {
          const output = data.toString()
          // 记录重要的FFmpeg信息
          if (output.includes('error') || output.includes('Error')) {
            logger.warn(`${speaker}推流FFmpeg警告: ${output.trim()}`)
          }
        })
      }

      // 不使用固定时长停止，让FFmpeg自然结束
      // 只设置一个安全超时，防止进程卡死
      const safetyTimeout = setTimeout(() => {
        const currentProcess = this.streamProcesses.get(speaker)
        if (currentProcess && !currentProcess.killed) {
          logger.warn(`${speaker}推流安全超时，强制停止: ${duration + 5000}ms`)
          currentProcess.kill('SIGTERM') // 使用SIGTERM而不是SIGKILL以允许优雅退出
          this.streamProcesses.delete(speaker)
          this.markItemAsStreamed(voiceItem.id)
        }
        this.streamTimeouts.delete(speaker)
      }, duration + 5000) // 减少安全时间以更快切换

      // 存储超时定时器
      this.streamTimeouts.set(speaker, safetyTimeout)

    } catch (error) {
      logger.error(`启动${speaker}推流进程失败:`, error)
      this.markItemAsStreamed(voiceItem.id)
    }
  }

  private async streamTestPattern(rtmpUrl: string, ffmpegPath: string) {
    // 测试图案推流到专用路径
    const baseUrl = rtmpUrl.replace(/\/live\/.*$/, '')
    const testPatternRtmpUrl = `${baseUrl}/live/test-pattern`
    logger.info(`开始推流测试图案到: ${testPatternRtmpUrl}`)

    // 检查是否已有测试图案推流在运行
    const testPatternKey = 'test-pattern'
    const existingProcess = this.streamProcesses.get(testPatternKey)
    if (existingProcess && !existingProcess.killed) {
      logger.debug('测试图案推流已在运行，跳过')
      return
    }

    try {
      const ffmpegArgs = this.createContinuousStreamArgs(testPatternRtmpUrl)
      logger.info(`FFmpeg参数: ${ffmpegArgs.join(' ')}`)

      const streamProcess = spawn(ffmpegPath, ffmpegArgs, {
        detached: false,
        stdio: ['ignore', 'pipe', 'pipe']
      })

      // 将测试图案进程存储
      this.streamProcesses.set(testPatternKey, streamProcess)

      const pid = streamProcess.pid
      logger.info(`测试图案推流已开始，PID: ${pid}`)

      // 监听进程事件
      streamProcess.on('error', (error) => {
        logger.error('测试图案推流进程错误:', error)
        this.streamProcesses.delete(testPatternKey)
      })

      streamProcess.on('exit', (code, signal) => {
        logger.info(`测试图案推流进程退出: code=${code}, signal=${signal}`)
        this.streamProcesses.delete(testPatternKey)
      })

      // 监听输出以获取错误信息
      if (streamProcess.stderr) {
        streamProcess.stderr.on('data', (data) => {
          const output = data.toString()
          if (output.includes('error') || output.includes('Error') || output.includes('failed')) {
            logger.error('FFmpeg错误输出:', output)
          } else {
            logger.debug('FFmpeg输出:', output)
          }
        })
      }

      if (streamProcess.stdout) {
        streamProcess.stdout.on('data', (data) => {
          logger.debug('FFmpeg标准输出:', data.toString())
        })
      }

    } catch (error) {
      logger.error('启动测试图案推流失败:', error)
    }
  }

  private markItemAsStreamed(itemId: string) {
    // 标记项目为已推流，避免重复推流
    logger.info(`标记项目为已推流: ${itemId}`)

    try {
      // 通过IPC通知前端移除已推流的项目
      windowManager.send(IPC_CHANNELS.streamServer.itemStreamed, itemId)
      logger.info(`已通知前端移除推流完成的项目: ${itemId}`)
    } catch (error) {
      logger.error('通知前端移除项目失败:', error)
    }
  }

  private createStreamArgsForMedia(rtmpUrl: string, mediaUrl: string): string[] {
    const args: string[] = []

    logger.info(`创建媒体推流参数: ${mediaUrl}`)

    // 添加关键的 -re 参数，以原始帧率读取输入文件
    args.push('-re')

    // HTTP URL的特殊处理
    if (mediaUrl.startsWith('http://') || mediaUrl.startsWith('https://')) {
      args.push(
        '-user_agent', 'FFmpeg/OBA-Live-Tool',
        '-timeout', '30000000',
        '-reconnect', '1',
        '-reconnect_streamed', '1',
        '-reconnect_delay_max', '5'
      )
    }

    args.push('-i', mediaUrl)

    // 检测输入是否为纯音频
    const isAudioOnly = mediaUrl.includes('.mp3') || mediaUrl.includes('.wav') || mediaUrl.includes('.aac')

    if (isAudioOnly) {
      // 纯音频推流参数
      args.push(
        // 音频编码设置
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',

        // 生成静态视频帧
        '-f', 'lavfi',
        '-i', 'color=c=black:s=1280x720:r=25',
        '-c:v', 'libx264',
        '-preset', 'ultrafast', // 更快的编码预设
        '-tune', 'stillimage',
        '-pix_fmt', 'yuv420p',
        '-r', '25',
        '-g', '25', // 减少GOP大小以降低延迟
        '-b:v', '500k',

        // 确保音频长度决定输出长度
        '-shortest',

        // 推流设置 - 优化延迟
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        '-flush_packets', '1', // 立即刷新数据包
        '-fflags', '+genpts+flush_packets', // 生成时间戳并刷新

        rtmpUrl
      )
    } else {
      // 视频推流参数 - 优化延迟
      args.push(
        // 视频编码设置
        '-c:v', 'libx264',
        '-preset', 'ultrafast', // 最快编码预设
        '-tune', 'zerolatency', // 零延迟调优
        '-profile:v', 'baseline',
        '-level', '3.0',
        '-pix_fmt', 'yuv420p',
        '-r', '25',
        '-g', '25', // 减少GOP大小
        '-keyint_min', '25',
        '-sc_threshold', '0',
        '-b:v', '2000k',
        '-maxrate', '2500k',
        '-bufsize', '2500k', // 减少缓冲大小以降低延迟

        // 音频编码设置
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',

        // 推流设置 - 优化延迟
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        '-avoid_negative_ts', 'make_zero',
        '-fflags', '+genpts+flush_packets', // 生成时间戳并刷新数据包
        '-flush_packets', '1', // 立即刷新数据包

        rtmpUrl
      )
    }

    return args
  }

  private createContinuousStreamArgs(rtmpUrl: string): string[] {
    const args: string[] = []

    logger.info('创建测试图案推流参数')

    // 使用测试图案作为基础流
    args.push(
      // 输入源：彩色测试图案
      '-f', 'lavfi',
      '-i', 'testsrc2=size=480x854:rate=25',

      // 音频输入：静音
      '-f', 'lavfi',
      '-i', 'anullsrc=channel_layout=stereo:sample_rate=44100',

      // 视频编码设置
      '-c:v', 'libx264',
      '-preset', 'veryfast',
      '-tune', 'zerolatency',
      '-profile:v', 'baseline',
      '-level', '3.0',
      '-pix_fmt', 'yuv420p',
      '-r', '25',
      '-g', '50',
      '-keyint_min', '25',
      '-sc_threshold', '0',
      '-b:v', '1000k',
      '-maxrate', '1500k',
      '-bufsize', '3000k',

      // 音频编码设置
      '-c:a', 'aac',
      '-b:a', '128k',
      '-ar', '44100',
      '-ac', '2',

      // 推流设置
      '-f', 'flv',
      '-flvflags', 'no_duration_filesize',
      '-avoid_negative_ts', 'make_zero',
      '-fflags', '+genpts',

      rtmpUrl
    )

    return args
  }

  private createRtmpServerScript(): string {
    return `
      const NodeMediaServer = require('node-media-server');
      const port = parseInt(process.env.RTMP_PORT) || 1935;

      console.log('启动RTMP服务器，端口:', port);

      const config = {
        rtmp: {
          port: port,
          chunk_size: 60000,
          gop_cache: true,
          ping: 30,
          ping_timeout: 60,
          // 优化推流切换的缓冲配置
          high_water_mark: 8 * 1024 * 1024, // 8MB缓冲
          max_connections: 1000,
          timeout: 60
        },
        http: {
          port: port + 1000, // HTTP-FLV端口
          allow_origin: '*',
          // 增加HTTP缓冲以减少卡顿
          http_buffer_size: 2 * 1024 * 1024 // 2MB HTTP缓冲
        },
        relay: {
          ffmpeg: 'ffmpeg',
          tasks: []
        }
      };

      const nms = new NodeMediaServer(config);
      nms.run();

      console.log('RTMP服务器已启动');
      console.log('RTMP推流地址: rtmp://localhost:' + port + '/live');
      console.log('HTTP-FLV播放地址: http://localhost:' + (port + 1000) + '/live/STREAM_NAME.flv');
      console.log('状态查询: http://localhost:' + (port + 1000) + '/api/server');

      // 监听推流事件
      nms.on('preConnect', (id, args) => {
        console.log('[NodeEvent on preConnect]', \`id=\${id} args=\${JSON.stringify(args)}\`);
      });

      nms.on('postConnect', (id, args) => {
        console.log('[NodeEvent on postConnect]', \`id=\${id} args=\${JSON.stringify(args)}\`);
      });

      nms.on('doneConnect', (id, args) => {
        console.log('[NodeEvent on doneConnect]', \`id=\${id} args=\${JSON.stringify(args)}\`);
      });

      nms.on('prePublish', (id, StreamPath, args) => {
        console.log('[NodeEvent on prePublish]', \`id=\${id} StreamPath=\${StreamPath} args=\${JSON.stringify(args)}\`);
      });

      nms.on('postPublish', (id, StreamPath, args) => {
        console.log('[NodeEvent on postPublish]', \`id=\${id} StreamPath=\${StreamPath} args=\${JSON.stringify(args)}\`);
      });

      nms.on('donePublish', (id, StreamPath, args) => {
        console.log('[NodeEvent on donePublish]', \`id=\${id} StreamPath=\${StreamPath} args=\${JSON.stringify(args)}\`);
      });

      process.on('SIGTERM', () => {
        console.log('收到SIGTERM信号，关闭RTMP服务器');
        nms.stop();
        process.exit(0);
      });

      process.on('SIGINT', () => {
        console.log('收到SIGINT信号，关闭RTMP服务器');
        nms.stop();
        process.exit(0);
      });
    `
  }

  private async findFFmpeg(): Promise<string | null> {
    const possiblePaths = [
      'ffmpeg',
      'ffmpeg.exe',
      join(process.cwd(), 'ffmpeg.exe'),
      join(process.cwd(), 'bin', 'ffmpeg.exe'),
      'C:\\ffmpeg\\bin\\ffmpeg.exe',
      'D:\\ffmpeg\\bin\\ffmpeg.exe'
    ]

    for (const path of possiblePaths) {
      try {
        await execAsync(`"${path}" -version`)
        logger.info(`找到FFmpeg: ${path}`)
        return path
      } catch (error) {
        // 继续尝试下一个路径
      }
    }

    return null
  }

  private async testRtmpConnection(rtmpUrl: string): Promise<boolean> {
    try {
      // 从RTMP URL中提取主机和端口
      const url = new URL(rtmpUrl.replace('rtmp://', 'http://'))
      const host = url.hostname
      const port = parseInt(url.port) || 1935

      logger.info(`测试RTMP连接: ${host}:${port}`)

      // 使用简单的TCP连接测试
      return new Promise((resolve) => {
        const socket = createConnection(port, host)

        const timeout = setTimeout(() => {
          socket.destroy()
          resolve(false)
        }, 3000) // 3秒超时

        socket.on('connect', () => {
          clearTimeout(timeout)
          socket.destroy()
          logger.info('RTMP连接测试成功')
          resolve(true)
        })

        socket.on('error', () => {
          clearTimeout(timeout)
          logger.warn('RTMP连接测试失败')
          resolve(false)
        })
      })
    } catch (error) {
      logger.error('RTMP连接测试异常:', error)
      return false
    }
  }




}

const streamServerManager = StreamServerManager.getInstance()

function setupIpcHandlers() {
  // 启动RTMP服务器
  ipcMain.handle(IPC_CHANNELS.streamServer.start, async (_, options: { port: number }) => {
    return await streamServerManager.startServer(options)
  })

  // 停止RTMP服务器
  ipcMain.handle(IPC_CHANNELS.streamServer.stop, async () => {
    return await streamServerManager.stopServer()
  })

  // 获取服务器状态
  ipcMain.handle(IPC_CHANNELS.streamServer.status, async () => {
    return streamServerManager.getStatus()
  })

  // 开始推流监听
  ipcMain.handle(IPC_CHANNELS.streamServer.startStream, async (_, options: { rtmpUrl: string }) => {
    return await streamServerManager.startVoiceListMonitoring(options.rtmpUrl)
  })

  // 停止推流
  ipcMain.handle(IPC_CHANNELS.streamServer.stopStream, async () => {
    return await streamServerManager.stopStream()
  })

  // 任务开始时启动推流
  ipcMain.handle(IPC_CHANNELS.streamServer.startTaskStream, async (_, options: { rtmpUrl: string }) => {
    return await streamServerManager.startTaskStreaming(options.rtmpUrl)
  })

  // 任务停止时停止推流
  ipcMain.handle(IPC_CHANNELS.streamServer.stopTaskStream, async () => {
    return await streamServerManager.stopTaskStreaming()
  })

  // 获取推流状态
  ipcMain.handle(IPC_CHANNELS.streamServer.streamStatus, async () => {
    return streamServerManager.getStreamStatus()
  })

  // 更新输出列表
  ipcMain.handle(IPC_CHANNELS.streamServer.updateVoiceList, async (_, voiceList: any[]) => {
    streamServerManager.updateVoiceList(voiceList)
    return { success: true }
  })
}

export function setupStreamServerIpcHandlers() {
  setupIpcHandlers()
}

export { streamServerManager }
