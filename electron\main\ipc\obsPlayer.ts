import { BrowserWindow, ipcMain } from 'electron'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { typedIpcMainHandle } from '#/utils'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

let obsPlayerWindow: BrowserWindow | null = null
let hostPlayerWindow: BrowserWindow | null = null
let assistantPlayerWindow: BrowserWindow | null = null



// 窗口尺寸配置 - 增大窗口尺寸
const ASPECT_RATIOS = {
  '16:9': { width: 1280, height: 720 },
  '9:16': { width: 720, height: 1280 }
} as const

type AspectRatio = keyof typeof ASPECT_RATIOS

function setupIpcHandlers() {
  // 打开OBS播放器窗口
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.open, async (_event, preferredAspectRatio?: AspectRatio) => {
    // 如果窗口已存在，则聚焦到该窗口
    if (obsPlayerWindow && !obsPlayerWindow.isDestroyed()) {
      obsPlayerWindow.focus()
      return
    }

    // 使用传入的比例或默认使用9:16比例
    const aspectRatio = preferredAspectRatio || '9:16'
    const defaultRatio = ASPECT_RATIOS[aspectRatio]
    obsPlayerWindow = new BrowserWindow({
      title: '数字人OBS播放器',
      width: defaultRatio.width,
      height: defaultRatio.height + 50, // 加上顶部信息栏和控制栏高度
      minWidth: 320,
      minHeight: 480,
      autoHideMenuBar: true,
      alwaysOnTop: false, // 不置顶显示
      resizable: true, // 允许用户手动调整窗口大小
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        webSecurity: false, // 允许加载本地视频文件
      },
    })

    // 设置窗口图标
    const iconPath = path.join(process.env.VITE_PUBLIC || '', 'favicon.ico')
    obsPlayerWindow.setIcon(iconPath)

    // 加载页面
    const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL
    if (VITE_DEV_SERVER_URL) {
      obsPlayerWindow.loadURL(`${VITE_DEV_SERVER_URL}#/obs-player`)
    } else {
      const indexHtml = path.join(__dirname, '../../dist/index.html')
      obsPlayerWindow.loadFile(indexHtml, { hash: '/obs-player' })
    }

    // 窗口关闭时清理引用
    obsPlayerWindow.on('closed', () => {
      obsPlayerWindow = null
    })

    // 开发环境下打开开发者工具
    if (process.env.NODE_ENV === 'development') {
      obsPlayerWindow.webContents.openDevTools()
    }
  })

  // 关闭OBS播放器窗口
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.close, async () => {
    if (obsPlayerWindow && !obsPlayerWindow.isDestroyed()) {
      obsPlayerWindow.close()
    }
  })

  // 播放数字人视频
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.playVideo, async (_event, videoUrl, text, id) => {
    console.log('主进程收到播放数字人视频请求:', { videoUrl, text, id })

    return new Promise<void>((resolve) => {
      if (!obsPlayerWindow || obsPlayerWindow.isDestroyed()) {
        console.error('OBS播放器窗口不存在或已销毁')
        resolve()
        return
      }

      console.log('OBS播放器窗口存在，发送播放指令')

      let isResolved = false

      // 监听视频播放结束事件
      const handleVideoEnded = () => {
        if (isResolved) return
        isResolved = true
        console.log('收到视频播放结束通知')
        ipcMain.removeListener(IPC_CHANNELS.obsPlayer.videoEnded, handleVideoEnded)
        resolve()
      }

      ipcMain.once(IPC_CHANNELS.obsPlayer.videoEnded, handleVideoEnded)

      // 创建简单的payload对象
      const payload = {
        videoUrl: String(videoUrl || ''),
        text: String(text || ''),
        id: String(id || '')
      }

      // 向OBS播放器窗口发送播放请求
      console.log('向OBS播放器发送播放指令，payload:', payload)
      try {
        obsPlayerWindow.webContents.send('PLAY_DIGITAL_HUMAN_VIDEO', payload)
      } catch (error) {
        console.error('发送播放指令失败:', error)
        if (!isResolved) {
          isResolved = true
          ipcMain.removeListener(IPC_CHANNELS.obsPlayer.videoEnded, handleVideoEnded)
          resolve()
        }
        return
      }

      // 设置超时，防止无限等待
      setTimeout(() => {
        if (!isResolved) {
          isResolved = true
          console.log('播放超时，强制结束')
          ipcMain.removeListener(IPC_CHANNELS.obsPlayer.videoEnded, handleVideoEnded)
          resolve()
        }
      }, 30000) // 30秒超时
    })
  })

  // 视频播放结束通知
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.videoEnded, async () => {
    console.log('主进程收到视频播放结束通知')
    // 这个方法由OBS播放器调用，通知视频播放结束
    // 实际的处理在playVideo方法中通过ipcMain.emit触发
    ipcMain.emit(IPC_CHANNELS.obsPlayer.videoEnded)
  })

  // 设置窗口宽高比
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.setAspectRatio, async (_event, aspectRatio: AspectRatio) => {
    if (!obsPlayerWindow || obsPlayerWindow.isDestroyed()) {
      console.error('OBS播放器窗口不存在或已销毁')
      return
    }

    const ratio = ASPECT_RATIOS[aspectRatio]
    if (!ratio) {
      console.error('不支持的宽高比:', aspectRatio)
      return
    }

    console.log('设置OBS播放器窗口比例:', aspectRatio, ratio)

    // 设置窗口尺寸，加上顶部信息栏和控制栏高度
    obsPlayerWindow.setSize(ratio.width, ratio.height + 50)

    // 居中显示窗口
    obsPlayerWindow.center()
  })

  // === 双轨播放器支持 ===

  // 创建窗口的通用函数
  const createPlayerWindow = (title: string, route: string, aspectRatio: AspectRatio = '9:16') => {
    const defaultRatio = ASPECT_RATIOS[aspectRatio]
    const window = new BrowserWindow({
      title,
      width: defaultRatio.width,
      height: defaultRatio.height + 50,
      minWidth: 320,
      minHeight: 480,
      autoHideMenuBar: true,
      alwaysOnTop: false,
      resizable: true,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        webSecurity: false,
      },
    })

    // 设置窗口图标
    const iconPath = path.join(process.env.VITE_PUBLIC || '', 'favicon.ico')
    window.setIcon(iconPath)

    // 加载页面
    const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL
    if (VITE_DEV_SERVER_URL) {
      window.loadURL(`${VITE_DEV_SERVER_URL}#${route}`)
    } else {
      const indexHtml = path.join(__dirname, '../../dist/index.html')
      window.loadFile(indexHtml, { hash: route })
    }

    // 开发环境下打开开发者工具
    if (process.env.NODE_ENV === 'development') {
      window.webContents.openDevTools()
    }

    return window
  }

  // 打开主播播放器窗口
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.openHost, async (_event, preferredAspectRatio?: AspectRatio) => {
    if (hostPlayerWindow && !hostPlayerWindow.isDestroyed()) {
      hostPlayerWindow.focus()
      return
    }

    const aspectRatio = preferredAspectRatio || '9:16'
    hostPlayerWindow = createPlayerWindow('主播数字人播放器', '/host-player', aspectRatio)

    // 窗口关闭时清理引用
    hostPlayerWindow.on('closed', () => {
      hostPlayerWindow = null
    })
  })

  // 打开助理播放器窗口
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.openAssistant, async (_event, preferredAspectRatio?: AspectRatio) => {
    if (assistantPlayerWindow && !assistantPlayerWindow.isDestroyed()) {
      assistantPlayerWindow.focus()
      return
    }

    const aspectRatio = preferredAspectRatio || '9:16'
    assistantPlayerWindow = createPlayerWindow('助理数字人播放器', '/assistant-player', aspectRatio)

    // 窗口关闭时清理引用
    assistantPlayerWindow.on('closed', () => {
      assistantPlayerWindow = null
    })
  })

  // 关闭主播播放器窗口
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.closeHost, async () => {
    if (hostPlayerWindow && !hostPlayerWindow.isDestroyed()) {
      hostPlayerWindow.close()
    }
  })

  // 关闭助理播放器窗口
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.closeAssistant, async () => {
    if (assistantPlayerWindow && !assistantPlayerWindow.isDestroyed()) {
      assistantPlayerWindow.close()
    }
  })

  // 主播播放器播放视频
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.playVideoHost, async (_event, videoUrl, text, id) => {
    console.log('主播播放器收到播放请求:', { videoUrl, text, id })

    return new Promise<void>((resolve) => {
      if (!hostPlayerWindow || hostPlayerWindow.isDestroyed()) {
        console.error('主播播放器窗口不存在或已销毁')
        resolve()
        return
      }

      let isResolved = false

      // 监听视频播放结束事件
      const handleVideoEnded = () => {
        if (isResolved) return
        isResolved = true
        console.log('主播播放器视频播放结束')
        ipcMain.removeListener(IPC_CHANNELS.obsPlayer.videoEndedHost, handleVideoEnded)
        resolve()
      }

      ipcMain.once(IPC_CHANNELS.obsPlayer.videoEndedHost, handleVideoEnded)

      // 创建payload对象
      const payload = {
        videoUrl: String(videoUrl || ''),
        text: String(text || ''),
        id: String(id || ''),
        speaker: 'host'
      }

      // 向主播播放器窗口发送播放请求
      try {
        hostPlayerWindow.webContents.send('PLAY_DIGITAL_HUMAN_VIDEO', payload)
      } catch (error) {
        console.error('发送主播播放指令失败:', error)
        if (!isResolved) {
          isResolved = true
          ipcMain.removeListener(IPC_CHANNELS.obsPlayer.videoEndedHost, handleVideoEnded)
          resolve()
        }
        return
      }

      // 设置超时
      setTimeout(() => {
        if (!isResolved) {
          isResolved = true
          console.log('主播播放器播放超时，强制结束')
          ipcMain.removeListener(IPC_CHANNELS.obsPlayer.videoEndedHost, handleVideoEnded)
          resolve()
        }
      }, 30000)
    })
  })

  // 助理播放器播放视频
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.playVideoAssistant, async (_event, videoUrl, text, id) => {
    console.log('助理播放器收到播放请求:', { videoUrl, text, id })

    return new Promise<void>((resolve) => {
      if (!assistantPlayerWindow || assistantPlayerWindow.isDestroyed()) {
        console.error('助理播放器窗口不存在或已销毁')
        resolve()
        return
      }

      let isResolved = false

      // 监听视频播放结束事件
      const handleVideoEnded = () => {
        if (isResolved) return
        isResolved = true
        console.log('助理播放器视频播放结束')
        ipcMain.removeListener(IPC_CHANNELS.obsPlayer.videoEndedAssistant, handleVideoEnded)
        resolve()
      }

      ipcMain.once(IPC_CHANNELS.obsPlayer.videoEndedAssistant, handleVideoEnded)

      // 创建payload对象
      const payload = {
        videoUrl: String(videoUrl || ''),
        text: String(text || ''),
        id: String(id || ''),
        speaker: 'assistant'
      }

      // 向助理播放器窗口发送播放请求
      try {
        assistantPlayerWindow.webContents.send('PLAY_DIGITAL_HUMAN_VIDEO', payload)
      } catch (error) {
        console.error('发送助理播放指令失败:', error)
        if (!isResolved) {
          isResolved = true
          ipcMain.removeListener(IPC_CHANNELS.obsPlayer.videoEndedAssistant, handleVideoEnded)
          resolve()
        }
        return
      }

      // 设置超时
      setTimeout(() => {
        if (!isResolved) {
          isResolved = true
          console.log('助理播放器播放超时，强制结束')
          ipcMain.removeListener(IPC_CHANNELS.obsPlayer.videoEndedAssistant, handleVideoEnded)
          resolve()
        }
      }, 30000)
    })
  })

  // 主播播放器视频播放结束通知
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.videoEndedHost, async () => {
    console.log('主进程收到主播播放器视频播放结束通知')
    ipcMain.emit(IPC_CHANNELS.obsPlayer.videoEndedHost)
  })

  // 助理播放器视频播放结束通知
  typedIpcMainHandle(IPC_CHANNELS.obsPlayer.videoEndedAssistant, async () => {
    console.log('主进程收到助理播放器视频播放结束通知')
    ipcMain.emit(IPC_CHANNELS.obsPlayer.videoEndedAssistant)
  })


}

export { setupIpcHandlers }
