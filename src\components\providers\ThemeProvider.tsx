import { useTheme } from '@/hooks/useTheme'
import { useEffect } from 'react'

interface ThemeProviderProps {
  children: React.ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const { resolvedTheme } = useTheme()

  useEffect(() => {
    const root = window.document.documentElement

    // 移除之前的主题类
    root.classList.remove('light', 'dark')

    // 添加当前主题类
    root.classList.add(resolvedTheme)
  }, [resolvedTheme])

  return <>{children}</>
}
