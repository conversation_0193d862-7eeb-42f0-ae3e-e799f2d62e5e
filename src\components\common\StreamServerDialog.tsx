import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useToast } from '@/hooks/useToast'
import { useRtmpServerUrl, useRtmpServerPort, useVoiceActions } from '@/hooks/useVoiceSettings'
import { Radio, Play, Square, Copy } from 'lucide-react'
import { useEffect, useState } from 'react'

interface StreamServerStatus {
  isRunning: boolean
  pid?: number
  isStreaming?: boolean
}

const StreamServerDialog = () => {
  const [serverStatus, setServerStatus] = useState<StreamServerStatus>({ isRunning: false })
  const [isStarting, setIsStarting] = useState(false)
  const [isStopping, setIsStopping] = useState(false)
  const [isStreamToggling, setIsStreamToggling] = useState(false)

  const { toast } = useToast()

  // 推流服务器配置
  const rtmpServerUrl = useRtmpServerUrl()
  const rtmpServerPort = useRtmpServerPort()
  const { setRtmpServerUrl, setRtmpServerPort } = useVoiceActions()

  // 生成推流地址
  const hostStreamUrl = `rtmp://localhost:${rtmpServerPort}/live/host`
  const assistantStreamUrl = `rtmp://localhost:${rtmpServerPort}/live/assistant`

  // 复制推流地址
  const copyStreamUrl = async (url: string, type: string) => {
    try {
      await navigator.clipboard.writeText(url)
      toast.success(`${type}推流地址已复制`)
    } catch (error) {
      toast.error('复制失败')
    }
  }

  // 切换推流状态
  const handleToggleStream = async () => {
    try {
      setIsStreamToggling(true)

      // 首先检查服务器状态
      const serverStatus = await window.ipcRenderer.invoke('streamServer:status' as any) as { isRunning: boolean; pid?: number }
      if (!serverStatus.isRunning) {
        toast.error('请先启动RTMP服务器')
        return
      }

      // 检查当前推流状态
      const currentStreamStatus = await window.ipcRenderer.invoke('streamServer:streamStatus' as any) as { isStreaming: boolean; pid?: number }

      if (currentStreamStatus.isStreaming) {
        // 停止推流
        const result = await window.ipcRenderer.invoke('streamServer:stopTaskStream' as any) as { success: boolean; error?: string }
        if (result && result.success) {
          toast.success('推流已停止')
        } else {
          toast.error(`停止推流失败: ${result?.error || '未知错误'}`)
        }
      } else {
        // 开始推流
        const result = await window.ipcRenderer.invoke('streamServer:startTaskStream' as any, {
          rtmpUrl: hostStreamUrl
        }) as { success: boolean; error?: string }

        if (result && result.success) {
          toast.success(`推流已启动到 ${hostStreamUrl}，请检查 http://localhost:${rtmpServerPort + 1000}/live/host.flv 是否有内容`)
        } else {
          toast.error(`开始推流失败: ${result?.error || '未知错误'}`)
        }
      }
    } catch (error) {
      console.error('切换推流状态失败:', error)
      toast.error('切换推流状态失败')
    } finally {
      setIsStreamToggling(false)
    }
  }

  // 初始化时获取服务状态，并设置定时检查
  useEffect(() => {
    checkServerStatus()

    // 每3秒自动检查一次状态
    const interval = setInterval(() => {
      checkServerStatus()
      checkStreamStatus()
    }, 3000)

    return () => clearInterval(interval)
  }, [])



  const checkServerStatus = async () => {
    try {
      const status = await window.ipcRenderer.invoke('streamServer:status' as any) as StreamServerStatus
      if (status) {
        setServerStatus(status)
      }
    } catch (error) {
      console.error('检查推流服务器状态失败:', error)
    }
  }

  const checkStreamStatus = async () => {
    try {
      const status = await window.ipcRenderer.invoke('streamServer:streamStatus' as any) as { isStreaming: boolean; pid?: number }
      if (status) {
        setServerStatus(prev => ({ ...prev, isStreaming: status.isStreaming }))
      }
    } catch (error) {
      console.error('检查推流状态失败:', error)
    }
  }

  const handleStartServer = async () => {
    try {
      setIsStarting(true)
      const result = await window.ipcRenderer.invoke('streamServer:start' as any, {
        port: rtmpServerPort
      }) as { success: boolean; error?: string; pid?: number }
      if (result && result.success) {
        setServerStatus({ isRunning: true, pid: result.pid })
        toast.success(`推流服务器已启动 (PID: ${result.pid})`)
        // 启动后立即检查状态
        setTimeout(checkServerStatus, 1000)
      } else {
        toast.error(`启动推流服务器失败: ${result?.error || '未知错误'}`)
      }
    } catch (error) {
      console.error('启动推流服务器失败:', error)
      toast.error('启动推流服务器失败')
    } finally {
      setIsStarting(false)
    }
  }

  const handleStopServer = async () => {
    try {
      setIsStopping(true)
      const result = await window.ipcRenderer.invoke('streamServer:stop' as any) as { success: boolean; error?: string }
      if (result && result.success) {
        setServerStatus({ isRunning: false })
        toast.success('推流服务器已停止')
      } else {
        toast.error(`停止推流服务器失败: ${result?.error || '未知错误'}`)
      }
    } catch (error) {
      console.error('停止推流服务器失败:', error)
      toast.error('停止推流服务器失败')
    } finally {
      setIsStopping(false)
    }
  }



  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          title="推流服务器"
          className={serverStatus.isRunning ? 'border-green-500 text-green-600' : ''}
        >
          <Radio className="h-4 w-4" />
          <span>推流服务器</span>
          {serverStatus.isRunning && (
            <div className="w-2 h-2 bg-green-500 rounded-full ml-1" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[650px]" align="end">
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <h4 className="font-medium leading-none">推流服务器</h4>
              <p className="text-sm text-muted-foreground">
                启动和管理RTMP推流服务器
              </p>
            </div>

            {/* 右侧：状态和控制按钮 */}
            <div className="flex gap-6">
              {/* 服务器状态 */}
              <div className="flex flex-col items-center gap-2">
                <div className="text-xs text-muted-foreground flex items-center gap-1">
                  <span className={`inline-block w-2 h-2 rounded-full ${serverStatus.isRunning ? 'bg-green-500' : 'bg-gray-400'}`}></span>
                  <span>服务器</span>
                </div>
                {!serverStatus.isRunning ? (
                  <Button
                    size="default"
                    onClick={handleStartServer}
                    disabled={isStarting}
                    className="h-9 px-4 text-sm gap-2"
                  >
                    <Play className="h-4 w-4" />
                    {isStarting ? '启动中' : '启动'}
                  </Button>
                ) : (
                  <Button
                    size="default"
                    variant="destructive"
                    onClick={handleStopServer}
                    disabled={isStopping}
                    className="h-9 px-4 text-sm gap-2"
                  >
                    <Square className="h-4 w-4" />
                    {isStopping ? '停止中' : '停止'}
                  </Button>
                )}
              </div>

              {/* 推流状态 */}
              <div className="flex flex-col items-center gap-2">
                <div className="text-xs text-muted-foreground flex items-center gap-1">
                  <span className={`inline-block w-2 h-2 rounded-full ${serverStatus.isStreaming ? 'bg-green-500' : 'bg-gray-400'}`}></span>
                  <span>推流</span>
                </div>
                <Button
                  size="default"
                  variant={serverStatus.isStreaming ? "destructive" : "default"}
                  onClick={handleToggleStream}
                  disabled={!serverStatus.isRunning || isStreamToggling}
                  className="h-9 px-4 text-sm gap-2"
                >
                  {serverStatus.isStreaming ? (
                    <>
                      <Square className="h-4 w-4" />
                      {isStreamToggling ? '停止推流中' : '停止推流'}
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4" />
                      {isStreamToggling ? '开始推流中' : '开始推流'}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* 左右分栏布局 */}
          <div className="grid grid-cols-2 gap-6">
            {/* 左侧：RTMP配置 */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">RTMP配置</Label>
              <div className="space-y-3">
                <div className="space-y-2">
                  <Label htmlFor="rtmp-server-url" className="text-xs text-muted-foreground">服务器地址</Label>
                  <Input
                    id="rtmp-server-url"
                    type="url"
                    placeholder="rtmp://localhost:1935/live"
                    value={rtmpServerUrl}
                    onChange={(e) => setRtmpServerUrl(e.target.value)}
                    className="text-xs"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="rtmp-server-port" className="text-xs text-muted-foreground">服务器端口</Label>
                  <Input
                    id="rtmp-server-port"
                    type="number"
                    placeholder="1935"
                    value={rtmpServerPort}
                    onChange={(e) => setRtmpServerPort(parseInt(e.target.value) || 1935)}
                    className="text-xs"
                  />
                </div>
              </div>
            </div>

            {/* 右侧：推流地址 */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">推流地址</Label>

              {/* 主播推流地址 */}
              <div className="space-y-2">
                <Label className="text-xs text-muted-foreground">主播推流地址</Label>
                <div className="flex items-center gap-2">
                  <Input
                    value={hostStreamUrl}
                    readOnly
                    className="text-xs font-mono"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyStreamUrl(hostStreamUrl, '主播')}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              {/* 助理推流地址 */}
              <div className="space-y-2">
                <Label className="text-xs text-muted-foreground">助理推流地址</Label>
                <div className="flex items-center gap-2">
                  <Input
                    value={assistantStreamUrl}
                    readOnly
                    className="text-xs font-mono"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyStreamUrl(assistantStreamUrl, '助理')}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              <p className="text-xs text-muted-foreground mb-2">
                根据需要选择对应的推流地址用于OBS等推流软件
              </p>
            </div>
          </div>
        </div>

        <Separator />

        {/* 使用说明和播放地址 */}
        <div className="grid grid-cols-2 gap-6 mt-4">
          {/* 左侧：使用说明 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">使用说明</Label>
            <div className="text-xs text-muted-foreground space-y-1">
              <p>• 当输出列表播放音频/视频时，自动推流对应内容</p>
              <p>• 没有播放内容时，推流测试图案</p>
            </div>
          </div>

          {/* 右侧：HTTP-FLV播放地址 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">播放地址</Label>
            <div className="text-xs text-muted-foreground space-y-1">
              <p>• 主播: ffplay http://localhost:{rtmpServerPort + 1000}/live/host.flv</p>
              <p>• 助理: ffplay http://localhost:{rtmpServerPort + 1000}/live/assistant.flv</p>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover >
  )
}

export default StreamServerDialog
