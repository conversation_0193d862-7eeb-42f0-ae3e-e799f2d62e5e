import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useForbiddenWords } from '@/hooks/useForbiddenWords'
import { useToast } from '@/hooks/useToast'
import { AlertTriangle, Plus, X, Upload, Trash2 } from 'lucide-react'
import { useState } from 'react'
import BatchImportDialog from '@/components/common/BatchImportDialog'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

const ForbiddenWordsSettings = () => {
  const { forbiddenWords, addForbiddenWord, addForbiddenWords, removeForbiddenWord, clearForbiddenWords } = useForbiddenWords()
  const [newWord, setNewWord] = useState('')
  const { toast } = useToast()

  const handleAddWord = () => {
    const word = newWord.trim()
    if (!word) {
      toast.error('请输入违禁词')
      return
    }

    if (forbiddenWords.includes(word)) {
      toast.error('该违禁词已存在')
      return
    }

    addForbiddenWord(word)
    setNewWord('')
    toast.success('违禁词已添加')
  }

  const handleRemoveWord = (word: string) => {
    removeForbiddenWord(word)
    toast.success('违禁词已删除')
  }

  const handleClearAll = () => {
    clearForbiddenWords()
    toast.success('所有违禁词已清空')
  }

  const handleBatchImport = (words: string[]) => {
    const existingWords = words.filter(word => forbiddenWords.includes(word))
    const newWords = words.filter(word => !forbiddenWords.includes(word))

    if (newWords.length > 0) {
      addForbiddenWords(newWords)
      toast.success(`成功导入 ${newWords.length} 个违禁词`)
    }

    if (existingWords.length > 0) {
      toast.error(`${existingWords.length} 个违禁词已存在，已跳过`)
    }

    if (newWords.length === 0 && existingWords.length === 0) {
      toast.error('没有有效的违禁词可导入')
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>违禁词设置</CardTitle>
        <CardDescription>
          管理全局违禁词列表，适用于直播话术和直播互动回复功能
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 添加违禁词 */}
        <div className="flex gap-2">
          <div className="flex-1">
            <Label htmlFor="new-word" className="sr-only">新违禁词</Label>
            <Input
              id="new-word"
              value={newWord}
              onChange={(e) => setNewWord(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleAddWord()}
              placeholder="输入违禁词..."
              className="flex-1"
            />
          </div>
          <Button onClick={handleAddWord} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            添加
          </Button>
          <BatchImportDialog
            onImport={handleBatchImport}
            trigger={
              <Button variant="outline" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                批量导入
              </Button>
            }
          />
        </div>

        {/* 违禁词列表 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">
              违禁词列表 ({forbiddenWords.length} 个)
            </Label>
            {forbiddenWords.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearAll}
                className="flex items-center gap-2 text-destructive hover:text-destructive"
              >
                <Trash2 className="h-3 w-3" />
                清空
              </Button>
            )}
          </div>

          {forbiddenWords.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>暂无违禁词</p>
              <p className="text-xs mt-1">添加违禁词来过滤不当内容</p>
            </div>
          ) : (
            <div className="border rounded-md p-3 max-h-[300px] overflow-y-auto bg-muted/50">
              <div className="flex flex-wrap gap-1">
                {forbiddenWords.map((word, index) => (
                  <div
                    key={index}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200 text-xs rounded group hover:bg-red-200 dark:hover:bg-red-900/30 transition-colors"
                  >
                    <span>{word}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 p-0 hover:bg-red-300 opacity-60 group-hover:opacity-100 transition-opacity"
                      onClick={() => handleRemoveWord(word)}
                      title="删除违禁词"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default ForbiddenWordsSettings
