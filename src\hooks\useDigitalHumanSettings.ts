import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { uploadDigitalHumanToComfyUI, verifyDigitalHumanExists } from '@/utils/digitalHumanUpload'

// 数字人形象文件信息接口
export interface DigitalHumanFileInfo {
  name: string
  displayName: string
  uploadTime: number
}

// 数字人形象状态接口
interface DigitalHumanSettingsState {
  // 数字人形象列表
  digitalHumanList: string[]
  // 加载状态
  isLoading: boolean
  // 上传状态
  isUploading: boolean
  // 数字人形象选择 - 分别为主播和助理设置
  hostSelectedDigitalHumans: string[] // 主播选中的数字人形象列表
  assistantSelectedDigitalHumans: string[] // 助理选中的数字人形象列表
  selectedDigitalHumans: string[] // 兼容性保留：当前选中的数字人形象列表

  // Actions
  actions: {
    // 加载数字人形象列表（从本地缓存）
    loadDigitalHumanList: () => Promise<void>
    // 添加数字人形象到本地列表
    addDigitalHumanToList: (fileName: string) => void
    // 上传数字人形象
    uploadDigitalHuman: (file: File) => Promise<void>
    // 删除数字人形象
    deleteDigitalHuman: (fileName: string) => Promise<void>
    // 验证数字人形象资源是否存在
    verifyDigitalHumanResources: () => Promise<void>
    // 主播数字人形象设置方法
    setHostSelectedDigitalHumans: (digitalHumans: string[]) => void
    toggleHostDigitalHumanSelection: (fileName: string) => void
    // 助理数字人形象设置方法
    setAssistantSelectedDigitalHumans: (digitalHumans: string[]) => void
    toggleAssistantDigitalHumanSelection: (fileName: string) => void
    // 兼容性方法
    toggleDigitalHumanSelection: (fileName: string) => void
    setSelectedDigitalHumans: (digitalHumans: string[]) => void
  }
}

// 获取随机选中的数字人形象
export function getRandomSelectedDigitalHuman(speaker?: 'host' | 'assistant'): string {
  const state = useDigitalHumanSettings.getState()

  // 根据说话者类型选择对应的数字人形象列表
  let selectedDigitalHumans: string[]
  if (speaker === 'assistant') {
    selectedDigitalHumans = state.assistantSelectedDigitalHumans.filter(dh => dh && dh.trim() !== '')
    console.log(`使用助理数字人形象列表: ${selectedDigitalHumans.length} 个形象`)
  } else {
    // 主播或未指定时使用主播数字人形象
    selectedDigitalHumans = state.hostSelectedDigitalHumans.filter(dh => dh && dh.trim() !== '') ||
                           state.selectedDigitalHumans.filter(dh => dh && dh.trim() !== '')
    console.log(`使用主播数字人形象列表: ${selectedDigitalHumans.length} 个形象`)
  }

  if (selectedDigitalHumans.length === 0) {
    console.warn(`没有选中的${speaker || 'host'}数字人形象，使用默认形象`)
    return '9.mp4' // 默认数字人形象
  }

  const randomIndex = Math.floor(Math.random() * selectedDigitalHumans.length)
  const selectedDigitalHuman = selectedDigitalHumans[randomIndex]
  console.log(`随机选择数字人形象: ${selectedDigitalHuman} (${speaker || 'host'}从 ${selectedDigitalHumans.length} 个形象中选择)`)
  return selectedDigitalHuman
}

export const useDigitalHumanSettings = create<DigitalHumanSettingsState>()(
  persist(
    immer((set, get) => ({
      // 初始状态
      digitalHumanList: ['9.mp4'], // 默认数字人形象
      isLoading: false,
      isUploading: false,
      // 数字人形象选择 - 分别为主播和助理设置
      hostSelectedDigitalHumans: ['9.mp4'], // 主播默认选中一个数字人形象
      assistantSelectedDigitalHumans: ['9.mp4'], // 助理默认选中一个数字人形象
      selectedDigitalHumans: ['9.mp4'], // 兼容性保留：默认选中一个数字人形象
      
      actions: {
        // 加载数字人形象列表（从本地缓存，无需网络请求）
        loadDigitalHumanList: async () => {
          set(state => {
            state.isLoading = true
          })

          try {
            // 数字人形象列表完全基于本地缓存
            // 这里只是模拟加载过程，实际数据已经在持久化存储中
            console.log('数字人形象列表加载完成（本地缓存）:', get().digitalHumanList)
          } catch (error) {
            console.error('加载数字人形象列表失败:', error)
          } finally {
            set(state => {
              state.isLoading = false
            })
          }
        },

        // 添加数字人形象到本地列表
        addDigitalHumanToList: (fileName: string) => {
          set(state => {
            if (!state.digitalHumanList.includes(fileName)) {
              state.digitalHumanList.push(fileName)
              console.log('添加数字人形象到本地列表:', fileName)
            }
          })
        },

        // 上传数字人形象
        uploadDigitalHuman: async (file: File) => {
          set(state => {
            state.isUploading = true
          })

          try {
            console.log('上传数字人形象:', file.name)

            // 使用专门的上传工具函数上传到服务器
            const result = await uploadDigitalHumanToComfyUI(file)
            console.log('数字人形象上传成功:', result)

            // 上传成功后，添加到本地列表中
            set(state => {
              if (!state.digitalHumanList.includes(file.name)) {
                state.digitalHumanList.push(file.name)
                console.log('数字人形象已添加到本地列表:', file.name)
              }
            })
          } catch (error) {
            console.error('上传数字人形象失败:', error)
            throw error
          } finally {
            set(state => {
              state.isUploading = false
            })
          }
        },

        // 删除数字人形象
        deleteDigitalHuman: async (fileName: string) => {
          try {
            console.log('从本地列表删除数字人形象:', fileName)

            set(state => {
              // 从本地列表中移除
              state.digitalHumanList = state.digitalHumanList.filter(dh => dh !== fileName)
              // 从所有选中列表中移除
              state.hostSelectedDigitalHumans = state.hostSelectedDigitalHumans.filter(dh => dh !== fileName)
              state.assistantSelectedDigitalHumans = state.assistantSelectedDigitalHumans.filter(dh => dh !== fileName)
              state.selectedDigitalHumans = state.selectedDigitalHumans.filter(dh => dh !== fileName)
            })

            console.log('数字人形象已从本地列表删除:', fileName)
          } catch (error) {
            console.error('删除数字人形象失败:', error)
            throw error
          }
        },

        // 验证数字人形象资源是否存在
        verifyDigitalHumanResources: async () => {
          set(state => {
            state.isLoading = true
          })

          try {
            const currentList = get().digitalHumanList
            const validList: string[] = []

            console.log('验证数字人形象资源:', currentList)

            // 验证每个数字人形象文件是否在服务器上存在
            for (const fileName of currentList) {
              if (fileName === '9.mp4') {
                // 默认形象总是有效的
                validList.push(fileName)
                continue
              }

              try {
                const exists = await verifyDigitalHumanExists(fileName)
                if (exists) {
                  validList.push(fileName)
                  console.log('数字人形象资源验证成功:', fileName)
                } else {
                  console.warn('数字人形象资源不存在:', fileName)
                }
              } catch (error) {
                console.warn('验证数字人形象资源失败:', fileName, error)
              }
            }

            set(state => {
              state.digitalHumanList = validList
              // 同时清理选中列表中无效的项目
              state.hostSelectedDigitalHumans = state.hostSelectedDigitalHumans.filter(dh => validList.includes(dh))
              state.assistantSelectedDigitalHumans = state.assistantSelectedDigitalHumans.filter(dh => validList.includes(dh))
              state.selectedDigitalHumans = state.selectedDigitalHumans.filter(dh => validList.includes(dh))
            })

            console.log('数字人形象资源验证完成，有效数量:', validList.length)
          } catch (error) {
            console.error('验证数字人形象资源失败:', error)
            throw error
          } finally {
            set(state => {
              state.isLoading = false
            })
          }
        },

        // 主播数字人形象设置方法
        setHostSelectedDigitalHumans: (digitalHumans: string[]) => {
          set(state => {
            state.hostSelectedDigitalHumans = digitalHumans
            // 同时更新兼容性字段
            state.selectedDigitalHumans = digitalHumans
          })
        },

        toggleHostDigitalHumanSelection: (fileName: string) => {
          set(state => {
            const index = state.hostSelectedDigitalHumans.indexOf(fileName)
            if (index > -1) {
              // 如果已选中，则取消选中
              state.hostSelectedDigitalHumans.splice(index, 1)
            } else {
              // 如果未选中，则添加到选中列表
              state.hostSelectedDigitalHumans.push(fileName)
            }
            // 同时更新兼容性字段
            state.selectedDigitalHumans = [...state.hostSelectedDigitalHumans]
          })
        },

        // 助理数字人形象设置方法
        setAssistantSelectedDigitalHumans: (digitalHumans: string[]) => {
          set(state => {
            state.assistantSelectedDigitalHumans = digitalHumans
          })
        },

        toggleAssistantDigitalHumanSelection: (fileName: string) => {
          set(state => {
            const index = state.assistantSelectedDigitalHumans.indexOf(fileName)
            if (index > -1) {
              // 如果已选中，则取消选中
              state.assistantSelectedDigitalHumans.splice(index, 1)
            } else {
              // 如果未选中，则添加到选中列表
              state.assistantSelectedDigitalHumans.push(fileName)
            }
          })
        },

        // 兼容性方法
        toggleDigitalHumanSelection: (fileName: string) => {
          set(state => {
            const index = state.selectedDigitalHumans.indexOf(fileName)
            if (index > -1) {
              // 如果已选中，则取消选中
              state.selectedDigitalHumans.splice(index, 1)
            } else {
              // 如果未选中，则添加到选中列表
              state.selectedDigitalHumans.push(fileName)
            }
            // 同时更新主播数字人形象（保持向后兼容）
            state.hostSelectedDigitalHumans = [...state.selectedDigitalHumans]
          })
        },

        // 设置选中的数字人形象列表
        setSelectedDigitalHumans: (digitalHumans: string[]) => {
          set(state => {
            state.selectedDigitalHumans = digitalHumans
            // 同时更新主播数字人形象（保持向后兼容）
            state.hostSelectedDigitalHumans = digitalHumans
          })
        },
      },
    })),
    {
      name: 'digital-human-settings-storage',
      partialize: (state) => ({
        digitalHumanList: state.digitalHumanList,
        hostSelectedDigitalHumans: state.hostSelectedDigitalHumans,
        assistantSelectedDigitalHumans: state.assistantSelectedDigitalHumans,
        selectedDigitalHumans: state.selectedDigitalHumans,
      }),
    }
  )
)

// 便捷的 hooks
export const useDigitalHumanList = () => useDigitalHumanSettings(state => state.digitalHumanList)
export const useDigitalHumanLoading = () => useDigitalHumanSettings(state => state.isLoading)
export const useDigitalHumanUploading = () => useDigitalHumanSettings(state => state.isUploading)
export const useSelectedDigitalHumans = () => useDigitalHumanSettings(state => state.selectedDigitalHumans)
export const useHostSelectedDigitalHumans = () => useDigitalHumanSettings(state => state.hostSelectedDigitalHumans)
export const useAssistantSelectedDigitalHumans = () => useDigitalHumanSettings(state => state.assistantSelectedDigitalHumans)
export const useDigitalHumanActions = () => useDigitalHumanSettings(state => state.actions)
