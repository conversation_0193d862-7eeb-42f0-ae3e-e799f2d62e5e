import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useTheme, type Theme } from '@/hooks/useTheme'
import { useToast } from '@/hooks/useToast'
import { Monitor, Moon, Sun } from 'lucide-react'

const themeOptions = [
  {
    value: 'light' as Theme,
    label: '明亮模式',
    icon: Sun,
    description: '使用明亮的界面主题'
  },
  {
    value: 'dark' as Theme,
    label: '暗黑模式',
    icon: Moon,
    description: '使用暗黑的界面主题'
  },
  {
    value: 'system' as Theme,
    label: '跟随系统',
    icon: Monitor,
    description: '根据系统设置自动切换主题'
  }
]

export function ThemeSetting() {
  const { theme, setTheme } = useTheme()
  const { toast } = useToast()

  const handleThemeChange = (newTheme: Theme) => {
    setTheme(newTheme)
    const selectedOption = themeOptions.find(option => option.value === newTheme)
    toast.success(`已切换到${selectedOption?.label}`)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>外观主题</CardTitle>
        <CardDescription>选择应用程序的外观主题</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label>主题模式</Label>
            <p className="text-sm text-muted-foreground">
              选择您偏好的界面主题
            </p>
          </div>
          <Select value={theme} onValueChange={handleThemeChange}>
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="选择主题" />
            </SelectTrigger>
            <SelectContent>
              {themeOptions.map(option => {
                const Icon = option.icon
                return (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <Icon className="h-4 w-4" />
                      <span>{option.label}</span>
                    </div>
                  </SelectItem>
                )
              })}
            </SelectContent>
          </Select>
        </div>

        {/* 主题预览 */}
        <div className="space-y-3">
          <Label>主题预览</Label>
          <div className="grid grid-cols-3 gap-3">
            {themeOptions.map(option => {
              const Icon = option.icon
              const isSelected = theme === option.value
              return (
                <div
                  key={option.value}
                  className={`
                    relative cursor-pointer rounded-lg border-2 p-3 transition-colors
                    ${isSelected
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:border-primary/50'
                    }
                  `}
                  onClick={() => handleThemeChange(option.value)}
                >
                  <div className="flex flex-col items-center gap-2">
                    <div className={`
                      rounded-full p-2 transition-colors
                      ${option.value === 'light' ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400' : ''}
                      ${option.value === 'dark' ? 'bg-slate-100 dark:bg-slate-900/20 text-slate-600 dark:text-slate-400' : ''}
                      ${option.value === 'system' ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : ''}
                    `}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-medium">{option.label}</div>
                      <div className="text-xs text-muted-foreground">
                        {option.description}
                      </div>
                    </div>
                  </div>
                  {isSelected && (
                    <div className="absolute top-2 right-2 h-2 w-2 rounded-full bg-primary" />
                  )}
                </div>
              )
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
