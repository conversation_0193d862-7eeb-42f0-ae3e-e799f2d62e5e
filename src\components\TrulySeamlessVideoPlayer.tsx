import React, { useRef, useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'

export interface VideoItem {
  id: string
  url: string
  text: string
}

export interface TrulySeamlessVideoPlayerProps {
  onVideoEnded?: (videoId: string) => void
  onError?: (error: Error, videoId?: string) => void
  className?: string
  autoPlay?: boolean
  muted?: boolean
  volume?: number
}

export interface TrulySeamlessVideoPlayerRef {
  playVideo: (video: VideoItem) => Promise<void>
  addToQueue: (video: VideoItem) => void
  setQueue: (videos: VideoItem[]) => void
  playNext: () => Promise<boolean>
  stop: () => void
  getState: () => any
  getCurrentText: () => string
  isPlaying: () => boolean
}

interface PlayerState {
  currentVideoId: string | null
  currentText: string
  isPlaying: boolean
  currentVideoIndex: number
  isTransitioning: boolean
}

/**
 * 真正无缝视频播放器
 * 使用Canvas渲染实现完全无缝的视频切换
 */
export const TrulySeamlessVideoPlayer = forwardRef<TrulySeamlessVideoPlayerRef, TrulySeamlessVideoPlayerProps>(({
  onVideoEnded,
  onError,
  className = '',
  autoPlay = true,
  muted = false,
  volume = 1
}, ref) => {
  // Canvas和视频元素引用
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const videoARef = useRef<HTMLVideoElement>(null)
  const videoBRef = useRef<HTMLVideoElement>(null)
  const animationFrameRef = useRef<number | undefined>(undefined)

  // 播放状态
  const [state, setState] = useState<PlayerState>({
    currentVideoId: null,
    currentText: '',
    isPlaying: false,
    currentVideoIndex: 0,
    isTransitioning: false
  })

  // 视频队列和预加载状态
  const [videoQueue, setVideoQueue] = useState<VideoItem[]>([])
  const [preloadedVideos, setPreloadedVideos] = useState<Map<string, HTMLVideoElement>>(new Map())

  // 切换参数
  const PRELOAD_THRESHOLD_MS = 1000 // 提前1秒开始预加载下一个视频
  const TRANSITION_DURATION_MS = 100 // 过渡时间100ms

  /**
   * 获取当前活跃的视频元素
   */
  const getCurrentVideo = useCallback((): HTMLVideoElement | null => {
    return state.currentVideoIndex % 2 === 0 ? videoARef.current : videoBRef.current
  }, [state.currentVideoIndex])

  /**
   * 获取下一个视频元素
   */
  const getNextVideo = useCallback((): HTMLVideoElement | null => {
    return (state.currentVideoIndex + 1) % 2 === 0 ? videoARef.current : videoBRef.current
  }, [state.currentVideoIndex])

  /**
   * Canvas渲染循环
   */
  const renderFrame = useCallback(() => {
    const canvas = canvasRef.current
    const currentVideo = getCurrentVideo()

    if (!canvas || !currentVideo) {
      animationFrameRef.current = requestAnimationFrame(renderFrame)
      return
    }

    const ctx = canvas.getContext('2d')
    if (!ctx) {
      animationFrameRef.current = requestAnimationFrame(renderFrame)
      return
    }

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 如果正在过渡，实现交叉淡化
    if (state.isTransitioning) {
      const nextVideo = getNextVideo()
      if (nextVideo && nextVideo.readyState >= 2) {
        // 计算过渡进度（这里简化为固定过渡）
        const alpha = 0.5 // 可以根据时间计算渐变

        // 绘制当前视频
        ctx.globalAlpha = 1 - alpha
        if (currentVideo.readyState >= 2) {
          ctx.drawImage(currentVideo, 0, 0, canvas.width, canvas.height)
        }

        // 绘制下一个视频
        ctx.globalAlpha = alpha
        ctx.drawImage(nextVideo, 0, 0, canvas.width, canvas.height)

        ctx.globalAlpha = 1
      } else {
        // 只绘制当前视频
        if (currentVideo.readyState >= 2) {
          ctx.drawImage(currentVideo, 0, 0, canvas.width, canvas.height)
        }
      }
    } else {
      // 正常播放，只绘制当前视频
      if (currentVideo.readyState >= 2) {
        ctx.drawImage(currentVideo, 0, 0, canvas.width, canvas.height)
      }
    }

    // 继续下一帧
    if (state.isPlaying) {
      animationFrameRef.current = requestAnimationFrame(renderFrame)
    }
  }, [getCurrentVideo, getNextVideo, state.isPlaying, state.isTransitioning])

  /**
   * 预加载视频
   */
  const preloadVideo = useCallback(async (video: VideoItem): Promise<HTMLVideoElement | null> => {
    // 检查是否已经预加载
    if (preloadedVideos.has(video.id)) {
      return preloadedVideos.get(video.id)!
    }

    console.log(`开始预加载视频: ${video.text}`)

    const videoElement = document.createElement('video')
    videoElement.crossOrigin = 'anonymous'
    videoElement.preload = 'auto'
    videoElement.muted = muted
    videoElement.volume = volume

    return new Promise((resolve) => {
      const onCanPlayThrough = () => {
        console.log(`视频预加载完成: ${video.text}`)
        videoElement.removeEventListener('canplaythrough', onCanPlayThrough)
        videoElement.removeEventListener('error', onError)

        setPreloadedVideos(prev => new Map(prev).set(video.id, videoElement))
        resolve(videoElement)
      }

      const onError = () => {
        console.error(`视频预加载失败: ${video.text}`)
        videoElement.removeEventListener('canplaythrough', onCanPlayThrough)
        videoElement.removeEventListener('error', onError)
        resolve(null)
      }

      videoElement.addEventListener('canplaythrough', onCanPlayThrough)
      videoElement.addEventListener('error', onError)

      videoElement.src = video.url
      videoElement.load()

      // 超时处理
      setTimeout(() => {
        videoElement.removeEventListener('canplaythrough', onCanPlayThrough)
        videoElement.removeEventListener('error', onError)
        console.warn(`视频预加载超时: ${video.text}`)
        resolve(null)
      }, 10000)
    })
  }, [muted, volume, preloadedVideos])

  /**
   * 播放指定视频
   */
  const playVideo = useCallback(async (video: VideoItem): Promise<void> => {
    const targetVideo = getCurrentVideo()
    if (!targetVideo) {
      throw new Error('没有可用的视频元素')
    }

    console.log(`开始播放视频: ${video.text}`)

    try {
      // 检查是否有预加载的视频
      const preloadedVideo = preloadedVideos.get(video.id)

      if (preloadedVideo) {
        console.log('使用预加载的视频')
        // 将预加载的视频内容复制到目标视频元素
        targetVideo.src = preloadedVideo.src
        targetVideo.currentTime = 0
      } else {
        console.log('直接加载视频')
        targetVideo.src = video.url
      }

      targetVideo.load()

      // 等待视频准备就绪
      await new Promise<void>((resolve, reject) => {
        const onCanPlay = () => {
          targetVideo.removeEventListener('canplay', onCanPlay)
          targetVideo.removeEventListener('error', onError)
          resolve()
        }

        const onError = (e: Event) => {
          targetVideo.removeEventListener('canplay', onCanPlay)
          targetVideo.removeEventListener('error', onError)
          reject(new Error('视频加载失败'))
        }

        targetVideo.addEventListener('canplay', onCanPlay)
        targetVideo.addEventListener('error', onError)
      })

      // 开始播放
      await targetVideo.play()

      // 更新状态
      setState(prev => ({
        ...prev,
        currentVideoId: video.id,
        currentText: video.text,
        isPlaying: true
      }))

      // 开始Canvas渲染
      if (!animationFrameRef.current) {
        renderFrame()
      }

      console.log(`视频开始播放: ${video.text}`)
    } catch (error) {
      console.error('播放视频失败:', error)
      onError?.(error as Error, video.id)
      throw error
    }
  }, [getCurrentVideo, preloadedVideos, renderFrame, onError])

  /**
   * 添加视频到播放队列
   */
  const addToQueue = useCallback((video: VideoItem) => {
    setVideoQueue(prev => [...prev, video])
  }, [])

  /**
   * 设置播放队列
   */
  const setQueue = useCallback((videos: VideoItem[]) => {
    setVideoQueue(videos)
    setPreloadedVideos(new Map())
  }, [])

  /**
   * 播放队列中的下一个视频
   */
  const playNext = useCallback(async (): Promise<boolean> => {
    if (videoQueue.length === 0) {
      console.log('播放队列为空')
      setState(prev => ({ ...prev, isPlaying: false }))
      return false
    }

    const nextVideo = videoQueue[0]
    setVideoQueue(prev => prev.slice(1))

    try {
      // 切换到下一个视频元素
      setState(prev => ({
        ...prev,
        currentVideoIndex: prev.currentVideoIndex + 1,
        isTransitioning: true
      }))

      await playVideo(nextVideo)

      // 结束过渡
      setTimeout(() => {
        setState(prev => ({ ...prev, isTransitioning: false }))
      }, TRANSITION_DURATION_MS)

      return true
    } catch (error) {
      console.error('播放下一个视频失败:', error)
      setState(prev => ({ ...prev, isTransitioning: false }))
      return false
    }
  }, [videoQueue, playVideo])

  // 监听视频播放事件
  useEffect(() => {
    const currentVideo = getCurrentVideo()
    if (!currentVideo) return

    const handleTimeUpdate = () => {
      const currentTime = currentVideo.currentTime
      const duration = currentVideo.duration

      if (duration && currentTime > 0) {
        const remainingTime = (duration - currentTime) * 1000

        // 提前预加载下一个视频
        if (remainingTime <= PRELOAD_THRESHOLD_MS && videoQueue.length > 0) {
          const nextVideo = videoQueue[0]
          if (!preloadedVideos.has(nextVideo.id)) {
            preloadVideo(nextVideo)
          }
        }
      }
    }

    const handleEnded = () => {
      console.log('当前视频播放结束')

      if (state.currentVideoId) {
        onVideoEnded?.(state.currentVideoId)
      }

      // 自动播放下一个视频
      if (videoQueue.length > 0) {
        playNext()
      } else {
        setState(prev => ({ ...prev, isPlaying: false }))
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current)
          animationFrameRef.current = undefined
        }
      }
    }

    currentVideo.addEventListener('timeupdate', handleTimeUpdate)
    currentVideo.addEventListener('ended', handleEnded)

    return () => {
      currentVideo.removeEventListener('timeupdate', handleTimeUpdate)
      currentVideo.removeEventListener('ended', handleEnded)
    }
  }, [getCurrentVideo, videoQueue, preloadedVideos, state.currentVideoId, onVideoEnded, playNext, preloadVideo])

  // 设置Canvas尺寸
  useEffect(() => {
    const canvas = canvasRef.current
    if (canvas) {
      // 设置Canvas的显示尺寸和内部分辨率
      const rect = canvas.getBoundingClientRect()
      canvas.width = rect.width * window.devicePixelRatio
      canvas.height = rect.height * window.devicePixelRatio

      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.scale(window.devicePixelRatio, window.devicePixelRatio)
      }
    }
  }, [])

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    playVideo,
    addToQueue,
    setQueue,
    playNext,
    stop: () => {
      const currentVideo = getCurrentVideo()
      if (currentVideo) {
        currentVideo.pause()
      }
      setState(prev => ({ ...prev, isPlaying: false }))
      setVideoQueue([])
      setPreloadedVideos(new Map())

      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
        animationFrameRef.current = undefined
      }
    },
    getState: () => state,
    getCurrentText: () => state.currentText,
    isPlaying: () => state.isPlaying
  }), [playVideo, addToQueue, setQueue, playNext, getCurrentVideo, state])

  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  return (
    <div className={`relative w-full h-full ${className}`}>
      {/* Canvas渲染层 */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full object-contain bg-black"
        style={{ width: '100%', height: '100%' }}
      />

      {/* 隐藏的视频元素 */}
      <video
        ref={videoARef}
        className="hidden"
        muted={muted}
        autoPlay={autoPlay}
        playsInline
      />

      <video
        ref={videoBRef}
        className="hidden"
        muted={muted}
        autoPlay={autoPlay}
        playsInline
      />

      {/* 调试信息（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs p-2 rounded">
          <div>当前视频: {state.currentVideoIndex % 2 === 0 ? 'A' : 'B'}</div>
          <div>视频ID: {state.currentVideoId}</div>
          <div>队列长度: {videoQueue.length}</div>
          <div>预加载: {preloadedVideos.size}</div>
          <div>过渡中: {state.isTransitioning ? '是' : '否'}</div>
        </div>
      )}
    </div>
  )
})

TrulySeamlessVideoPlayer.displayName = 'TrulySeamlessVideoPlayer'

export default TrulySeamlessVideoPlayer
