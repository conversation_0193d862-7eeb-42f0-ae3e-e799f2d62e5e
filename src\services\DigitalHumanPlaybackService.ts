/**
 * 数字人播放服务
 * 优化的播放方案，支持预加载和播放列表
 */

import { playlistManager, type PlaylistItem } from '@/utils/PlaylistManager'
import { videoPreloader } from '@/utils/VideoPreloader'

export interface DigitalHumanVideo {
  id: string
  videoUrl: string
  text: string
}

export class DigitalHumanPlaybackService {
  private static instance: DigitalHumanPlaybackService
  private isInitialized = false
  private onVideoCompleted: ((videoId: string) => void) | null = null

  static getInstance(): DigitalHumanPlaybackService {
    if (!DigitalHumanPlaybackService.instance) {
      DigitalHumanPlaybackService.instance = new DigitalHumanPlaybackService()
    }
    return DigitalHumanPlaybackService.instance
  }

  /**
   * 初始化服务
   */
  async initialize() {
    if (this.isInitialized) return

    console.log('初始化数字人播放服务')

    // 监听播放列表变化，自动预加载
    playlistManager.addListener((state) => {
      this.handlePlaylistChange(state)
    })

    this.isInitialized = true
  }

  /**
   * 设置视频播放完成回调
   */
  setOnVideoCompleted(callback: (videoId: string) => void) {
    this.onVideoCompleted = callback
  }

  /**
   * 处理播放列表变化
   */
  private async handlePlaylistChange(state: any) {
    // 预加载当前和下一个视频
    if (state.currentItem) {
      this.preloadVideo(state.currentItem)
    }

    const nextItem = playlistManager.getNextItem()
    if (nextItem) {
      this.preloadVideo(nextItem)
    }
  }

  /**
   * 预加载视频
   */
  private async preloadVideo(item: PlaylistItem) {
    try {
      await videoPreloader.preloadVideo(item.id, item.videoUrl, item.text)
    } catch (error) {
      console.error('预加载视频失败:', error)
    }
  }

  /**
   * 设置播放列表并开始播放
   */
  async setPlaylistAndStart(videos: DigitalHumanVideo[]): Promise<void> {
    if (videos.length === 0) {
      console.log('数字人视频列表为空')
      return
    }

    console.log(`设置数字人播放列表，共 ${videos.length} 个视频`)

    // 转换为播放列表格式
    const playlistItems: PlaylistItem[] = videos.map(video => ({
      id: video.id,
      videoUrl: video.videoUrl,
      text: video.text
    }))

    // 设置播放列表
    playlistManager.setPlaylist(playlistItems)

    // 确保OBS播放器已打开
    await this.ensureOBSPlayerOpen()

    // 开始播放第一个视频
    await this.startPlayback()
  }

  /**
   * 添加视频到播放列表
   */
  async addToPlaylist(video: DigitalHumanVideo): Promise<void> {
    const playlistItem: PlaylistItem = {
      id: video.id,
      videoUrl: video.videoUrl,
      text: video.text
    }

    playlistManager.addToPlaylist(playlistItem)

    // 如果当前没有在播放，开始播放
    const state = playlistManager.getState()
    if (!state.isPlaying && state.currentItem) {
      await this.ensureOBSPlayerOpen()
      await this.startPlayback()
    }
  }

  /**
   * 开始播放
   */
  private async startPlayback(): Promise<void> {
    const currentItem = playlistManager.getCurrentItem()
    if (!currentItem) {
      console.log('没有可播放的视频')
      return
    }

    console.log(`开始播放数字人视频: ${currentItem.text}`)
    playlistManager.setPlayingState(true)

    try {
      // 尝试使用预加载的视频
      const preloadedVideo = videoPreloader.getPreloadedVideo(currentItem.id)
      
      if (preloadedVideo && preloadedVideo.isLoaded) {
        console.log('使用预加载的视频')
        await this.playPreloadedVideo(preloadedVideo)
      } else {
        console.log('直接播放视频（未预加载）')
        await this.playVideoDirectly(currentItem)
      }

      // 播放完成，移除预加载的视频
      videoPreloader.removePreloadedVideo(currentItem.id)

      // 通知主窗口移除对应的输出列表项目
      if (this.onVideoCompleted) {
        this.onVideoCompleted(currentItem.id)
      }

      // 自动播放下一个
      const hasNext = playlistManager.onCurrentVideoEnded()
      if (hasNext) {
        // 递归播放下一个
        await this.startPlayback()
      } else {
        console.log('播放列表播放完成')
        playlistManager.setPlayingState(false)
      }

    } catch (error) {
      console.error('播放视频失败:', error)
      playlistManager.setPlayingState(false)
    }
  }

  /**
   * 播放预加载的视频
   */
  private async playPreloadedVideo(preloadedVideo: any): Promise<void> {
    console.log('通过IPC播放预加载的视频')

    // 使用IPC通信而不是直接操作DOM
    try {
      await (window as any).ipcRenderer.invoke('obsPlayer:playVideo',
        preloadedVideo.url,
        preloadedVideo.text,
        preloadedVideo.id
      )
      console.log('预加载视频播放完成')
    } catch (error) {
      console.error('预加载视频播放失败:', error)
      throw error
    }
  }

  /**
   * 直接播放视频
   */
  private async playVideoDirectly(item: PlaylistItem): Promise<void> {
    console.log('通过IPC直接播放视频')

    try {
      await (window as any).ipcRenderer.invoke('obsPlayer:playVideo',
        item.videoUrl,
        item.text,
        item.id
      )
      console.log('直接播放视频完成')
    } catch (error) {
      console.error('直接播放视频失败:', error)
      throw error
    }
  }



  /**
   * 确保OBS播放器已打开
   */
  private async ensureOBSPlayerOpen(): Promise<void> {
    try {
      console.log('确保OBS播放器已打开')

      // 从localStorage读取保存的比例设置
      let aspectRatio: '16:9' | '9:16' = '9:16' // 默认值
      try {
        const obsPlayerSettings = localStorage.getItem('obs-player-settings')
        if (obsPlayerSettings) {
          const settings = JSON.parse(obsPlayerSettings)
          if (settings.state && settings.state.aspectRatio) {
            aspectRatio = settings.state.aspectRatio
          }
        }
      } catch (error) {
        console.warn('读取OBS播放器设置失败，使用默认比例:', error)
      }

      // 通过IPC打开OBS播放器，传递比例设置
      await (window as any).ipcRenderer.invoke('obsPlayer:open', aspectRatio)

      // 等待窗口加载
      console.log('等待5秒让OBS播放器完全加载')
      await new Promise(resolve => setTimeout(resolve, 5000))

      console.log('OBS播放器准备就绪')
    } catch (error) {
      console.error('打开OBS播放器失败:', error)
    }
  }

  /**
   * 停止播放
   */
  async stopPlayback(): Promise<void> {
    console.log('停止数字人播放')

    playlistManager.setPlayingState(false)
    playlistManager.clear()
    videoPreloader.cleanup()

    // 不关闭OBS播放器，保持窗口打开
    console.log('数字人播放已停止，OBS播放器保持打开状态')
  }

  /**
   * 跳转到下一个视频
   */
  async playNext(): Promise<void> {
    if (playlistManager.playNext()) {
      await this.startPlayback()
    }
  }

  /**
   * 跳转到上一个视频
   */
  async playPrevious(): Promise<void> {
    if (playlistManager.playPrevious()) {
      await this.startPlayback()
    }
  }

  /**
   * 获取播放状态
   */
  getPlaybackState() {
    return {
      ...playlistManager.getState(),
      progress: playlistManager.getProgress(),
      preloadStatus: videoPreloader.getPreloadStatus()
    }
  }
}

// 全局单例
export const digitalHumanPlaybackService = DigitalHumanPlaybackService.getInstance()
