import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/useToast'
import { useAutoReply } from '@/hooks/useAutoReply'
import { useAIChatStore } from '@/hooks/useAIChat'
import { useForbiddenWords } from '@/hooks/useForbiddenWords'
import { getForbiddenWordsInText } from '@/utils/filter'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import {
  ChevronDown,
  ChevronRight,
  Edit3,
  FilterIcon,
  Loader2,
  Plus,
  Sparkles,
  Trash,
  Trash2Icon,
  X,
  CheckCircle,
  XCircle,
} from 'lucide-react'

const KeywordReplySimple = () => {
  const { config, updateKeywordRules } = useAutoReply()
  const { toast } = useToast()
  const aiStore = useAIChatStore()
  const { forbiddenWords } = useForbiddenWords()

  // 使用本地状态来编辑规则
  const [rules, setRules] = useState(config.comment.keywordReply.rules)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // AI泛化相关状态
  const [generalizingRules, setGeneralizingRules] = useState<Set<number>>(new Set())
  const [generalizationPrompt, setGeneralizationPrompt] = useState(() => {
    const saved = localStorage.getItem('keyword-reply-generalization-prompt')
    return saved || `你是一个直播带货主播。请基于以下回复内容，生成5-8个语义相似但表达方式不同的变体。要求：
1. 保持原意不变
2. 语气风格保持一致
3. 适合直播间回复场景
4. 每个变体独占一行
5. 不要添加编号或其他格式
6. 语气更加热情一些，不要出现多表情符号

原始回复内容：
{original_content}`
  })

  // 展开/折叠状态 - 默认展开第一个规则
  const [expandedRules, setExpandedRules] = useState<Set<number>>(new Set([0]))

  // AI泛化提示词弹窗状态
  const [showPromptDialog, setShowPromptDialog] = useState(false)
  const [tempPrompt, setTempPrompt] = useState('')

  // 自动保存规则变化
  useEffect(() => {
    if (hasUnsavedChanges) {
      const timer = setTimeout(() => {
        console.log('自动保存规则:', rules)
        updateKeywordRules(rules)
        setHasUnsavedChanges(false)
      }, 500) // 500ms 防抖

      return () => clearTimeout(timer)
    }
  }, [rules, hasUnsavedChanges])

  // 更新本地规则状态
  const updateLocalRules = (newRules: typeof rules) => {
    console.log('updateLocalRules 被调用:', newRules)
    setRules(newRules)
    setHasUnsavedChanges(true)
    console.log('hasUnsavedChanges 设置为 true，将在500ms后自动保存')
  }

  const updateRuleNestedArray = (
    ruleIndex: number,
    key: 'keywords' | 'contents',
    updateFn: (currentArray: string[]) => string[], // 函数接收当前数组，返回新数组
  ) => {
    const newRules = rules.map((rule, index) => {
      if (index === ruleIndex) {
        const currentArray = rule[key]
        const newArray = updateFn(currentArray)
        return {
          ...rule,
          [key]: newArray, // 使用计算属性名
        }
      }
      return rule
    })

    updateLocalRules(newRules)
  }

  // 添加新规则
  const addRule = () => {
    updateLocalRules([...rules, { keywords: [], contents: [] }])
  }

  // 删除规则
  const removeRule = (index: number) => {
    const newRules = rules.filter((_, i) => i !== index)
    updateLocalRules(newRules)
  }

  // 清空所有规则
  const clearAllRules = () => {
    if (rules.length === 0) {
      toast.error('没有规则可以清空')
      return
    }

    if (confirm('确定要清空所有规则吗？此操作不可撤销。')) {
      updateLocalRules([])
      toast.success('所有规则已清空')
    }
  }

  // 切换规则展开/折叠状态
  const toggleRuleExpanded = (ruleIndex: number) => {
    setExpandedRules(prev => {
      const newSet = new Set(prev)
      if (newSet.has(ruleIndex)) {
        newSet.delete(ruleIndex)
      } else {
        newSet.add(ruleIndex)
      }
      return newSet
    })
  }

  // 处理AI泛化提示词弹窗
  const handleOpenPromptDialog = () => {
    setTempPrompt(generalizationPrompt)
    setShowPromptDialog(true)
  }

  const handleSavePrompt = () => {
    setGeneralizationPrompt(tempPrompt)
    localStorage.setItem('keyword-reply-generalization-prompt', tempPrompt)
    setShowPromptDialog(false)
    toast.success('AI泛化提示词已保存')
  }

  const handleCancelPrompt = () => {
    setShowPromptDialog(false)
    setTempPrompt('')
  }

  // 添加回复内容
  const addContent = (ruleIndex: number, content: string) => {
    const trimmedContent = content.trim()
    if (!trimmedContent) {
      toast.error('回复内容不能为空')
      return
    }

    updateRuleNestedArray(ruleIndex, 'contents', currentContents => {
      if (currentContents.includes(trimmedContent)) {
        toast.error('该回复内容已存在')
        return currentContents
      }
      return [...currentContents, trimmedContent]
    })
    toast.success('回复内容已添加')
  }

  // 删除回复内容
  const removeContent = (ruleIndex: number, contentIndex: number) => {
    updateRuleNestedArray(ruleIndex, 'contents', currentContents =>
      currentContents.filter((_, i) => i !== contentIndex)
    )
    toast.success('回复内容已删除')
  }

  // AI泛化回复内容
  const generalizeContents = async (ruleIndex: number) => {
    const rule = rules[ruleIndex]
    if (!rule.contents.length) {
      toast.error('请先添加至少一条回复内容')
      return
    }

    // 使用完整的提示词，替换占位符
    const prompt = generalizationPrompt.replace('{original_content}', rule.contents[0])

    const messages = [
      {
        role: 'user' as const,
        content: prompt,
      },
    ]

    const { provider } = aiStore.config
    const apiKey = aiStore.apiKeys[provider]
    const customBaseURL = aiStore.customBaseURL

    if (!apiKey) {
      toast.error('请先配置AI API密钥')
      return
    }

    setGeneralizingRules(prev => new Set([...prev, ruleIndex]))

    try {
      const response = await window.ipcRenderer.invoke(IPC_CHANNELS.tasks.aiChat.normalChat, {
        messages,
        apiKey,
        provider: aiStore.config.provider,
        model: aiStore.config.model,
        customBaseURL: aiStore.config.provider === 'custom' ? customBaseURL : undefined,
      })

      if (!response) {
        throw new Error('AI服务返回空结果')
      }

      // 解析AI返回的结果，按行分割
      const generatedContents = response
        .split('\n')
        .map((line: string) => line.trim())
        .filter((line: string) => line.length > 0 && !line.match(/^\d+\./)) // 过滤空行和编号
        .slice(0, 8) // 最多取8个变体

      if (generatedContents.length > 0) {
        updateRuleNestedArray(ruleIndex, 'contents', currentContents => [
          ...currentContents,
          ...generatedContents,
        ])
        toast.success(`成功生成 ${generatedContents.length} 条回复变体`)
      } else {
        toast.error('AI生成的内容为空')
      }
    } catch (error) {
      console.error('AI泛化错误:', error)
      toast.error('AI泛化过程中发生错误')
    } finally {
      setGeneralizingRules(prev => {
        const newSet = new Set(prev)
        newSet.delete(ruleIndex)
        return newSet
      })
    }
  }

  console.log('KeywordReplySimple 渲染:', { hasUnsavedChanges, rulesLength: rules.length })

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium">关键词回复规则</h3>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleOpenPromptDialog}
            className="flex items-center gap-1"
          >
            <Sparkles className="h-4 w-4" /> AI泛化设置
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={clearAllRules}
            className="flex items-center gap-1 text-destructive hover:text-destructive"
          >
            <Trash2Icon className="h-4 w-4" /> 清空
          </Button>
          <Button size="sm" onClick={addRule} className="flex items-center gap-1">
            <Plus className="h-4 w-4" /> 添加规则
          </Button>
        </div>
      </div>



      {rules.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <div className="mb-4">
            <FilterIcon className="h-12 w-12 mx-auto opacity-50" />
          </div>
          <p className="mb-4">还没有关键词回复规则</p>
          <Button onClick={addRule} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            创建第一个规则
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          {rules.map((rule, ruleIndex) => (
            // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
            <Card key={ruleIndex} className="border">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => toggleRuleExpanded(ruleIndex)}
                    >
                      {expandedRules.has(ruleIndex) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                    <span
                      className="text-sm font-medium text-muted-foreground cursor-pointer hover:text-foreground transition-colors"
                      onClick={() => toggleRuleExpanded(ruleIndex)}
                    >
                      关键字{ruleIndex + 1}
                    </span>
                    <div className="flex items-center gap-2">
                      <Input
                        placeholder="输入关键词..."
                        value={rule.keywords[0] || ''}
                        onChange={e => {
                          const newKeyword = e.target.value
                          updateRuleNestedArray(ruleIndex, 'keywords', () =>
                            newKeyword ? [newKeyword] : []
                          )
                        }}
                        className="h-8 text-sm min-w-[280px]"
                      />
                      <Edit3 className="h-4 w-4 text-muted-foreground" />
                    </div>
                    {rule.contents.length > 0 && (
                      <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                        {rule.contents.length} 条回复
                      </span>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 hover:bg-destructive/10"
                    onClick={() => {
                      if (rule.keywords.length > 0 || rule.contents.length > 0) {
                        if (confirm('确定要删除这个规则吗？此操作不可撤销。')) {
                          removeRule(ruleIndex)
                          toast.success('规则已删除')
                        }
                      } else {
                        removeRule(ruleIndex)
                        toast.success('规则已删除')
                      }
                    }}
                    title="删除规则"
                  >
                    <Trash className="h-4 w-4 text-destructive" />
                  </Button>
                </div>
              </CardHeader>

              {expandedRules.has(ruleIndex) && (
                <CardContent className="pt-0">
                  {/* 回复内容列表 */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">回复内容</h4>
                      {rule.contents.length > 0 && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-7 text-xs"
                          disabled={generalizingRules.has(ruleIndex)}
                          onClick={() => generalizeContents(ruleIndex)}
                        >
                          {generalizingRules.has(ruleIndex) ? (
                            <>
                              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                              泛化中...
                            </>
                          ) : (
                            <>
                              <Sparkles className="h-3 w-3 mr-1" />
                              AI泛化
                            </>
                          )}
                        </Button>
                      )}
                    </div>

                    <div className="space-y-2">
                      {rule.contents.map((content, contentIndex) => {
                        // 检查当前回复内容中的违禁词
                        const forbiddenWordsInContent = getForbiddenWordsInText(content, forbiddenWords)
                        const hasForbiddenWords = forbiddenWordsInContent.length > 0

                        return (
                          <div
                            // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                            key={contentIndex}
                            className={`relative flex items-start gap-2 p-3 bg-muted/30 rounded border ${hasForbiddenWords ? 'border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/20' : ''}`}
                          >
                            {/* 左上角悬浮的违禁词检查图标 */}
                            <div
                              className="absolute left-1 top-1 flex items-center justify-center w-4 h-4 bg-white/90 backdrop-blur-sm rounded-full shadow-sm border z-10"
                              title={hasForbiddenWords ? `包含违禁词：${forbiddenWordsInContent.join('、')}` : "内容安全"}
                            >
                              {hasForbiddenWords ? (
                                <XCircle className="h-2.5 w-2.5 text-red-500" />
                              ) : (
                                <CheckCircle className="h-2.5 w-2.5 text-green-500" />
                              )}
                            </div>

                            <Input
                              value={content}
                              onChange={e => {
                                const newContent = e.target.value
                                updateRuleNestedArray(ruleIndex, 'contents', currentContents =>
                                  currentContents.map((c, i) => i === contentIndex ? newContent : c)
                                )
                              }}
                              className={`flex-1 text-sm ${hasForbiddenWords ? 'border-red-500 focus:border-red-500' : ''}`}
                              placeholder="输入回复内容..."
                            />

                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 hover:bg-destructive/10 flex-shrink-0"
                              onClick={() => removeContent(ruleIndex, contentIndex)}
                              title="删除回复内容"
                            >
                              <X className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>
                        )
                      })}

                      {/* 添加新回复内容 */}
                      <AddContentInput
                        ruleIndex={ruleIndex}
                        onAddContent={addContent}
                        forbiddenWords={forbiddenWords}
                      />
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* AI泛化提示词设置弹窗 */}
      <Dialog open={showPromptDialog} onOpenChange={setShowPromptDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>AI泛化提示词设置</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Textarea
              placeholder="输入完整的AI泛化提示词..."
              value={tempPrompt}
              onChange={e => setTempPrompt(e.target.value)}
              className="text-sm min-h-[200px]"
            />
            <p className="text-xs text-muted-foreground">
              完整的AI泛化提示词，使用 <code>{'{original_content}'}</code> 作为原始内容的占位符。此设置将应用于所有规则的AI泛化。
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelPrompt}>
              取消
            </Button>
            <Button onClick={handleSavePrompt}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// 添加回复内容输入组件
const AddContentInput: React.FC<{
  ruleIndex: number
  onAddContent: (ruleIndex: number, content: string) => void
  forbiddenWords: string[]
}> = ({ ruleIndex, onAddContent, forbiddenWords }) => {
  const [inputValue, setInputValue] = useState('')

  // 检查输入内容中的违禁词
  const forbiddenWordsInInput = getForbiddenWordsInText(inputValue, forbiddenWords)
  const hasForbiddenWords = forbiddenWordsInInput.length > 0

  const handleAddContent = () => {
    const value = inputValue.trim()
    if (value) {
      onAddContent(ruleIndex, value)
      setInputValue('')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddContent()
    }
  }

  return (
    <div className="flex gap-2 relative">
      <div className="flex-1 relative">
        <Input
          placeholder="输入新回复内容..."
          value={inputValue}
          onChange={e => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          className={`h-9 text-sm pr-8 ${hasForbiddenWords ? 'border-red-500 focus:border-red-500' : ''}`}
        />
        {/* 违禁词检查图标 */}
        {inputValue.trim() && (
          <div
            className="absolute top-1/2 right-2 transform -translate-y-1/2 flex items-center"
            title={hasForbiddenWords ? `包含违禁词：${forbiddenWordsInInput.join('、')}` : "内容安全"}
          >
            {hasForbiddenWords ? (
              <XCircle className="h-4 w-4 text-red-500" />
            ) : (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
          </div>
        )}
      </div>
      <Button
        variant="outline"
        onClick={handleAddContent}
        disabled={!inputValue.trim()}
      >
        添加
      </Button>
    </div>
  )
}

export default KeywordReplySimple
