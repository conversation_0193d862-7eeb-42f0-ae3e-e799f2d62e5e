import { TaskButton, ConnectLiveButton } from '@/components/common/TaskButton'
import { Title } from '@/components/common/Title'
import { useAutoReply } from '@/hooks/useAutoReply'
import { useAutoTimeAnnouncementActions, useCurrentAutoTimeAnnouncement } from '@/hooks/useAutoTimeAnnouncement'
import { useCurrentLiveControl } from '@/hooks/useLiveControl'
import { useToast } from '@/hooks/useToast'
import CommentList from '@/pages/AutoReply/components/CommentList'
import PreviewList from '@/pages/AutoReply/components/PreviewList'
import { useState } from 'react'
import { IPC_CHANNELS } from 'shared/ipcChannels'

export default function AutoReply() {
  const { isRunning, isListening, setIsRunning, setIsListening, config } = useAutoReply()
  const isConnected = useCurrentLiveControl(context => context.isConnected)
  const { toast } = useToast()

  // 自动报时相关状态
  const timeAnnouncementIsRunning = useCurrentAutoTimeAnnouncement(context => context.isRunning)
  const timeAnnouncementConfig = useCurrentAutoTimeAnnouncement(context => context.config)
  const { setIsRunning: setTimeAnnouncementIsRunning } = useAutoTimeAnnouncementActions()

  const [highlightedCommentId, setHighlightedCommentId] = useState<
    string | null
  >(null)

  const handleAutoReplyToggle = async () => {
    try {
      if (!isRunning) {
        // 开始运行：自动启动消息弹幕监听（如果没有正在监听）
        if (isListening !== 'listening') {
          if (isConnected !== 'connected') {
            toast.error('请先连接直播间')
            return
          }

          try {
            setIsListening('waiting')
            const result = await window.ipcRenderer.invoke(
              IPC_CHANNELS.tasks.autoReply.startCommentListener,
              {
                source: config.entry,
                ws: config.ws?.enable ? { port: config.ws.port } : undefined,
              },
            )
            if (!result) {
              setIsListening('error')
              toast.error('启动消息监听失败')
              return
            }
            setIsListening('listening')
            toast.success('已自动启动消息监听')
          } catch (error) {
            setIsListening('error')
            toast.error('启动消息监听失败')
            return
          }
        }

        // 自动启动自动报时（如果启用且没有正在运行）
        if (timeAnnouncementConfig.enabled && !timeAnnouncementIsRunning) {
          const enabledMessages = timeAnnouncementConfig.messages.filter(msg => msg.enabled && msg.content.trim())
          if (enabledMessages.length > 0) {
            try {
              const timeAnnouncementResult = await window.ipcRenderer.invoke(
                IPC_CHANNELS.tasks.autoTimeAnnouncement.start,
                timeAnnouncementConfig,
              )
              if (timeAnnouncementResult) {
                setTimeAnnouncementIsRunning(true)
                toast.success('已自动启动自动报时')
              } else {
                toast.error('启动自动报时失败')
              }
            } catch (error) {
              toast.error('启动自动报时失败')
            }
          }
        }

        setIsRunning(true)
      } else {
        // 停止运行：自动停止消息弹幕监听（如果正在监听）
        if (isListening === 'listening') {
          try {
            await window.ipcRenderer.invoke(
              IPC_CHANNELS.tasks.autoReply.stopCommentListener,
            )
            setIsListening('stopped')
            toast.success('已自动停止消息监听')
          } catch (error) {
            toast.error('停止消息监听失败')
          }
        }

        // 自动停止自动报时（如果正在运行）
        if (timeAnnouncementIsRunning) {
          try {
            await window.ipcRenderer.invoke(
              IPC_CHANNELS.tasks.autoTimeAnnouncement.stop,
            )
            setTimeAnnouncementIsRunning(false)
            toast.success('已自动停止自动报时')
          } catch (error) {
            toast.error('停止自动报时失败')
          }
        }

        setIsRunning(false)
      }
    } catch (error) {
      console.error('切换自动回复失败:', error)
    }
  }

  const platform = useCurrentLiveControl(context => context.platform)
  const isTaskForbidden = platform !== 'douyin' && platform !== 'buyin'

  return (
    <div className="container space-y-4">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <Title
              title="直播监控"
              description="对直播间消息进行回应，避免非实时、单一互动。"
            />
          </div>
          <div className="flex items-center gap-4">
            {/* 连接直播间按钮 - 只在未连接时显示，左侧小一些 */}
            <ConnectLiveButton />
            <TaskButton
              isTaskRunning={isRunning}
              onStartStop={handleAutoReplyToggle}
              forbidden={isTaskForbidden}
            />
          </div>
        </div>
      </div>

      {/* 评论和回复区域 */}
      <div className="grid grid-cols-2 gap-4">
        {/* 评论列表卡片 */}
        <CommentList highlight={highlightedCommentId} />

        {/* 回复预览卡片 */}
        <PreviewList setHighLight={setHighlightedCommentId} />
      </div>
    </div>
  )
}
