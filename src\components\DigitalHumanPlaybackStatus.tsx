import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  Play, 
  Pause, 
  SkipForward, 
  Ski<PERSON>Back, 
  Square,
  Video,
  Clock,
  List
} from 'lucide-react'
import { digitalHumanPlaybackService } from '@/services/DigitalHumanPlaybackService'
import { playlistManager } from '@/utils/PlaylistManager'

export function DigitalHumanPlaybackStatus() {
  const [playbackState, setPlaybackState] = useState<any>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // 监听播放状态变化
    const unsubscribe = playlistManager.addListener((state) => {
      setPlaybackState(digitalHumanPlaybackService.getPlaybackState())
      setIsVisible(state.items.length > 0)
    })

    // 初始状态
    setPlaybackState(digitalHumanPlaybackService.getPlaybackState())

    return unsubscribe
  }, [])

  if (!isVisible || !playbackState) {
    return null
  }

  const { items, currentItem, isPlaying, progress, preloadStatus } = playbackState

  const handlePlayPause = async () => {
    if (isPlaying) {
      await digitalHumanPlaybackService.stopPlayback()
    } else {
      // 如果有当前项目，继续播放
      if (currentItem) {
        // 这里可以添加恢复播放的逻辑
      }
    }
  }

  const handleNext = async () => {
    await digitalHumanPlaybackService.playNext()
  }

  const handlePrevious = async () => {
    await digitalHumanPlaybackService.playPrevious()
  }

  const handleStop = async () => {
    await digitalHumanPlaybackService.stopPlayback()
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Video className="h-4 w-4" />
          数字人播放状态
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 当前播放信息 */}
        {currentItem && (
          <div className="space-y-2">
            <div className="text-sm font-medium truncate">
              {currentItem.text}
            </div>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <List className="h-3 w-3" />
              {progress.current} / {progress.total}
            </div>
          </div>
        )}

        {/* 播放进度 */}
        <div className="space-y-2">
          <Progress value={progress.percentage} className="h-2" />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>播放进度</span>
            <span>{Math.round(progress.percentage)}%</span>
          </div>
        </div>

        {/* 预加载状态 */}
        {preloadStatus && preloadStatus.total > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Clock className="h-3 w-3" />
              预加载: {preloadStatus.loaded} / {preloadStatus.total}
            </div>
            <Progress 
              value={(preloadStatus.loaded / preloadStatus.total) * 100} 
              className="h-1" 
            />
          </div>
        )}

        {/* 控制按钮 */}
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrevious}
            disabled={!playbackState.hasPrevious}
          >
            <SkipBack className="h-4 w-4" />
          </Button>
          
          <Button
            variant={isPlaying ? "secondary" : "default"}
            size="sm"
            onClick={handlePlayPause}
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleNext}
            disabled={!playbackState.hasNext}
          >
            <SkipForward className="h-4 w-4" />
          </Button>
          
          <Button
            variant="destructive"
            size="sm"
            onClick={handleStop}
          >
            <Square className="h-4 w-4" />
          </Button>
        </div>

        {/* 播放列表信息 */}
        {items.length > 0 && (
          <div className="text-xs text-muted-foreground text-center">
            播放列表: {items.length} 个视频
          </div>
        )}
      </CardContent>
    </Card>
  )
}
