import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'

export type AspectRatio = '16:9' | '9:16'

interface OBSPlayerSettings {
  aspectRatio: AspectRatio
  setAspectRatio: (ratio: AspectRatio) => void
}

export const useOBSPlayerSettings = create<OBSPlayerSettings>()(
  persist(
    (set) => ({
      aspectRatio: '9:16', // 默认使用9:16比例
      setAspectRatio: (ratio: AspectRatio) => set({ aspectRatio: ratio }),
    }),
    {
      name: 'obs-player-settings',
      storage: createJSONStorage(() => localStorage),
    }
  )
)
