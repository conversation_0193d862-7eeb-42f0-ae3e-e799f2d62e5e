import { Button } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { useToast } from '@/hooks/useToast'
import { Upload, FileText, AlertCircle } from 'lucide-react'
import { useState } from 'react'

interface BatchImportDialogProps {
  onImport: (words: string[]) => void
  trigger?: React.ReactNode
  title?: string
  description?: string
}

const BatchImportDialog = ({
  onImport,
  trigger,
  title = "批量导入违禁词",
  description = "支持多种分割方式：每行一个、顿号分割、逗号分割等"
}: BatchImportDialogProps) => {
  const [open, setOpen] = useState(false)
  const [importText, setImportText] = useState('')
  const { toast } = useToast()

  const handleImport = () => {
    const text = importText.trim()
    if (!text) {
      toast.error('请输入要导入的违禁词')
      return
    }

    // 支持多种分割方式：换行、顿号、逗号、分号、空格
    const words = text
      .split(/[\n、，,;；\s]+/)  // 使用正则表达式分割多种分隔符
      .map(word => word.trim())
      .filter(word => word.length > 0)
      .filter((word, index, array) => array.indexOf(word) === index) // 去重

    if (words.length === 0) {
      toast.error('没有有效的违禁词可导入')
      return
    }

    onImport(words)
    setImportText('')
    setOpen(false)
    toast.success(`成功导入 ${words.length} 个违禁词`)
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('text/') && !file.name.endsWith('.txt')) {
      toast.error('请选择文本文件')
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setImportText(content)
    }
    reader.onerror = () => {
      toast.error('文件读取失败')
    }
    reader.readAsText(file, 'UTF-8')
  }

  const previewWords = importText
    .split(/[\n、，,;；\s]+/)
    .map(word => word.trim())
    .filter(word => word.length > 0)
    .filter((word, index, array) => array.indexOf(word) === index)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            批量导入
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <p className="text-sm text-muted-foreground">{description}</p>
        </DialogHeader>

        <div className="flex-1 space-y-4 overflow-hidden">
          {/* 文件上传 */}
          <div className="space-y-2">
            <Label htmlFor="file-upload" className="text-sm font-medium">
              从文件导入
            </Label>
            <div className="flex items-center gap-2">
              <input
                id="file-upload"
                type="file"
                accept=".txt,text/*"
                onChange={handleFileUpload}
                className="hidden"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => document.getElementById('file-upload')?.click()}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                选择文件
              </Button>
              <span className="text-xs text-muted-foreground">
                支持 .txt 文本文件
              </span>
            </div>
          </div>

          {/* 手动输入 */}
          <div className="space-y-2 flex-1 flex flex-col">
            <Label htmlFor="import-text" className="text-sm font-medium">
              手动输入
            </Label>
            <Textarea
              id="import-text"
              value={importText}
              onChange={(e) => setImportText(e.target.value)}
              placeholder="请输入违禁词，支持多种分割方式：&#10;• 每行一个违禁词&#10;• 顿号分割：词1、词2、词3&#10;• 逗号分割：词1，词2，词3&#10;• 空格分割：词1 词2 词3"
              className="flex-1 min-h-[120px] resize-none"
            />
            <div className="text-xs text-muted-foreground">
              支持的分割符：换行、顿号（、）、逗号（，）、分号（；）、空格
            </div>
          </div>

          {/* 预览 */}
          {previewWords.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                预览 ({previewWords.length} 个词)
              </Label>
              <div className="border rounded-md p-3 max-h-[120px] overflow-y-auto bg-muted/50">
                <div className="flex flex-wrap gap-1">
                  {previewWords.slice(0, 50).map((word, index) => (
                    <span
                      key={index}
                      className="inline-block px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 text-xs rounded"
                    >
                      {word}
                    </span>
                  ))}
                  {previewWords.length > 50 && (
                    <span className="inline-block px-2 py-1 bg-muted text-muted-foreground text-xs rounded">
                      ...还有 {previewWords.length - 50} 个
                    </span>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => setOpen(false)}>
            取消
          </Button>
          <Button
            onClick={handleImport}
            disabled={previewWords.length === 0}
            className="flex items-center gap-2"
          >
            <Upload className="h-4 w-4" />
            导入 {previewWords.length > 0 && `(${previewWords.length})`}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default BatchImportDialog
