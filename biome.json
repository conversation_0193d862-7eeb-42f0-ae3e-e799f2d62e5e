{"$schema": "https://biomejs.dev/schemas/2.0.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/node_modules", "!**/dist", "!**/dist-electron", "!**/release", "!**/build", "!**/.vscode"]}, "formatter": {"enabled": true, "indentStyle": "space"}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "all", "enabled": true, "jsxQuoteStyle": "double", "semicolons": "asNeeded", "arrowParentheses": "asNeeded"}}}