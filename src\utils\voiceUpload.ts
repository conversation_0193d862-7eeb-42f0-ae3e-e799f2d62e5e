// 默认的 ComfyUI 服务器地址
const DEFAULT_COMFYUI_SERVER = "http://127.0.0.1:8188";

// 获取 ComfyUI 服务器地址
export function getComfyUIServer(): string {
  try {
    // 从localStorage中获取用户配置的服务器地址
    const voiceSettings = localStorage.getItem('voice-settings-storage');
    if (voiceSettings) {
      const parsed = JSON.parse(voiceSettings);
      return parsed.state?.comfyUIServer || DEFAULT_COMFYUI_SERVER;
    }
  } catch (error) {
    console.warn('获取ComfyUI服务器地址失败，使用默认地址:', error);
  }
  return DEFAULT_COMFYUI_SERVER;
}

// 音色文件上传响应接口
export interface VoiceUploadResponse {
  name: string;
  subfolder: string;
  type: string;
}

// 音色文件信息接口
export interface VoiceFileInfo {
  name: string;
  displayName: string;
  uploadTime: number;
}

// 音频格式转换结果接口
export interface AudioConversionResult {
  convertedFile: File;
  originalFormat: string;
  convertedFormat: string;
}

/**
 * 检查文件是否为支持的音频格式
 * @param file 要检查的文件
 * @returns 是否为支持的音频格式
 */
export function isSupportedAudioFormat(file: File): boolean {
  const supportedTypes = [
    'audio/wav', 'audio/wave', 'audio/x-wav',
    'audio/mp3', 'audio/mpeg',
    'audio/flac',
    'audio/ogg', 'audio/ogg; codecs=vorbis',
    'audio/aac',
    'audio/m4a',
    'audio/webm'
  ];

  const supportedExtensions = ['.wav', '.mp3', '.flac', '.ogg', '.aac', '.m4a', '.webm'];
  const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

  return supportedTypes.includes(file.type) || supportedExtensions.includes(fileExtension);
}

/**
 * 获取文件的音频格式
 * @param file 音频文件
 * @returns 音频格式字符串
 */
export function getAudioFormat(file: File): string {
  const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  switch (extension) {
    case '.wav': return 'WAV';
    case '.mp3': return 'MP3';
    case '.flac': return 'FLAC';
    case '.ogg': return 'OGG';
    case '.aac': return 'AAC';
    case '.m4a': return 'M4A';
    case '.webm': return 'WebM';
    default: return 'Unknown';
  }
}

/**
 * 将音频文件转换为WAV格式
 * @param file 要转换的音频文件
 * @returns Promise<AudioConversionResult> 转换结果
 */
export async function convertAudioToWav(file: File): Promise<AudioConversionResult> {
  const originalFormat = getAudioFormat(file);

  // 如果已经是WAV格式，直接返回
  if (originalFormat === 'WAV') {
    return {
      convertedFile: file,
      originalFormat,
      convertedFormat: 'WAV'
    };
  }

  return new Promise((resolve, reject) => {
    try {
      // 创建音频上下文
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const fileReader = new FileReader();

      fileReader.onload = async (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;

          // 解码音频数据
          const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

          // 转换为WAV格式
          const wavArrayBuffer = audioBufferToWav(audioBuffer);

          // 创建新的文件名（替换扩展名为.wav）
          const originalName = file.name.substring(0, file.name.lastIndexOf('.'));
          const newFileName = `${originalName}.wav`;

          // 创建新的File对象
          const convertedFile = new File([wavArrayBuffer], newFileName, {
            type: 'audio/wav'
          });

          resolve({
            convertedFile,
            originalFormat,
            convertedFormat: 'WAV'
          });
        } catch (error) {
          console.error('音频解码失败:', error);
          reject(new Error(`音频格式转换失败: ${error instanceof Error ? error.message : '未知错误'}`));
        }
      };

      fileReader.onerror = () => {
        reject(new Error('文件读取失败'));
      };

      // 读取文件
      fileReader.readAsArrayBuffer(file);
    } catch (error) {
      console.error('音频转换初始化失败:', error);
      reject(new Error(`音频转换失败: ${error instanceof Error ? error.message : '未知错误'}`));
    }
  });
}

/**
 * 将AudioBuffer转换为WAV格式的ArrayBuffer
 * @param buffer AudioBuffer对象
 * @returns ArrayBuffer WAV格式的音频数据
 */
function audioBufferToWav(buffer: AudioBuffer): ArrayBuffer {
  const length = buffer.length;
  const numberOfChannels = buffer.numberOfChannels;
  const sampleRate = buffer.sampleRate;
  const bytesPerSample = 2; // 16-bit
  const blockAlign = numberOfChannels * bytesPerSample;
  const byteRate = sampleRate * blockAlign;
  const dataSize = length * blockAlign;
  const bufferSize = 44 + dataSize;

  const arrayBuffer = new ArrayBuffer(bufferSize);
  const view = new DataView(arrayBuffer);

  // WAV文件头
  const writeString = (offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };

  // RIFF chunk descriptor
  writeString(0, 'RIFF');
  view.setUint32(4, bufferSize - 8, true);
  writeString(8, 'WAVE');

  // FMT sub-chunk
  writeString(12, 'fmt ');
  view.setUint32(16, 16, true); // Sub-chunk size
  view.setUint16(20, 1, true); // Audio format (PCM)
  view.setUint16(22, numberOfChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, byteRate, true);
  view.setUint16(32, blockAlign, true);
  view.setUint16(34, 16, true); // Bits per sample

  // Data sub-chunk
  writeString(36, 'data');
  view.setUint32(40, dataSize, true);

  // 写入音频数据
  let offset = 44;
  for (let i = 0; i < length; i++) {
    for (let channel = 0; channel < numberOfChannels; channel++) {
      const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
      const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      view.setInt16(offset, intSample, true);
      offset += 2;
    }
  }

  return arrayBuffer;
}

/**
 * 上传音色文件到 ComfyUI 服务器（支持自动格式转换）
 * @param file 要上传的音色文件
 * @param autoConvert 是否自动转换为WAV格式，默认为true
 * @returns {Promise<VoiceUploadResponse & { converted?: boolean, originalFormat?: string }>} 上传响应
 */
export async function uploadVoiceFile(
  file: File,
  autoConvert: boolean = true
): Promise<VoiceUploadResponse & { converted?: boolean, originalFormat?: string }> {
  if (!file) {
    throw new Error('请选择要上传的音色文件');
  }

  // 检查是否为支持的音频格式
  if (!isSupportedAudioFormat(file)) {
    throw new Error('不支持的音频格式。支持的格式：WAV, MP3, FLAC, OGG, AAC, M4A, WebM');
  }

  // 检查文件大小 (限制为 50MB)
  const maxSize = 50 * 1024 * 1024; // 50MB
  if (file.size > maxSize) {
    throw new Error('音色文件大小不能超过 50MB');
  }

  let fileToUpload = file;
  let converted = false;
  const originalFormat = getAudioFormat(file);

  // 如果不是WAV格式且启用自动转换，则进行格式转换
  if (autoConvert && originalFormat !== 'WAV') {
    try {
      console.log(`正在将 ${originalFormat} 格式转换为 WAV...`);
      const conversionResult = await convertAudioToWav(file);
      fileToUpload = conversionResult.convertedFile;
      converted = true;
      console.log(`格式转换完成: ${originalFormat} -> WAV`);
    } catch (error) {
      console.error('音频格式转换失败:', error);
      throw new Error(`音频格式转换失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  } else if (!autoConvert && originalFormat !== 'WAV') {
    throw new Error('只支持 WAV 格式的音色文件，请启用自动转换或手动转换为WAV格式');
  }

  try {
    // 获取服务器地址
    const serverUrl = getComfyUIServer();

    // 创建 FormData
    const formData = new FormData();
    formData.append('image', fileToUpload, fileToUpload.name);

    console.log('正在上传音色文件到 ComfyUI...', fileToUpload.name);
    if (converted) {
      console.log(`已转换格式: ${originalFormat} -> WAV`);
    }

    const response = await fetch(`${serverUrl}/api/upload/image`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`上传失败: ${response.status} ${response.statusText}`);
    }

    const result: VoiceUploadResponse = await response.json();
    console.log('音色文件上传成功:', result);

    // 返回包含转换信息的结果
    return {
      ...result,
      converted,
      originalFormat: converted ? originalFormat : undefined
    };
  } catch (error) {
    console.error('音色文件上传失败:', error);
    throw error;
  }
}

/**
 * 获取已上传的音色文件列表
 * @returns {Promise<string[]>} 音色文件名列表
 */
export async function getVoiceFileList(): Promise<string[]> {
  try {
    // 获取服务器地址
    const serverUrl = getComfyUIServer();

    // 尝试从 ComfyUI 服务器获取音频文件列表
    console.log('正在从 ComfyUI 服务器获取音色文件列表...');

    // 尝试多个可能的API端点来获取文件列表
    const possibleEndpoints = [
      // 尝试使用GET方法获取input目录文件列表
      `${serverUrl}/api/view?filename=&type=input&subfolder=`,
      // 尝试其他可能的端点
      `${serverUrl}/api/files/input`,
      `${serverUrl}/files/input`,
    ]

    for (const endpoint of possibleEndpoints) {
      try {
        console.log(`尝试获取音色文件列表: ${endpoint}`)

        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        })

        if (response.ok) {
          const data = await response.json()
          console.log('从服务器获取到的文件列表:', data)

          // 处理不同格式的响应
          let files: string[] = []
          if (Array.isArray(data)) {
            files = data
          } else if (data.files && Array.isArray(data.files)) {
            files = data.files
          } else if (data.data && Array.isArray(data.data)) {
            files = data.data
          } else {
            console.warn('未知的响应格式:', data)
            continue
          }

          // 过滤出音频文件
          const audioFiles = files.filter((file: string) =>
            file.toLowerCase().endsWith('.wav') ||
            file.toLowerCase().endsWith('.mp3') ||
            file.toLowerCase().endsWith('.flac')
          );

          // 确保默认音色在列表中
          const defaultVoice = '默认音色.wav';
          if (!audioFiles.includes(defaultVoice)) {
            audioFiles.unshift(defaultVoice);
          }

          if (audioFiles.length > 0) {
            console.log('过滤后的音色文件列表:', audioFiles);
            return audioFiles;
          }
        } else {
          console.warn(`端点 ${endpoint} 返回错误: ${response.status} ${response.statusText}`)
        }
      } catch (error) {
        console.warn(`端点 ${endpoint} 请求失败:`, error)
        continue
      }
    }

    // 如果所有端点都失败，返回默认音色
    console.warn('所有API端点都无法获取音色文件列表，使用默认音色');
    return ['默认音色.wav'];
  } catch (error) {
    console.error('获取音色文件列表失败:', error);
    console.log('使用默认音色列表');
    return ['默认音色.wav']; // 返回默认音色
  }
}

/**
 * 删除音色文件
 * @param fileName 要删除的文件名
 * @returns {Promise<boolean>} 删除是否成功
 */
export async function deleteVoiceFile(fileName: string): Promise<boolean> {
  try {
    // TODO: 实现删除音色文件的功能
    // 目前 ComfyUI 可能没有直接的删除 API，需要根据实际情况实现
    console.log('删除音色文件:', fileName);
    
    // 暂时返回 true，表示删除成功
    return true;
  } catch (error) {
    console.error('删除音色文件失败:', error);
    return false;
  }
}

/**
 * 验证音色文件是否存在
 * @param fileName 文件名
 * @returns {Promise<boolean>} 文件是否存在
 */
export async function validateVoiceFile(fileName: string): Promise<boolean> {
  try {
    // TODO: 实现验证音色文件是否存在的功能
    console.log('验证音色文件:', fileName);
    
    // 暂时返回 true，表示文件存在
    return true;
  } catch (error) {
    console.error('验证音色文件失败:', error);
    return false;
  }
}
