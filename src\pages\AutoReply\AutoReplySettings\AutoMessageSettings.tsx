import { TaskButton } from '@/components/common/TaskButton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  useAutoMessageActions,
  useCurrentAutoMessage,
} from '@/hooks/useAutoMessage'
import { useToast } from '@/hooks/useToast'
import MessageListCard from '@/pages/AutoMessage/components/MessageListCard'
import MessageSettingsCard from '@/pages/AutoMessage/components/MessageSettingsCard'
import { MessageOneKey } from '@/pages/AutoMessage/components/MessagesOneKey'
import { useMemoizedFn } from 'ahooks'
import { useCallback, useState } from 'react'
import { IPC_CHANNELS } from 'shared/ipcChannels'

interface TaskControl {
  isRunning: boolean
  onStartTask: () => void
  onStopTask: () => void
}

// 实现useTaskControl hook的功能
function useTaskControl(): TaskControl {
  const isRunning = useCurrentAutoMessage(context => context.isRunning)
  const config = useCurrentAutoMessage(context => context.config)
  const { setIsRunning } = useAutoMessageActions()
  const { toast } = useToast()

  const onStartTask = useCallback(async () => {
    const result = await window.ipcRenderer.invoke(
      IPC_CHANNELS.tasks.autoMessage.start,
      config,
    )
    if (result) {
      setIsRunning(true)
      toast.success('自动消息任务已启动')
    } else {
      setIsRunning(false)
      toast.error('自动消息任务启动失败')
    }
  }, [config, setIsRunning, toast])

  const onStopTask = useCallback(async () => {
    await window.ipcRenderer.invoke(IPC_CHANNELS.tasks.autoMessage.stop)
    setIsRunning(false)
  }, [setIsRunning])

  return {
    isRunning,
    onStartTask,
    onStopTask,
  }
}

export default function AutoMessageSettings() {
  const { isRunning, onStartTask, onStopTask } = useTaskControl()
  const [validationError] = useState<string | null>(null)

  const handleTaskButtonClick = useMemoizedFn(() => {
    if (!isRunning) onStartTask()
    else onStopTask()
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h3 className="text-sm font-medium">在直播间评论区主动发评论</h3>
          <p className="text-sm text-muted-foreground">
            配置自动发送消息的规则
          </p>
        </div>
        <TaskButton
          isTaskRunning={isRunning}
          onStartStop={handleTaskButtonClick}
        />
      </div>

      {validationError && (
        <Alert variant="destructive">
          <AlertDescription>{validationError}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-6">
        <MessageListCard />
        <MessageSettingsCard />
        <MessageOneKey />
      </div>
    </div>
  )
}
