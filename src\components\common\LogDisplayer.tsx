import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { useIpcListener } from '@/hooks/useIpc'
import { cn } from '@/lib/utils'
import { useMemoizedFn } from 'ahooks'
import type { LogMessage } from 'electron-log'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { ChevronUpIcon, ChevronDownIcon } from 'lucide-react'
import AudioSettingsQuick from '@/pages/AutoVoice/components/AudioSettingsQuick'
import AudioServiceDialog from './AudioServiceDialog'
import DigitalHumanServiceDialog from './DigitalHumanServiceDialog'
import StreamServerDialog from './StreamServerDialog'
import { useVirtualizer } from '@tanstack/react-virtual'

interface ParsedLog {
  id: string
  timestamp: string
  module: string
  level: string
  message: string
}

// interface LogMessage {
//   logId: string
//   date: Date
//   scope?: string
//   level: string
//   data: string[]
// }

const MAX_LOG_MESSAGES = 200 // 仅展示最近的 200 条日志

interface LogDisplayerProps {
  isExpanded: boolean
  onToggleExpand: () => void
}

export default function LogDisplayer({
  isExpanded,
  onToggleExpand,
}: LogDisplayerProps) {
  const [logMessages, setLogMessages] = useState<ParsedLog[]>([])
  const [autoScroll, setAutoScroll] = useState(true)
  const parentRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = useCallback(() => {
    if (parentRef.current && autoScroll) {
      const scrollContainer = parentRef.current
      requestAnimationFrame(() => {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
      })
    }
  }, [autoScroll])

  const parseLogMessage = (log: LogMessage): ParsedLog | null => {
    if (!log.data || log.data.length === 0) {
      return null
    }
    return {
      id: crypto.randomUUID(),
      timestamp: log.date.toLocaleString(),
      module: log.scope ?? 'App',
      level: typeof log.level === 'string' ? log.level.toUpperCase() : 'INFO',
      message: log.data.map(String).join(' '),
    }
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: parseLogMessage 不影响
  const handleLogMessage = useCallback(
    (message: LogMessage) => {
      const parsed = parseLogMessage(message)
      if (parsed) {
        setLogMessages(prev => [...prev.slice(-MAX_LOG_MESSAGES + 1), parsed])
        if (autoScroll) {
          scrollToBottom()
        }
      }
    },
    [autoScroll, scrollToBottom],
  )

  useIpcListener(IPC_CHANNELS.log, handleLogMessage)

  // 创建虚拟列表实例
  const virtualizer = useVirtualizer({
    count: logMessages.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 32, // 估算每个日志项的高度
    overscan: 5, // 预渲染额外的项目数量
    // 启用动态高度测量
    measureElement: (element) => {
      return element?.getBoundingClientRect().height ?? 32
    },
  })

  return (
    <div className="h-full flex flex-col bg-background">
      {/* 日志头部 */}
      <div className="flex items-center justify-between px-4 py-2 border-b">
        <div className="flex items-center gap-2">
          <h3 className="font-medium">运行日志</h3>
          <span className="text-xs text-muted-foreground">
            {logMessages.length} 条记录
          </span>
        </div>
        <div className="flex items-center gap-4">
          {/* 音频控制按钮 */}
          <div className="flex items-center gap-2">
            <AudioSettingsQuick />
            <AudioServiceDialog />
            <DigitalHumanServiceDialog />
            <StreamServerDialog />
          </div>

          {/* 自动滚动开关 */}
          <div className="flex items-center gap-2">
            <Switch
              id="auto-scroll"
              checked={autoScroll}
              onCheckedChange={setAutoScroll}
            />
            <label
              htmlFor="auto-scroll"
              className="text-xs text-muted-foreground cursor-pointer select-none"
            >
              自动滚动
            </label>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setLogMessages([])
              scrollToBottom()
            }}
            className="text-xs h-7 px-2 text-muted-foreground hover:text-destructive"
          >
            清空
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleExpand}
            className="h-7 w-7 p-0 text-muted-foreground"
            title={isExpanded ? "收起日志" : "展开日志"}
          >
            {isExpanded ? (
              <ChevronDownIcon className="h-4 w-4" />
            ) : (
              <ChevronUpIcon className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* 日志内容 - 根据isExpanded状态决定是否渲染 */}
      {isExpanded && (
        logMessages.length === 0 ? (
          <div className="flex-1 flex items-center justify-center text-muted-foreground">
            暂无日志数据
          </div>
        ) : (
          <div
            ref={parentRef}
            className="flex-1 overflow-auto font-mono text-sm"
          >
            <div
              style={{
                height: `${virtualizer.getTotalSize()}px`,
                width: '100%',
                position: 'relative',
              }}
            >
              {virtualizer.getVirtualItems().map((virtualItem) => (
                <div
                  key={virtualItem.key}
                  data-index={virtualItem.index}
                  ref={virtualizer.measureElement}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    transform: `translateY(${virtualItem.start}px)`,
                  }}
                  className="px-4" // 添加水平间距
                >
                  <LogItem
                    log={logMessages[virtualItem.index]}
                    index={virtualItem.index}
                  />
                </div>
              ))}
            </div>
          </div>
        )
      )}
    </div>
  )
}

function LogItem({ log, index }: { log: ParsedLog; index: number }) {
  const getLevelColor = useMemoizedFn((level: string): string => {
    switch (level.toUpperCase()) {
      case 'ERROR':
        return 'text-destructive font-medium'
      case 'FATAL':
        return 'text-destructive font-bold'
      case 'WARN':
        return 'text-orange-600 dark:text-orange-400 font-medium'
      case 'DEBUG':
        return 'text-blue-600 dark:text-blue-400'
      case 'INFO':
        return 'text-muted-foreground'
      case 'SUCCESS':
        return 'text-emerald-600 dark:text-emerald-400'
      case 'NOTE':
        return 'text-purple-600 dark:text-purple-400'
      default:
        return 'text-muted-foreground'
    }
  })
  return (
    <div
      key={log.id}
      className={cn(
        'flex gap-2 items-start py-1 whitespace-nowrap',
        index % 2 === 0 ? 'bg-muted/40' : 'bg-background',
      )}
    >
      <span className="text-muted-foreground shrink-0">[{log.timestamp}]</span>
      <span className="text-foreground/70 shrink-0">[{log.module}]</span>
      <span className={cn('shrink-0', getLevelColor(log.level))}>
        {log.level}
      </span>
      <span className="text-foreground truncate">{log.message}</span>
    </div>
  )
}
