import { generateDigitalHumanVideo, VideoGenerationResult } from '@/utils/textToVideo'
import { getAvatarVideoManager, AvatarVideoSegment } from '@/utils/avatarVideoManager'

/**
 * 使用视频片段信息生成数字人视频
 * @param text 要转换的文本
 * @param audioPath 音频文件路径
 * @param avatarSegment 形象视频片段信息
 * @param speaker 说话者类型
 * @returns 视频生成结果
 */
async function generateDigitalHumanVideoWithSegment(
  text: string,
  audioPath: string,
  avatarSegment: AvatarVideoSegment,
  speaker?: 'host' | 'assistant'
): Promise<VideoGenerationResult> {
  console.log('使用连续播放模式生成数字人视频')
  console.log('视频片段信息:', {
    startTime: avatarSegment.startTime,
    endTime: avatarSegment.endTime,
    duration: avatarSegment.duration,
    url: avatarSegment.url
  })

  try {
    // 从URL中提取切割信息
    const url = new URL(avatarSegment.url)
    const segmentStart = parseFloat(url.searchParams.get('segment_start') || '0')
    const segmentEnd = parseFloat(url.searchParams.get('segment_end') || '0')
    const segmentDuration = parseFloat(url.searchParams.get('segment_duration') || '0')

    // 从URL中正确提取文件名
    let filename = 'default.mp4'
    try {
      const urlObj = new URL(avatarSegment.url)

      // 优先使用 original_filename 参数
      filename = urlObj.searchParams.get('original_filename') ||
                urlObj.searchParams.get('filename') ||
                'default.mp4'

      // 如果filename仍然不正确，使用随机选择的形象
      if (filename === 'view' || !filename.includes('.')) {
        const { getRandomSelectedDigitalHuman } = await import('@/hooks/useDigitalHumanSettings')
        filename = getRandomSelectedDigitalHuman(speaker)
      }
    } catch (error) {
      console.error('解析URL失败:', error)
      // 使用随机选择的形象作为回退
      const { getRandomSelectedDigitalHuman } = await import('@/hooks/useDigitalHumanSettings')
      filename = getRandomSelectedDigitalHuman(speaker)
    }

    console.log('切割信息:', {
      filename,
      segmentStart,
      segmentEnd,
      segmentDuration
    })

    // 重新启用切割功能，使用改进的方法
    console.log('使用改进的切割功能')

    // 创建切割后的视频文件
    const segmentFilename = await createSegmentVideoFile(filename, segmentStart, segmentEnd)

    if (segmentFilename) {
      console.log(`使用切割后的视频文件: ${segmentFilename}`)

      // 验证切割后的文件是否可以被ComfyUI访问
      const isFileValid = await validateSegmentFile(segmentFilename)
      if (!isFileValid) {
        console.warn('切割后的文件验证失败，回退到原始文件')
        return await generateDigitalHumanVideo(
          text,
          audioPath,
          filename,
          speaker
        )
      }

      // 使用切割后的视频文件生成数字人视频
      const result = await generateDigitalHumanVideo(
        text,
        audioPath,
        segmentFilename,
        speaker
      )

      return {
        ...result,
        segmentInfo: {
          startTime: segmentStart,
          endTime: segmentEnd,
          duration: segmentDuration,
          originalFilename: filename,
          segmentFilename
        }
      } as VideoGenerationResult & { segmentInfo: any }
    } else {
      // 如果切割失败，使用原始文件
      console.log('视频切割失败，使用原始文件')
      return await generateDigitalHumanVideo(
        text,
        audioPath,
        filename,
        speaker
      )
    }

  } catch (error) {
    console.error('连续播放模式生成视频失败:', error)

    // 回退到原有的生成方式
    console.log('回退到传统生成模式')
    let filename = 'default.mp4'
    try {
      const urlObj = new URL(avatarSegment.url)

      // 优先使用 original_filename 参数
      filename = urlObj.searchParams.get('original_filename') ||
                urlObj.searchParams.get('filename') ||
                'default.mp4'

      if (filename === 'view' || !filename.includes('.')) {
        const { getRandomSelectedDigitalHuman } = await import('@/hooks/useDigitalHumanSettings')
        filename = getRandomSelectedDigitalHuman(speaker)
      }
    } catch (error) {
      console.error('解析URL失败:', error)
      const { getRandomSelectedDigitalHuman } = await import('@/hooks/useDigitalHumanSettings')
      filename = getRandomSelectedDigitalHuman(speaker)
    }

    return await generateDigitalHumanVideo(
      text,
      audioPath,
      filename,
      speaker
    )
  }
}

/**
 * 验证切割后的视频文件是否有效
 * @param filename 文件名
 * @returns 是否有效
 */
async function validateSegmentFile(filename: string): Promise<boolean> {
  try {
    const result = await (window as any).ipcRenderer?.invoke('video:getInfo', filename)

    if (result && !result.error && result.duration > 0) {
      console.log(`切割文件验证成功: ${filename}, 时长: ${result.duration}秒`)
      return true
    } else {
      console.warn(`切割文件验证失败: ${filename}`, result?.error)
      return false
    }
  } catch (error) {
    console.error('验证切割文件失败:', error)
    return false
  }
}

/**
 * 创建切割后的视频文件
 * @param originalFilename 原始视频文件名
 * @param startTime 开始时间（秒）
 * @param endTime 结束时间（秒）
 * @returns 切割后的视频文件名，如果失败返回null
 */
async function createSegmentVideoFile(
  originalFilename: string,
  startTime: number,
  endTime: number
): Promise<string | null> {
  try {
    console.log(`请求切割视频: ${originalFilename} (${startTime}s - ${endTime}s)`)

    // 生成切割后的文件名
    const timestamp = Date.now()
    const segmentFilename = `segment_${timestamp}_${startTime}_${endTime}_${originalFilename}`

    // 调用后端服务进行视频切割
    const result = await (window as any).ipcRenderer?.invoke('video:cutSegment', {
      originalFilename,
      startTime,
      endTime,
      outputFilename: segmentFilename
    })

    if (result && result.success) {
      console.log(`视频切割成功: ${segmentFilename}`)

      // 额外验证：等待一小段时间确保文件写入完成
      await new Promise(resolve => setTimeout(resolve, 500))

      return segmentFilename
    } else {
      console.warn('视频切割失败:', result?.error || '未知错误')
      return null
    }

  } catch (error) {
    console.error('调用视频切割服务失败:', error)
    return null
  }
}

/**
 * 连续数字人视频生成结果
 */
export interface ContinuousVideoResult extends VideoGenerationResult {
  avatarSegment?: AvatarVideoSegment
  isContinuous: boolean
  segmentInfo?: {
    startTime: number
    endTime: number
    totalDuration: number
    remainingDuration: number
  }
}

/**
 * 连续数字人视频生成选项
 */
export interface ContinuousVideoOptions {
  enableContinuous?: boolean
  speaker?: 'host' | 'assistant'
  audioDuration?: number
  digitalHumanFile?: string
}

/**
 * 生成连续的数字人视频
 * 根据音频时长智能切割形象视频，确保画面连续
 */
export async function generateContinuousDigitalHumanVideo(
  text: string,
  audioPath: string,
  options: ContinuousVideoOptions = {}
): Promise<ContinuousVideoResult> {
  const {
    enableContinuous = true,
    speaker = 'host',
    audioDuration,
    digitalHumanFile
  } = options

  console.log('🎬 开始生成连续数字人视频...')
  console.log('🎬 选项:', { enableContinuous, speaker, audioDuration, digitalHumanFile })
  console.log('🎬 说话者类型:', speaker, speaker === 'assistant' ? '(助理)' : '(主播)')

  try {
    // 获取形象视频管理器
    const avatarManager = getAvatarVideoManager()
    
    let avatarSegment: AvatarVideoSegment | undefined
    let segmentInfo: any = undefined

    // 如果启用连续播放且有音频时长信息
    if (enableContinuous && audioDuration && audioDuration > 0) {
      console.log(`为 ${audioDuration}秒 音频生成连续形象视频片段`)
      
      // 获取匹配音频时长的形象视频片段
      avatarSegment = await avatarManager.getAvatarSegmentForAudio(audioDuration, speaker)
      
      // 获取管理器状态信息
      const status = avatarManager.getStatus(speaker)
      segmentInfo = {
        startTime: avatarSegment.startTime,
        endTime: avatarSegment.endTime,
        totalDuration: status.concatenatedDuration,
        remainingDuration: status.remainingDuration
      }

      console.log('形象视频片段信息:', segmentInfo)
    }

    // 生成数字人视频（使用增强的生成逻辑）
    let result: VideoGenerationResult

    if (avatarSegment && avatarSegment.url.includes('continuous_mode=true')) {
      // 使用连续播放模式生成视频
      result = await generateDigitalHumanVideoWithSegment(
        text,
        audioPath,
        avatarSegment,
        speaker
      )
    } else {
      // 使用原有的生成逻辑
      result = await generateDigitalHumanVideo(
        text,
        audioPath,
        digitalHumanFile || avatarSegment?.url.split('/').pop(),
        speaker
      )
    }

    // 返回增强的结果
    const continuousResult: ContinuousVideoResult = {
      ...result,
      avatarSegment,
      isContinuous: enableContinuous && !!avatarSegment,
      segmentInfo
    }

    console.log('连续数字人视频生成完成:', {
      videoUrl: result.videoUrl,
      isContinuous: continuousResult.isContinuous,
      segmentInfo: continuousResult.segmentInfo
    })

    return continuousResult

  } catch (error) {
    console.error('生成连续数字人视频失败:', error)
    
    // 如果连续播放失败，回退到原有的生成方式
    console.log('回退到原有的数字人视频生成方式')
    const fallbackResult = await generateDigitalHumanVideo(text, audioPath, digitalHumanFile, speaker)
    
    return {
      ...fallbackResult,
      isContinuous: false
    }
  }
}

/**
 * 批量生成连续数字人视频
 * 确保整个批次的视频画面连续
 */
export async function generateBatchContinuousVideos(
  requests: Array<{
    text: string
    audioPath: string
    audioDuration?: number
    speaker?: 'host' | 'assistant'
  }>
): Promise<ContinuousVideoResult[]> {
  console.log(`开始批量生成 ${requests.length} 个连续数字人视频`)
  
  const results: ContinuousVideoResult[] = []
  const avatarManager = getAvatarVideoManager()
  
  try {
    // 预处理形象视频（如果还没有处理）
    if (requests.length > 0) {
      const firstSpeaker = requests[0].speaker || 'host'
      await avatarManager.preprocessAvatarVideos(firstSpeaker)
    }

    // 逐个生成视频
    for (let i = 0; i < requests.length; i++) {
      const request = requests[i]
      console.log(`生成第 ${i + 1}/${requests.length} 个视频: ${request.text}`)
      
      const result = await generateContinuousDigitalHumanVideo(
        request.text,
        request.audioPath,
        {
          enableContinuous: true,
          speaker: request.speaker,
          audioDuration: request.audioDuration
        }
      )
      
      results.push(result)
      
      // 添加短暂延迟避免服务器压力
      if (i < requests.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    console.log(`批量生成完成，共 ${results.length} 个视频`)
    return results

  } catch (error) {
    console.error('批量生成连续数字人视频失败:', error)
    throw error
  }
}

/**
 * 重置连续播放状态
 * 在开始新的直播会话时调用
 */
export function resetContinuousPlayback(): void {
  console.log('重置连续播放状态')
  const avatarManager = getAvatarVideoManager()
  avatarManager.resetOffset()
}

/**
 * 获取连续播放状态信息
 */
export function getContinuousPlaybackStatus() {
  const avatarManager = getAvatarVideoManager()
  return avatarManager.getStatus()
}

/**
 * 更新连续播放配置
 */
export function updateContinuousPlaybackConfig(config: any) {
  const avatarManager = getAvatarVideoManager()
  avatarManager.updateConfig(config)
}

/**
 * 从localStorage加载连续播放配置
 */
export function loadContinuousPlaybackConfig() {
  try {
    const saved = localStorage.getItem('continuous-playback-config')
    if (saved) {
      const config = JSON.parse(saved)
      updateContinuousPlaybackConfig(config)
      return config
    }
  } catch (error) {
    console.error('加载连续播放配置失败:', error)
  }
  return null
}

// 在模块加载时自动加载配置
loadContinuousPlaybackConfig()
