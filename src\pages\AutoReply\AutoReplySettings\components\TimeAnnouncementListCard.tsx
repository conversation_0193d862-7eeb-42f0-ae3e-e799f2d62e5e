import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import {
  useAutoTimeAnnouncementActions,
  useCurrentAutoTimeAnnouncement,
  type TimeAnnouncementMessage,
} from '@/hooks/useAutoTimeAnnouncement'
import { useToast } from '@/hooks/useToast'
import { useAIChatStore } from '@/hooks/useAIChat'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { PlusIcon, RotateCcwIcon, DownloadIcon, Trash2Icon, Sparkles, Loader2, Settings } from 'lucide-react'
import { useMemoizedFn } from 'ahooks'
import React, { useState } from 'react'
import TimeAnnouncementMessageComp from './TimeAnnouncementMessageComp'

// 默认AI泛化提示词
const DEFAULT_GENERALIZATION_PROMPT = `你是一个专业的直播报时消息泛化助手。你的任务是将给定的报时消息进行多样化改写，保持原意的同时增加表达的丰富性。

请遵循以下原则：
1. 保持原报时消息的核心意思和目的不变
2. 使用不同的表达方式、语气和词汇
3. 适合直播场景，语言要亲切自然
4. 每个变体都应该是完整的、可直接使用的报时消息
5. 保持{时间}变量不变
6. 生成5-8个不同的变体
7. 消息内容要简短有力，适合定时播放

请对以下报时消息进行泛化改写：
{original_content}

请直接返回变体列表，每行一个变体，不需要编号或其他格式。`

const TimeAnnouncementListCard = React.memo(() => {
  const messages = useCurrentAutoTimeAnnouncement(context => context.config.messages)
  const { setMessages, resetToDefaultMessages } = useAutoTimeAnnouncementActions()
  const { toast } = useToast()
  const aiStore = useAIChatStore()
  const [batchImportOpen, setBatchImportOpen] = useState(false)
  const [importText, setImportText] = useState('')
  const [isGeneralizing, setIsGeneralizing] = useState(false)
  const [showPromptDialog, setShowPromptDialog] = useState(false)
  const [generalizationPrompt, setGeneralizationPrompt] = useState(() => {
    return localStorage.getItem('time-announcement-generalization-prompt') || DEFAULT_GENERALIZATION_PROMPT
  })
  const [tempPrompt, setTempPrompt] = useState(generalizationPrompt)

  const handleMessageChange = useMemoizedFn((message: TimeAnnouncementMessage) => {
    setMessages(messages.map(m => (m.id === message.id ? message : m)))
  })

  const handleAddMessage = useMemoizedFn(() => {
    setMessages([
      ...messages,
      {
        id: crypto.randomUUID(),
        content: '',
        enabled: true,
      },
    ])
  })

  const handleDeleteMessage = useMemoizedFn((id: string) => {
    setMessages(messages.filter(m => m.id !== id))
  })

  const handleResetToDefault = useMemoizedFn(() => {
    resetToDefaultMessages()
    toast.success('已恢复为默认报时消息列表')
  })

  const handleBatchImport = useMemoizedFn(() => {
    const text = importText.trim()
    if (!text) {
      toast.error('请输入要导入的报时消息')
      return
    }

    // 按换行分割导入
    const newMessages = text
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .map(content => ({
        id: crypto.randomUUID(),
        content,
        enabled: true,
      }))

    if (newMessages.length === 0) {
      toast.error('没有有效的报时消息可导入')
      return
    }

    setMessages([...messages, ...newMessages])
    setImportText('')
    setBatchImportOpen(false)
    toast.success(`成功导入 ${newMessages.length} 条报时消息`)
  })

  const handleClearAllMessages = useMemoizedFn(() => {
    setMessages([])
    toast.success('所有报时消息已清空')
  })

  // AI泛化功能
  const handleGeneralize = useMemoizedFn(async () => {
    if (messages.length === 0) {
      toast.error('请先添加至少1条报时消息才能进行AI泛化')
      return
    }

    const { provider } = aiStore.config
    const apiKey = aiStore.apiKeys[provider]
    const customBaseURL = aiStore.customBaseURL

    if (!apiKey) {
      toast.error('请先配置AI API密钥')
      return
    }

    setIsGeneralizing(true)

    try {
      // 使用第一条消息进行泛化
      const firstMessage = messages[0]
      const originalContent = firstMessage.content
      const prompt = generalizationPrompt.replace('{original_content}', originalContent)

      const requestMessages = [
        {
          role: 'user' as const,
          content: prompt,
        },
      ]

      const response = await window.ipcRenderer.invoke(IPC_CHANNELS.tasks.aiChat.normalChat, {
        messages: requestMessages,
        apiKey,
        provider: aiStore.config.provider,
        model: aiStore.config.model,
        customBaseURL: aiStore.config.provider === 'custom' ? customBaseURL : undefined,
      })

      if (!response) {
        throw new Error('AI服务返回空结果')
      }

      // 解析AI返回的结果，按行分割
      const generatedContents = response
        .split('\n')
        .map((line: string) => line.trim())
        .filter((line: string) => line.length > 0 && !line.match(/^\d+\./)) // 过滤空行和编号
        .slice(0, 8) // 最多取8个变体

      if (generatedContents.length > 0) {
        // 创建新的消息对象
        const newMessages = generatedContents.map(content => ({
          id: crypto.randomUUID(),
          content,
          enabled: true,
        }))

        // 添加到现有消息列表
        setMessages([...messages, ...newMessages])
        toast.success(`成功生成 ${generatedContents.length} 条报时消息变体`)
      } else {
        toast.error('AI生成的内容为空')
      }
    } catch (error) {
      console.error('AI泛化失败:', error)
      toast.error('AI泛化失败，请检查AI服务配置')
    } finally {
      setIsGeneralizing(false)
    }
  })

  // 提示词设置相关函数
  const handleOpenPromptDialog = useMemoizedFn(() => {
    setTempPrompt(generalizationPrompt)
    setShowPromptDialog(true)
  })

  const handleSavePrompt = useMemoizedFn(() => {
    setGeneralizationPrompt(tempPrompt)
    localStorage.setItem('time-announcement-generalization-prompt', tempPrompt)
    setShowPromptDialog(false)
    toast.success('AI泛化提示词已保存')
  })

  const handleCancelPrompt = useMemoizedFn(() => {
    setTempPrompt(generalizationPrompt)
    setShowPromptDialog(false)
  })

  return (
    <>
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>报时消息列表</Label>
                <p className="text-sm text-muted-foreground">
                  添加需要定时播报的消息内容，支持{'{时间}'}占位符（例：现在是{'{时间}'}，感谢大家的观看！）
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleOpenPromptDialog}
                  title="设置AI泛化提示词"
                >
                  <Settings className="h-4 w-4" />
                  提示词
                </Button>
                <Button variant="outline" size="sm" onClick={handleResetToDefault}>
                  <RotateCcwIcon className=" h-4 w-4" />
                  默认
                </Button>

                <Dialog open={batchImportOpen} onOpenChange={setBatchImportOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <DownloadIcon className=" h-4 w-4" />
                      导入
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md">
                    <DialogHeader>
                      <DialogTitle>批量导入报时消息</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="import-text" className="text-sm font-medium">
                          输入报时消息
                        </Label>
                        <Textarea
                          id="import-text"
                          value={importText}
                          onChange={(e) => setImportText(e.target.value)}
                          placeholder="请输入报时消息，每行一条：&#10;现在时间{时间}，新朋友记得点击关注！&#10;{时间}了，感谢大家的热情参与和支持！&#10;当前时间为{时间}，我看到直播间有[人数]位宝宝！"
                          className="min-h-[120px] resize-none"
                        />
                        <div className="text-xs text-muted-foreground">
                          每行一条报时消息，支持 {'{时间}'} 占位符
                        </div>
                      </div>

                      <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setBatchImportOpen(false)}>
                          取消
                        </Button>
                        <Button onClick={handleBatchImport} disabled={!importText.trim()}>
                          导入
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleGeneralize}
                  disabled={isGeneralizing || messages.length === 0}
                  title="使用第一条消息进行AI泛化"
                >
                  {isGeneralizing ? (
                    <Loader2 className=" h-4 w-4 animate-spin" />
                  ) : (
                    <Sparkles className=" h-4 w-4" />
                  )}
                  泛化
                </Button>

                {messages.length > 0 && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Trash2Icon className=" h-4 w-4" />
                        清空
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>确认清空报时消息列表？</AlertDialogTitle>
                        <AlertDialogDescription>
                          此操作将清空所有报时消息（共 {messages.length} 条），此操作不可撤销。
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleClearAllMessages}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          确认清空
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}

                <Button variant="outline" size="sm" onClick={handleAddMessage}>
                  <PlusIcon className=" h-4 w-4" />
                  添加
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              {messages.map(message => (
                <TimeAnnouncementMessageComp
                  key={message.id}
                  message={message}
                  onChange={handleMessageChange}
                  onDelete={handleDeleteMessage}
                />
              ))}
              {messages.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  暂无报时消息，点击上方按钮添加
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* AI泛化提示词设置对话框 */}
      <Dialog open={showPromptDialog} onOpenChange={setShowPromptDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>报时消息AI泛化提示词设置</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Textarea
              placeholder="输入完整的AI泛化提示词..."
              value={tempPrompt}
              onChange={e => setTempPrompt(e.target.value)}
              className="text-sm min-h-[300px]"
            />
            <p className="text-xs text-muted-foreground">
              完整的AI泛化提示词，使用 <code>{'{original_content}'}</code> 作为原始内容的占位符。此设置将应用于报时消息的AI泛化。
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelPrompt}>
              取消
            </Button>
            <Button onClick={handleSavePrompt}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
})

export default TimeAnnouncementListCard
