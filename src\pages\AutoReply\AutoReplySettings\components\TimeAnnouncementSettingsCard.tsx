import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  useAutoTimeAnnouncementActions,
  useCurrentAutoTimeAnnouncement,
} from '@/hooks/useAutoTimeAnnouncement'
import { useMemoizedFn } from 'ahooks'
import React from 'react'

const TimeAnnouncementSettingsCard = React.memo(() => {
  const { scheduler, random, enabled } = useCurrentAutoTimeAnnouncement(
    context => context.config,
  )
  const { setScheduler, setRandom, setEnabled } = useAutoTimeAnnouncementActions()

  const handleIntervalChange = useMemoizedFn((index: 0 | 1, value: string) => {
    const numValue = Number(value) * 1000
    setScheduler({
      interval:
        index === 0
          ? [numValue, scheduler.interval[1]]
          : [scheduler.interval[0], numValue],
    })
  })

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>报时设置</Label>
              <p className="text-sm text-muted-foreground">
                配置自动报时的相关选项
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="random"
                  checked={random}
                  onCheckedChange={setRandom}
                />
                <Label htmlFor="random" className="cursor-pointer">
                  随机播报
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="enabled"
                  checked={enabled}
                  onCheckedChange={setEnabled}
                />
                <Label htmlFor="enabled" className="cursor-pointer">
                  启用报时
                </Label>
              </div>
            </div>
          </div>

          <div className="space-y-1">
            <Label>播报间隔（秒）</Label>
            <div className="flex items-center space-x-2">
              <Input
                type="number"
                value={scheduler.interval[0] / 1000}
                onChange={e => handleIntervalChange(0, e.target.value)}
                className="w-24"
                min="60"
                placeholder="最小"
              />
              <span className="text-sm text-muted-foreground">至</span>
              <Input
                type="number"
                value={scheduler.interval[1] / 1000}
                onChange={e => handleIntervalChange(1, e.target.value)}
                className="w-24"
                min="60"
                placeholder="最大"
              />
              <span className="text-sm text-muted-foreground">秒</span>
            </div>
            <p className="text-xs text-muted-foreground">
              系统将在设定的时间区间内随机选择播报时机，建议设置较长间隔避免过于频繁
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
})

export default TimeAnnouncementSettingsCard
