import React from 'react'
import type { StringFilter, StringFilterConfig } from '@/utils/filter'

const conditionTextMap = {
  eq: '等于',
  includes: '包含',
} as const

const fieldNameMap = {
  nick_name: '用户名',
  order_status: '订单状态',
  product_title: '商品名称',
} as const

const FilterText: React.FC<{
  filterConfig: StringFilterConfig
}> = ({ filterConfig }) => {
  const renderCondition = (
    field: keyof typeof fieldNameMap,
    condition: StringFilter,
  ) => {
    const label = fieldNameMap[field] || field
    const lines: {
      prefix: string
      suffix: string
      label: string
      conditionText: string
      value: string
    }[] = []

    for (const [key, values] of Object.entries(condition)) {
      if (!values || values.length === 0) return

      const conditionText = conditionTextMap[key as keyof StringFilter] || key

      values.forEach((value, index) => {
        const prefix = index === 0 ? '当' : '或'
        const suffix = index === values.length - 1 ? ' 时' : ''
        lines.push({
          prefix,
          suffix,
          label,
          conditionText,
          value,
        })
      })
    }

    return lines.map(({ prefix, suffix, conditionText, value, label }, i) => (
      // biome-ignore lint/suspicious/noArrayIndexKey: 用下标问题不大
      <div key={i}>
        <span>{prefix}</span>
        <span className="px-1 text-amber-200">{label}</span>
        <span className="text-fuchsia-100">{conditionText}</span>
        <span className="px-1 text-emerald-200">{value}</span>
        <span>{suffix}</span>
      </div>
    ))
  }

  return (
    <div>
      {Object.entries(filterConfig).map(([field, condition]) =>
        renderCondition(field as keyof typeof fieldNameMap, condition),
      )}
    </div>
  )
}

export default FilterText
