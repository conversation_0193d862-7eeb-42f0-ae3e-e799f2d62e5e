import { useState, useRef, useCallback } from 'react'
import { setAudioOutputDevice, isAudioOutputSupported } from '@/utils/audioDevices'
import { useAutoVoice } from './useAutoVoice'

interface PreviewState {
  isPlaying: boolean
  currentTime: number
  duration: number
  progress: number // 0-100 的百分比
}

export const useAudioPreview = () => {
  const [previewStates, setPreviewStates] = useState<Record<string, PreviewState>>({})
  const audioRefs = useRef<Record<string, HTMLAudioElement>>({})
  const progressIntervals = useRef<Record<string, NodeJS.Timeout>>({})
  
  const { selectedDeviceId, volume, playbackSpeed } = useAutoVoice()

  // 开始预览播放
  const startPreview = useCallback(async (voiceId: string, audioUrl: string) => {
    try {
      // 停止其他正在播放的预览（直接实现停止逻辑，避免循环依赖）
      Object.keys(audioRefs.current).forEach(id => {
        if (id !== voiceId && audioRefs.current[id]) {
          const audio = audioRefs.current[id]
          if (audio) {
            audio.pause()
            audio.currentTime = 0
          }

          // 清除进度更新
          if (progressIntervals.current[id]) {
            clearInterval(progressIntervals.current[id])
            delete progressIntervals.current[id]
          }

          // 更新状态
          setPreviewStates(prev => ({
            ...prev,
            [id]: {
              isPlaying: false,
              currentTime: 0,
              duration: prev[id]?.duration || 0,
              progress: 0,
            }
          }))
        }
      })

      // 创建或获取音频元素
      if (!audioRefs.current[voiceId]) {
        const audio = new Audio(audioUrl)
        audioRefs.current[voiceId] = audio

        // 设置音量 (0-100 转换为 0-1)
        audio.volume = volume / 100

        // 设置播放速度
        audio.playbackRate = playbackSpeed

        // 设置音频输出设备
        if (isAudioOutputSupported() && selectedDeviceId !== 'default') {
          await setAudioOutputDevice(audio, selectedDeviceId)
        }

        // 监听音频事件
        audio.addEventListener('loadedmetadata', () => {
          setPreviewStates(prev => ({
            ...prev,
            [voiceId]: {
              ...prev[voiceId],
              duration: audio.duration,
            }
          }))
        })

        audio.addEventListener('ended', () => {
          stopPreview(voiceId)
        })

        audio.addEventListener('error', (e) => {
          console.error('音频预览播放错误:', e)
          stopPreview(voiceId)
        })
      }

      const audio = audioRefs.current[voiceId]
      
      // 重置播放位置
      audio.currentTime = 0
      
      // 开始播放
      await audio.play()

      // 设置播放状态
      setPreviewStates(prev => ({
        ...prev,
        [voiceId]: {
          isPlaying: true,
          currentTime: 0,
          duration: audio.duration || 0,
          progress: 0,
        }
      }))

      // 开始进度更新
      startProgressUpdate(voiceId)

    } catch (error) {
      console.error('开始音频预览失败:', error)
      setPreviewStates(prev => ({
        ...prev,
        [voiceId]: {
          isPlaying: false,
          currentTime: 0,
          duration: 0,
          progress: 0,
        }
      }))
    }
  }, [selectedDeviceId, volume, playbackSpeed])

  // 停止预览播放
  const stopPreview = useCallback((voiceId: string) => {
    const audio = audioRefs.current[voiceId]
    if (audio) {
      audio.pause()
      audio.currentTime = 0
    }

    // 清除进度更新
    if (progressIntervals.current[voiceId]) {
      clearInterval(progressIntervals.current[voiceId])
      delete progressIntervals.current[voiceId]
    }

    // 更新状态
    setPreviewStates(prev => ({
      ...prev,
      [voiceId]: {
        isPlaying: false,
        currentTime: 0,
        duration: prev[voiceId]?.duration || 0,
        progress: 0,
      }
    }))
  }, [])

  // 开始进度更新
  const startProgressUpdate = useCallback((voiceId: string) => {
    // 清除之前的定时器
    if (progressIntervals.current[voiceId]) {
      clearInterval(progressIntervals.current[voiceId])
    }

    progressIntervals.current[voiceId] = setInterval(() => {
      const audio = audioRefs.current[voiceId]
      if (audio && !audio.paused) {
        const currentTime = audio.currentTime
        const duration = audio.duration || 0
        const progress = duration > 0 ? (currentTime / duration) * 100 : 0

        setPreviewStates(prev => ({
          ...prev,
          [voiceId]: {
            ...prev[voiceId],
            currentTime,
            progress,
          }
        }))

        // 如果播放结束，停止更新
        if (currentTime >= duration) {
          // 直接调用停止逻辑，避免循环依赖
          const audio = audioRefs.current[voiceId]
          if (audio) {
            audio.pause()
            audio.currentTime = 0
          }

          // 清除进度更新
          if (progressIntervals.current[voiceId]) {
            clearInterval(progressIntervals.current[voiceId])
            delete progressIntervals.current[voiceId]
          }

          // 更新状态
          setPreviewStates(prev => ({
            ...prev,
            [voiceId]: {
              isPlaying: false,
              currentTime: 0,
              duration: prev[voiceId]?.duration || 0,
              progress: 0,
            }
          }))
        }
      }
    }, 100) // 每100ms更新一次
  }, [])

  // 切换播放/暂停
  const togglePreview = useCallback(async (voiceId: string, audioUrl: string) => {
    // 获取当前状态
    const currentState = previewStates[voiceId]

    if (currentState?.isPlaying) {
      // 如果正在播放，停止播放
      stopPreview(voiceId)
    } else {
      // 如果没有播放，开始播放
      await startPreview(voiceId, audioUrl)
    }
  }, [previewStates, startPreview, stopPreview])

  // 清理资源
  const cleanup = useCallback(() => {
    Object.keys(audioRefs.current).forEach(voiceId => {
      stopPreview(voiceId)
      const audio = audioRefs.current[voiceId]
      if (audio) {
        audio.src = ''
        audio.load()
      }
    })
    audioRefs.current = {}
    setPreviewStates({})
  }, [stopPreview])

  return {
    previewStates,
    startPreview,
    stopPreview,
    togglePreview,
    cleanup,
  }
}
