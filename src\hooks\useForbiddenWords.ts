import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

interface ForbiddenWordsState {
  forbiddenWords: string[]
  addForbiddenWord: (word: string) => void
  addForbiddenWords: (words: string[]) => void
  removeForbiddenWord: (word: string) => void
  clearForbiddenWords: () => void
}

export const useForbiddenWords = create<ForbiddenWordsState>()(
  persist(
    immer(set => ({
      forbiddenWords: [],

      addForbiddenWord: (word: string) => {
        set(state => {
          if (!state.forbiddenWords.includes(word)) {
            state.forbiddenWords.push(word)
          }
        })
      },

      addForbiddenWords: (words: string[]) => {
        set(state => {
          const newWords = words.filter(word => !state.forbiddenWords.includes(word))
          state.forbiddenWords.push(...newWords)
        })
      },

      removeForbiddenWord: (word: string) => {
        set(state => {
          const index = state.forbiddenWords.indexOf(word)
          if (index > -1) {
            state.forbiddenWords.splice(index, 1)
          }
        })
      },

      clearForbiddenWords: () => {
        set(state => {
          state.forbiddenWords = []
        })
      },
    })),
    {
      name: 'forbidden-words-storage',
    }
  )
)
