import { useState, useEffect } from 'react'

/**
 * 用于持久化tab选择状态的hook
 * @param storageKey 本地存储的key
 * @param defaultValue 默认值
 * @returns [currentValue, setValue] 当前值和设置值的函数
 */
export function useTabPersistence(storageKey: string, defaultValue: string) {
  const [value, setValue] = useState<string>(() => {
    try {
      const storedValue = localStorage.getItem(storageKey)
      return storedValue || defaultValue
    } catch (error) {
      console.warn(`Failed to read from localStorage for key "${storageKey}":`, error)
      return defaultValue
    }
  })

  useEffect(() => {
    try {
      localStorage.setItem(storageKey, value)
    } catch (error) {
      console.warn(`Failed to write to localStorage for key "${storageKey}":`, error)
    }
  }, [storageKey, value])

  return [value, setValue] as const
}
