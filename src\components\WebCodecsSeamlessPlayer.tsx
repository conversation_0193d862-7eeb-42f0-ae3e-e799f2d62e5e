import React, { useRef, useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'

export interface VideoItem {
  id: string
  url: string
  text: string
}

export interface WebCodecsSeamlessPlayerProps {
  onVideoEnded?: (videoId: string) => void
  onError?: (error: Error, videoId?: string) => void
  className?: string
  autoPlay?: boolean
  muted?: boolean
  volume?: number
}

export interface WebCodecsSeamlessPlayerRef {
  playVideo: (video: VideoItem) => Promise<void>
  addToQueue: (video: VideoItem) => void
  setQueue: (videos: VideoItem[]) => void
  playNext: () => Promise<boolean>
  stop: () => void
  getState: () => any
  getCurrentText: () => string
  isPlaying: () => boolean
}

interface PlayerState {
  currentVideoId: string | null
  currentText: string
  isPlaying: boolean
  isTransitioning: boolean
  frameCount: number
}

/**
 * 基于WebCodecs的超级无缝视频播放器
 * 使用硬件解码和帧级别的精确控制
 */
export const WebCodecsSeamlessPlayer = forwardRef<WebCodecsSeamlessPlayerRef, WebCodecsSeamlessPlayerProps>(({
  onVideoEnded,
  onError,
  className = '',
  autoPlay = true,
  muted = false,
  volume = 1
}, ref) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const audioContextRef = useRef<AudioContext | null>(null)
  const animationFrameRef = useRef<number | undefined>(undefined)

  const [state, setState] = useState<PlayerState>({
    currentVideoId: null,
    currentText: '',
    isPlaying: false,
    isTransitioning: false,
    frameCount: 0
  })

  const [videoQueue, setVideoQueue] = useState<VideoItem[]>([])
  const [decodedFrames, setDecodedFrames] = useState<Map<string, VideoFrame[]>>(new Map())
  const [currentFrameIndex, setCurrentFrameIndex] = useState(0)
  const [targetFPS] = useState(30) // 目标帧率

  // 检查WebCodecs支持
  const isWebCodecsSupported = useCallback(() => {
    return 'VideoDecoder' in window && 'VideoFrame' in window
  }, [])

  /**
   * 初始化音频上下文
   */
  const initializeAudioContext = useCallback(() => {
    if (!audioContextRef.current && !muted) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
    }
  }, [muted])

  /**
   * 解码视频文件
   */
  const decodeVideo = useCallback(async (video: VideoItem): Promise<VideoFrame[]> => {
    if (!isWebCodecsSupported()) {
      throw new Error('WebCodecs不支持，请使用现代浏览器')
    }

    console.log('开始解码视频:', video.text)

    try {
      // 获取视频数据
      const response = await fetch(video.url)
      const arrayBuffer = await response.arrayBuffer()

      // 创建视频解码器
      const frames: VideoFrame[] = []
      let isDecoding = true

      const decoder = new VideoDecoder({
        output: (frame: VideoFrame) => {
          frames.push(frame)
          console.log(`解码帧 ${frames.length}:`, frame.timestamp)
        },
        error: (error: Error) => {
          console.error('视频解码错误:', error)
          isDecoding = false
        }
      })

      // 配置解码器（这里简化，实际需要解析视频格式）
      const config = {
        codec: 'avc1.42E01E', // H.264 Baseline
        codedWidth: 1920,
        codedHeight: 1080,
      }

      decoder.configure(config)

      // 模拟解码过程（实际需要解析MP4容器格式）
      // 这里简化为直接使用video元素预解码
      const tempVideo = document.createElement('video')
      tempVideo.src = video.url
      tempVideo.muted = true

      await new Promise<void>((resolve, reject) => {
        tempVideo.onloadeddata = () => {
          console.log('视频元数据加载完成')
          resolve()
        }
        tempVideo.onerror = reject
        tempVideo.load()
      })

      // 使用canvas捕获帧（WebCodecs的简化实现）
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      canvas.width = tempVideo.videoWidth
      canvas.height = tempVideo.videoHeight

      const captureFrames = async () => {
        const duration = tempVideo.duration
        const frameInterval = 1 / targetFPS
        const totalFrames = Math.floor(duration * targetFPS)

        for (let i = 0; i < totalFrames; i++) {
          const time = i * frameInterval
          tempVideo.currentTime = time

          await new Promise(resolve => {
            tempVideo.onseeked = resolve
          })

          // 捕获当前帧
          ctx.drawImage(tempVideo, 0, 0)
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)

          // 创建VideoFrame（简化版本）
          const frame = new VideoFrame(imageData, {
            timestamp: time * 1000000, // 微秒
            duration: frameInterval * 1000000
          })

          frames.push(frame)
        }
      }

      await captureFrames()
      decoder.close()

      console.log(`视频解码完成: ${video.text}, 总帧数: ${frames.length}`)
      return frames

    } catch (error) {
      console.error('解码视频失败:', error)
      throw error
    }
  }, [isWebCodecsSupported, targetFPS])

  /**
   * 预解码视频
   */
  const preDecodeVideo = useCallback(async (video: VideoItem): Promise<void> => {
    if (decodedFrames.has(video.id)) {
      console.log('视频已预解码:', video.text)
      return
    }

    console.log('开始预解码视频:', video.text)

    try {
      const frames = await decodeVideo(video)
      setDecodedFrames(prev => new Map(prev).set(video.id, frames))
      console.log('视频预解码完成:', video.text)
    } catch (error) {
      console.error('预解码视频失败:', video.text, error)
      onError?.(error as Error, video.id)
    }
  }, [decodedFrames, decodeVideo, onError])

  /**
   * 渲染帧到Canvas
   */
  const renderFrame = useCallback((frame: VideoFrame) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 绘制视频帧
    ctx.drawImage(frame, 0, 0, canvas.width, canvas.height)

    setState(prev => ({ ...prev, frameCount: prev.frameCount + 1 }))
  }, [])

  /**
   * 播放循环
   */
  const playbackLoop = useCallback(() => {
    if (!state.isPlaying || !state.currentVideoId) {
      return
    }

    const frames = decodedFrames.get(state.currentVideoId)
    if (!frames || frames.length === 0) {
      return
    }

    const currentFrame = frames[currentFrameIndex]
    if (currentFrame) {
      renderFrame(currentFrame)

      // 移动到下一帧
      setCurrentFrameIndex(prev => {
        const nextIndex = prev + 1

        // 如果到达视频末尾
        if (nextIndex >= frames.length) {
          // 触发视频结束事件
          setTimeout(() => {
            if (state.currentVideoId) {
              onVideoEnded?.(state.currentVideoId)
            }

            // 播放下一个视频
            if (videoQueue.length > 0) {
              playNext()
            } else {
              setState(prev => ({ ...prev, isPlaying: false }))
            }
          }, 0)

          return 0 // 重置帧索引
        }

        return nextIndex
      })
    }

    // 按目标帧率调度下一帧
    const frameDelay = 1000 / targetFPS
    setTimeout(() => {
      animationFrameRef.current = requestAnimationFrame(playbackLoop)
    }, frameDelay)

  }, [state.isPlaying, state.currentVideoId, decodedFrames, currentFrameIndex, renderFrame, onVideoEnded, videoQueue, targetFPS])

  /**
   * 播放指定视频
   */
  const playVideo = useCallback(async (video: VideoItem): Promise<void> => {
    console.log('开始播放视频:', video.text)

    try {
      // 预解码视频（如果还没有）
      if (!decodedFrames.has(video.id)) {
        await preDecodeVideo(video)
      }

      // 初始化音频上下文
      initializeAudioContext()

      // 更新状态
      setState(prev => ({
        ...prev,
        currentVideoId: video.id,
        currentText: video.text,
        isPlaying: true
      }))

      setCurrentFrameIndex(0)

      // 开始播放循环
      if (!animationFrameRef.current) {
        playbackLoop()
      }

      console.log('视频开始播放:', video.text)
    } catch (error) {
      console.error('播放视频失败:', error)
      onError?.(error as Error, video.id)
      throw error
    }
  }, [decodedFrames, preDecodeVideo, initializeAudioContext, playbackLoop, onError])

  /**
   * 添加视频到播放队列
   */
  const addToQueue = useCallback((video: VideoItem) => {
    setVideoQueue(prev => [...prev, video])

    // 自动预解码下一个视频
    if (videoQueue.length < 2) {
      preDecodeVideo(video)
    }
  }, [videoQueue.length, preDecodeVideo])

  /**
   * 设置播放队列
   */
  const setQueue = useCallback((videos: VideoItem[]) => {
    setVideoQueue(videos)
    setDecodedFrames(new Map())
    setCurrentFrameIndex(0)

    // 预解码前几个视频
    videos.slice(0, 2).forEach(video => {
      preDecodeVideo(video)
    })
  }, [preDecodeVideo])

  /**
   * 播放队列中的下一个视频
   */
  const playNext = useCallback(async (): Promise<boolean> => {
    if (videoQueue.length === 0) {
      console.log('播放队列为空')
      setState(prev => ({ ...prev, isPlaying: false }))
      return false
    }

    const nextVideo = videoQueue[0]
    setVideoQueue(prev => prev.slice(1))

    try {
      setState(prev => ({ ...prev, isTransitioning: true }))

      // 无缝切换到下一个视频
      setState(prev => ({
        ...prev,
        currentVideoId: nextVideo.id,
        currentText: nextVideo.text,
        isTransitioning: false
      }))

      setCurrentFrameIndex(0)

      // 预解码后续视频
      if (videoQueue.length > 1) {
        preDecodeVideo(videoQueue[1])
      }

      return true
    } catch (error) {
      console.error('播放下一个视频失败:', error)
      setState(prev => ({ ...prev, isTransitioning: false }))
      return false
    }
  }, [videoQueue, preDecodeVideo])

  // 设置Canvas尺寸
  useEffect(() => {
    const canvas = canvasRef.current
    if (canvas) {
      const rect = canvas.getBoundingClientRect()
      canvas.width = rect.width * window.devicePixelRatio
      canvas.height = rect.height * window.devicePixelRatio

      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.scale(window.devicePixelRatio, window.devicePixelRatio)
      }
    }
  }, [])

  // 启动播放循环
  useEffect(() => {
    if (state.isPlaying && !animationFrameRef.current) {
      playbackLoop()
    }
  }, [state.isPlaying, playbackLoop])

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    playVideo,
    addToQueue,
    setQueue,
    playNext,
    stop: () => {
      setState(prev => ({ ...prev, isPlaying: false }))
      setVideoQueue([])
      setDecodedFrames(new Map())
      setCurrentFrameIndex(0)

      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
        animationFrameRef.current = undefined
      }

      // 清理音频上下文
      if (audioContextRef.current) {
        audioContextRef.current.close()
        audioContextRef.current = null
      }
    },
    getState: () => state,
    getCurrentText: () => state.currentText,
    isPlaying: () => state.isPlaying
  }), [playVideo, addToQueue, setQueue, playNext, state])

  // 清理资源
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }

      // 清理所有VideoFrame
      decodedFrames.forEach(frames => {
        frames.forEach(frame => frame.close())
      })
    }
  }, [decodedFrames])

  // 检查浏览器支持
  if (!isWebCodecsSupported()) {
    return (
      <div className={`relative w-full h-full ${className} flex items-center justify-center bg-gray-800`}>
        <div className="text-white text-center">
          <div className="text-lg font-semibold mb-2">WebCodecs不支持</div>
          <div className="text-sm text-gray-300">
            请使用支持WebCodecs的现代浏览器<br />
            (Chrome 94+, Edge 94+)
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative w-full h-full ${className}`}>
      {/* Canvas渲染层 */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full object-contain bg-black"
        style={{ width: '100%', height: '100%' }}
      />

      {/* 过渡指示器 */}
      {state.isTransitioning && (
        <div className="absolute top-4 right-4 bg-blue-600 text-white text-xs px-2 py-1 rounded">
          切换中...
        </div>
      )}

      {/* 调试信息（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs p-2 rounded">
          <div>视频ID: {state.currentVideoId}</div>
          <div>队列长度: {videoQueue.length}</div>
          <div>预解码: {decodedFrames.size}</div>
          <div>当前帧: {currentFrameIndex}</div>
          <div>总帧数: {state.frameCount}</div>
          <div>过渡中: {state.isTransitioning ? '是' : '否'}</div>
        </div>
      )}
    </div>
  )
})

WebCodecsSeamlessPlayer.displayName = 'WebCodecsSeamlessPlayer'

export default WebCodecsSeamlessPlayer
