name: Weekly Update Providers

on:
  schedule:
    - cron: '0 0 * * 1'  # 每周一 00:00 UTC
  workflow_dispatch:  # 手动触发也可以

permissions:
  contents: write
  pull-requests: write

jobs:
  update-providers:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: install dependencies
        run: pnpm install

      - name: Run updateProviders script
        run: pnpm tsx scripts/updateProvidersModels.js
        env:
          OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
          SILICONFLOW_API_KEY: ${{ secrets.SILICONFLOW_API_KEY }}

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore: update providers models [automated]'
          title: 'chore: update providers models (auto)'
          body: 'This PR was automatically generated by the updateProviders workflow.'
          branch: auto/update-providers
          delete-branch: true
