import React, { useRef, useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'

export interface VideoItem {
  id: string
  url: string
  text: string
}

export interface SeamlessVideoPlayerProps {
  onVideoEnded?: (videoId: string) => void
  onError?: (error: Error, videoId?: string) => void
  className?: string
  autoPlay?: boolean
  muted?: boolean
  volume?: number
}

export interface SeamlessVideoPlayerRef {
  playVideo: (video: VideoItem) => Promise<void>
  addToQueue: (video: VideoItem) => void
  setQueue: (videos: VideoItem[]) => void
  playNext: () => Promise<boolean>
  stop: () => void
  getState: () => any
  getCurrentText: () => string
  isPlaying: () => boolean
}

interface PlayerState {
  currentVideoId: string | null
  currentText: string
  isPlaying: boolean
  activePlayer: 'A' | 'B'
  nextPlayer: 'A' | 'B'
  isTransitioning: boolean
}

/**
 * 无缝视频播放器组件
 * 使用双缓冲机制实现真正无缝的视频切换
 */
export const SeamlessVideoPlayer = forwardRef<SeamlessVideoPlayerRef, SeamlessVideoPlayerProps>(({
  onVideoEnded,
  onError,
  className = '',
  autoPlay = true,
  muted = false,
  volume = 1
}, ref) => {
  // 双视频元素引用
  const videoARef = useRef<HTMLVideoElement>(null)
  const videoBRef = useRef<HTMLVideoElement>(null)

  // 播放状态
  const [state, setState] = useState<PlayerState>({
    currentVideoId: null,
    currentText: '',
    isPlaying: false,
    activePlayer: 'A',
    nextPlayer: 'B',
    isTransitioning: false
  })

  // 视频队列和预加载状态
  const [videoQueue, setVideoQueue] = useState<VideoItem[]>([])
  const [preloadedVideos, setPreloadedVideos] = useState<Map<string, boolean>>(new Map())

  // 切换时机控制（视频结束前多少毫秒开始切换）
  const SWITCH_THRESHOLD_MS = 50 // 减少到50ms，更精确的切换时机
  const PRELOAD_THRESHOLD_MS = 2000 // 提前2秒预加载

  /**
   * 获取当前活跃的视频元素
   */
  const getActiveVideo = useCallback((): HTMLVideoElement | null => {
    return state.activePlayer === 'A' ? videoARef.current : videoBRef.current
  }, [state.activePlayer])

  /**
   * 获取下一个视频元素
   */
  const getNextVideo = useCallback((): HTMLVideoElement | null => {
    return state.nextPlayer === 'A' ? videoARef.current : videoBRef.current
  }, [state.nextPlayer])

  /**
   * 切换活跃播放器
   */
  const switchActivePlayer = useCallback(() => {
    setState(prev => ({
      ...prev,
      activePlayer: prev.nextPlayer,
      nextPlayer: prev.activePlayer,
      isTransitioning: false
    }))
  }, [])

  /**
   * 预加载视频
   */
  const preloadVideo = useCallback(async (video: VideoItem, videoElement: HTMLVideoElement): Promise<boolean> => {
    return new Promise((resolve) => {
      console.log(`开始预加载视频: ${video.text}`)

      const onCanPlay = () => {
        console.log(`视频预加载完成: ${video.text}`)
        videoElement.removeEventListener('canplay', onCanPlay)
        videoElement.removeEventListener('error', onError)
        setPreloadedVideos(prev => new Map(prev).set(video.id, true))
        resolve(true)
      }

      const onError = () => {
        console.error(`视频预加载失败: ${video.text}`)
        videoElement.removeEventListener('canplay', onCanPlay)
        videoElement.removeEventListener('error', onError)
        resolve(false)
      }

      videoElement.addEventListener('canplay', onCanPlay)
      videoElement.addEventListener('error', onError)

      // 设置视频源并开始预加载
      videoElement.src = video.url
      videoElement.load()

      // 设置超时
      setTimeout(() => {
        videoElement.removeEventListener('canplay', onCanPlay)
        videoElement.removeEventListener('error', onError)
        console.warn(`视频预加载超时: ${video.text}`)
        resolve(false)
      }, 10000)
    })
  }, [])

  /**
   * 播放指定视频
   */
  const playVideo = useCallback(async (video: VideoItem): Promise<void> => {
    const activeVideo = getActiveVideo()
    if (!activeVideo) {
      throw new Error('没有可用的视频元素')
    }

    console.log(`开始播放视频: ${video.text}`)

    try {
      // 如果视频已预加载，直接播放
      if (preloadedVideos.get(video.id)) {
        console.log('使用预加载的视频')
        await activeVideo.play()
      } else {
        // 否则先加载再播放
        console.log('视频未预加载，现在加载')
        activeVideo.src = video.url
        activeVideo.load()

        await new Promise<void>((resolve, reject) => {
          const onCanPlay = () => {
            activeVideo.removeEventListener('canplay', onCanPlay)
            activeVideo.removeEventListener('error', onError)
            resolve()
          }

          const onError = (e: Event) => {
            activeVideo.removeEventListener('canplay', onCanPlay)
            activeVideo.removeEventListener('error', onError)
            reject(new Error('视频加载失败'))
          }

          activeVideo.addEventListener('canplay', onCanPlay)
          activeVideo.addEventListener('error', onError)
        })

        await activeVideo.play()
      }

      // 更新状态
      setState(prev => ({
        ...prev,
        currentVideoId: video.id,
        currentText: video.text,
        isPlaying: true
      }))

      console.log(`视频开始播放: ${video.text}`)
    } catch (error) {
      console.error('播放视频失败:', error)
      onError?.(error as Error, video.id)
      throw error
    }
  }, [getActiveVideo, preloadedVideos, onError])

  /**
   * 添加视频到播放队列
   */
  const addToQueue = useCallback((video: VideoItem) => {
    setVideoQueue(prev => [...prev, video])
  }, [])

  /**
   * 设置播放队列
   */
  const setQueue = useCallback((videos: VideoItem[]) => {
    setVideoQueue(videos)
    setPreloadedVideos(new Map())
  }, [])

  /**
   * 播放队列中的下一个视频
   */
  const playNext = useCallback(async (): Promise<boolean> => {
    if (videoQueue.length === 0) {
      console.log('播放队列为空')
      setState(prev => ({ ...prev, isPlaying: false }))
      return false
    }

    const nextVideo = videoQueue[0]
    setVideoQueue(prev => prev.slice(1))

    try {
      await playVideo(nextVideo)
      return true
    } catch (error) {
      console.error('播放下一个视频失败:', error)
      return false
    }
  }, [videoQueue, playVideo])

  // 监听视频播放事件
  useEffect(() => {
    const activeVideo = getActiveVideo()
    if (!activeVideo) return

    const handleTimeUpdate = () => {
      if (state.isTransitioning) return

      const currentTime = activeVideo.currentTime
      const duration = activeVideo.duration

      if (duration && currentTime > 0) {
        const remainingTime = (duration - currentTime) * 1000 // 转换为毫秒

        // 提前预加载下一个视频
        if (remainingTime <= PRELOAD_THRESHOLD_MS && videoQueue.length > 0) {
          const nextVideo = videoQueue[0]
          const nextVideoElement = getNextVideo()

          if (nextVideoElement && !preloadedVideos.get(nextVideo.id)) {
            preloadVideo(nextVideo, nextVideoElement)
          }
        }

        // 精确切换时机 - 在最后几帧开始切换
        if (remainingTime <= SWITCH_THRESHOLD_MS && videoQueue.length > 0 && !state.isTransitioning) {
          const nextVideo = videoQueue[0]
          const nextVideoElement = getNextVideo()

          // 确保下一个视频已经预加载并准备就绪
          if (nextVideoElement && preloadedVideos.get(nextVideo.id)) {
            setState(prev => ({ ...prev, isTransitioning: true }))

            // 立即开始播放下一个视频（在当前视频结束前）
            nextVideoElement.currentTime = 0
            nextVideoElement.play().then(() => {
              // 在下一个视频开始播放后立即切换
              setTimeout(() => {
                switchActivePlayer()
                setState(prev => ({
                  ...prev,
                  currentVideoId: nextVideo.id,
                  currentText: nextVideo.text,
                  isTransitioning: false
                }))
                setVideoQueue(prev => prev.slice(1))
                console.log(`无缝切换完成: ${nextVideo.text}`)
              }, 10) // 10ms后切换，确保新视频已开始播放
            }).catch(error => {
              console.error('预播放下一个视频失败:', error)
              setState(prev => ({ ...prev, isTransitioning: false }))
            })
          }
        }
      }
    }

    const handleEnded = () => {
      console.log('当前视频播放结束')

      if (state.currentVideoId) {
        onVideoEnded?.(state.currentVideoId)
      }

      // 如果已经在切换过程中，不需要再次处理
      if (state.isTransitioning) {
        console.log('已在切换过程中，跳过ended事件处理')
        return
      }

      // 如果还有视频队列但没有预切换，进行降级处理
      if (videoQueue.length > 0) {
        console.log('视频结束时仍有队列，进行降级播放')
        playNext()
      } else {
        // 没有更多视频，停止播放
        console.log('播放队列已空，停止播放')
        setState(prev => ({ ...prev, isPlaying: false }))
      }
    }

    const handleError = (error: Event) => {
      console.error('视频播放出错:', error)
      setState(prev => ({ ...prev, isPlaying: false }))
      onError?.(new Error('视频播放出错'), state.currentVideoId || undefined)
    }

    activeVideo.addEventListener('timeupdate', handleTimeUpdate)
    activeVideo.addEventListener('ended', handleEnded)
    activeVideo.addEventListener('error', handleError)

    return () => {
      activeVideo.removeEventListener('timeupdate', handleTimeUpdate)
      activeVideo.removeEventListener('ended', handleEnded)
      activeVideo.removeEventListener('error', handleError)
    }
  }, [state, videoQueue, getActiveVideo, getNextVideo, preloadedVideos, onVideoEnded, onError, playNext, preloadVideo, switchActivePlayer])

  // 设置视频属性
  useEffect(() => {
    [videoARef.current, videoBRef.current].forEach(video => {
      if (video) {
        video.muted = muted
        video.volume = volume
        video.autoplay = autoPlay
      }
    })
  }, [muted, volume, autoPlay])

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    playVideo,
    addToQueue,
    setQueue,
    playNext,
    stop: () => {
      const activeVideo = getActiveVideo()
      if (activeVideo) {
        activeVideo.pause()
      }
      setState(prev => ({ ...prev, isPlaying: false }))
      setVideoQueue([])
      setPreloadedVideos(new Map())
    },
    getState: () => state,
    getCurrentText: () => state.currentText,
    isPlaying: () => state.isPlaying
  }), [playVideo, addToQueue, setQueue, playNext, getActiveVideo, state])

  return (
    <div className={`relative w-full h-full ${className}`}>
      {/* 视频元素A - 使用GPU加速和更快的切换 */}
      <video
        ref={videoARef}
        className={`absolute inset-0 w-full h-full object-contain will-change-transform ${state.activePlayer === 'A' ? 'z-10' : 'z-0'
          }`}
        style={{
          transform: 'translateZ(0)', // 强制GPU加速
          opacity: state.activePlayer === 'A' ? 1 : 0,
          transition: 'opacity 30ms ease-out' // 更快的切换
        }}
        muted={muted}
        autoPlay={autoPlay}
        playsInline
      />

      {/* 视频元素B - 使用GPU加速和更快的切换 */}
      <video
        ref={videoBRef}
        className={`absolute inset-0 w-full h-full object-contain will-change-transform ${state.activePlayer === 'B' ? 'z-10' : 'z-0'
          }`}
        style={{
          transform: 'translateZ(0)', // 强制GPU加速
          opacity: state.activePlayer === 'B' ? 1 : 0,
          transition: 'opacity 30ms ease-out' // 更快的切换
        }}
        muted={muted}
        autoPlay={autoPlay}
        playsInline
      />

      {/* 调试信息（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs p-2 rounded">
          <div>当前播放器: {state.activePlayer}</div>
          <div>视频ID: {state.currentVideoId}</div>
          <div>队列长度: {videoQueue.length}</div>
          <div>预加载: {preloadedVideos.size}</div>
          <div>切换中: {state.isTransitioning ? '是' : '否'}</div>
        </div>
      )}
    </div>
  )
})

SeamlessVideoPlayer.displayName = 'SeamlessVideoPlayer'

export default SeamlessVideoPlayer
