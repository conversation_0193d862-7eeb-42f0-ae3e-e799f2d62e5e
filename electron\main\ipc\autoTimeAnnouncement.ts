import { IPC_CHANNELS } from 'shared/ipcChannels'
import { createLogger } from '#/logger'
import { accountManager } from '#/managers/AccountManager'
import { contextManager } from '#/managers/BrowserContextManager'
import { taskManager } from '#/managers/TaskManager'
import { AutoTimeAnnouncementTask } from '#/tasks/autoTimeAnnouncement'
import { typedIpcMainHandle } from '#/utils'

const TASK_NAME = '自动报时'

// IPC 处理程序
function setupIpcHandlers() {
  typedIpcMainHandle(
    IPC_CHANNELS.tasks.autoTimeAnnouncement.start,
    async (_, config) => {
      try {
        taskManager.register(
          TASK_NAME,
          (page, account) => new AutoTimeAnnouncementTask(page, account, config),
        )
        taskManager.startTask(TASK_NAME)
        return true
      } catch (error) {
        const logger = createLogger(
          `${TASK_NAME} @${accountManager.getActiveAccount().name}`,
        )
        logger.error(
          '启动自动报时失败:',
          error instanceof Error ? error.message : error,
        )
        return false
      }
    },
  )

  typedIpcMainHandle(IPC_CHANNELS.tasks.autoTimeAnnouncement.stop, async () => {
    taskManager.stopTask(TASK_NAME)
    return true
  })

  typedIpcMainHandle(
    IPC_CHANNELS.tasks.autoTimeAnnouncement.updateConfig,
    async (_, newConfig) => {
      taskManager.updateTaskConfig(TASK_NAME, newConfig)
      return true
    },
  )
}

export function setupAutoTimeAnnouncementIpcHandlers() {
  setupIpcHandlers()
}
