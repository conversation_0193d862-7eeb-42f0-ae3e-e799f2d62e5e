import type { LogMessage } from 'electron-log'
import type {
  ProgressInfo,
  UpdateCheckResult,
  UpdateDownloadedEvent,
} from 'electron-updater'
import type { AutoPopUpConfig } from 'electron/main/tasks/autoPopUp'
import type { providers } from 'shared/providers'

import type { AutoReplyConfig } from '#/tasks/autoReply/index'
import type { AutoTimeAnnouncementConfig } from '#/tasks/autoTimeAnnouncement'
import { IPC_CHANNELS } from './ipcChannels'

export interface IpcChannels {
  // LiveControl
  'tasks:liveControl:connect': (params: {
    chromePath?: string
    headless?: boolean
    storageState?: string
    platform?: LiveControlPlatform
    testConnection?: boolean
  }) => {
    accountName: string | null
  } | null
  'tasks:liveControl:disconnect': () => boolean
  'tasks:liveControl:disconnectedEvent': (id: string) => void

  // AutoMessage
  'tasks:autoMessage:start': (config: AutoMessageConfig) => boolean
  'tasks:autoMessage:stop': () => boolean
  'tasks:autoMessage:stoppedEvent': (id: string) => void
  'tasks:autoMessage:sendBatchMessages': (
    messages: string[],
    count: number,
  ) => boolean

  // AutoTimeAnnouncement
  'tasks:autoTimeAnnouncement:start': (config: AutoTimeAnnouncementConfig) => boolean
  'tasks:autoTimeAnnouncement:stop': () => boolean
  'tasks:autoTimeAnnouncement:stoppedEvent': (id: string) => void
  'tasks:autoTimeAnnouncement:updateConfig': (config: AutoTimeAnnouncementConfig) => boolean

  // AutoVoice events
  'autoVoice:addTimeAnnouncement': (data: { id: string, text: string, type: string, timestamp: number }) => void
  'autoVoice:addPassiveInteraction': (data: { id: string, text: string, type: string, messageType: string, timestamp: number }) => void
  'autoVoice:addCommentReply': (data: { id: string, text: string, type: string, replyType: string, timestamp: number }) => void

  // AutoPopup
  [IPC_CHANNELS.tasks.autoPopUp.start]: (config: AutoPopUpConfig) => boolean
  [IPC_CHANNELS.tasks.autoPopUp.stop]: () => boolean
  [IPC_CHANNELS.tasks.autoPopUp.stoppedEvent]: (id: string) => void
  [IPC_CHANNELS.tasks.autoPopUp.updateConfig]: (
    config: Parital<AutoPopUpConfig>,
  ) => void
  'tasks:autoPopup:registerShortuct': (
    shortcuts: { accelerator: string; goodsIds: number[] }[],
  ) => void
  'tasks:autoPopup:unregisterShortcut': () => void

  // AutoReply
  'tasks:autoReply:startCommentListener': (
    config: AutoReplyConfig,
  ) => boolean
  'tasks:autoReply:stopCommentListener': () => void
  'tasks:autoReply:sendReply': (replyContent: string) => void
  'tasks:autoReply:listenerStopped': () => void
  'tasks:autoReply:showComment': (data: {
    comment: DouyinLiveMessage
    accountId: string
  }) => void

  // AutoVoice
  'tasks:autoVoice:stoppedEvent': () => void
  'tasks:autoVoice:start': () => void
  'tasks:autoVoice:stop': () => void

  // AIChat
  'tasks:aiChat:normalChat': (params: {
    messages: AIChatMessage[]
    provider: keyof typeof providers
    model: string
    apiKey: string
    customBaseURL?: string
  }) => string | null
  'tasks:aiChat:testApiKey': (params: {
    apiKey: string
    provider: keyof typeof providers
    customBaseURL?: string
  }) => { success: boolean; models?: string[]; error?: string }
  'tasks:aiChat:fetchModels': (params: {
    apiKey: string
    provider: keyof typeof providers
    customBaseURL?: string
  }) => string[]
  'tasks:aiChat:chat': (params: {
    // 用于启动流式传输，响应通过 stream/error 通道
    messages: AIChatMessage[]
    provider: keyof typeof providers
    model: string
    apiKey: string
    customBaseURL?: string
  }) => void
  'tasks:aiChat:stream': (
    data:
      | {
          chunk: string
          type: 'content' | 'reasoning'
        }
      | { done: boolean },
  ) => void
  'tasks:aiChat:error': (data: { error: string }) => void

  // Updater
  'updater:checkUpdate': (params: { source: string }) =>
    | UpdateCheckResult
    | { message: string; error: Error; downloadURL?: string }
    | null

  'updater:startDownload': () => void
  'updater:quitAndInstall': () => void
  'updater:updateAvailable': (info: VersionInfo) => void
  'updater:updateError': (error: ErrorType) => void
  'updater:downloadProgress': (progress: ProgressInfo) => void
  'updater:updateDownloaded': (
    event: UpdateDownloadedEvent,
  ) => void

  // Chrome
  [IPC_CHANNELS.chrome.selectPath]: () => string | null
  [IPC_CHANNELS.chrome.getPath]: (edge?: boolean) => string | null
  [IPC_CHANNELS.chrome.toggleDevTools]: () => void
  [IPC_CHANNELS.chrome.setPath]: (path: string) => void
  [IPC_CHANNELS.chrome.saveState]: (accountId: string, state: string) => void

  // App
  'app:openLogFolder': () => void
  'app:notifyUpdate': (arg: {
    currentVersion: string
    latestVersion: string
    releaseNote?: string
  }) => void

  [IPC_CHANNELS.account.switch]: (params: {
    account: Account
  }) => void

  // Log
  'log': (message: LogMessage) => void
  'sendLog': (params: {
    scope: string
    level: 'info' | 'warn' | 'error' | 'success'
    message: string
  }) => void

  // AudioService
  'audioService:selectScriptPath': () => string | null
  'audioService:start': (scriptPath: string) => { success: boolean; error?: string; pid?: number }
  'audioService:stop': () => { success: boolean; error?: string }
  'audioService:status': () => { isRunning: boolean; pid?: number }

  // DigitalHumanService
  'digitalHumanService:selectScriptPath': () => string | null
  'digitalHumanService:start': (scriptPath: string) => { success: boolean; error?: string; pid?: number }
  'digitalHumanService:stop': () => { success: boolean; error?: string }
  'digitalHumanService:status': () => { isRunning: boolean; pid?: number }

  // OBS Player
  'obsPlayer:open': (preferredAspectRatio?: '16:9' | '9:16') => void
  'obsPlayer:close': () => void
  'obsPlayer:playVideo': (videoUrl: string, text: string, id: string) => Promise<void>
  'obsPlayer:videoEnded': () => void
  'obsPlayer:setAspectRatio': (aspectRatio: '16:9' | '9:16') => void

  // Stream Server
  'streamServer:start': (options: { port: number }) => { success: boolean; error?: string; pid?: number }
  'streamServer:stop': () => { success: boolean; error?: string }
  'streamServer:status': () => { isRunning: boolean; pid?: number }
  'streamServer:startStream': (options: { rtmpUrl: string }) => { success: boolean; error?: string }
  'streamServer:stopStream': () => { success: boolean; error?: string }
  'streamServer:streamStatus': () => { isStreaming: boolean; pid?: number }
  'streamServer:startTaskStream': (options: { rtmpUrl: string }) => { success: boolean; error?: string }
  'streamServer:stopTaskStream': () => { success: boolean; error?: string }
  'streamServer:updateVoiceList': (voiceList: any[]) => { success: boolean }
  'streamServer:itemStreamed': (itemId: string) => void
}

export interface ElectronAPI {
  ipcRenderer: {
    invoke: <Channel extends keyof IpcChannels>(
      channel: Channel,
      ...args: Parameters<IpcChannels[Channel]>
    ) => ReturnType<IpcChannels[Channel]> extends Promise<infer U>
      ? ReturnType<IpcChannels[Channel]>
      : Promise<ReturnType<IpcChannels[Channel]>>

    send: <Channel extends keyof IpcChannels>(
      channel: Channel,
      ...args: Parameters<IpcChannels[Channel]>
    ) => void

    on: <Channel extends keyof IpcChannels>(
      channel: Channel,
      listener: (...args: Parameters<IpcChannels[Channel]>) => void,
    ) => () => void
  }
}
