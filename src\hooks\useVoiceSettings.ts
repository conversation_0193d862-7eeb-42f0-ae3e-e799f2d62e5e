import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { uploadVoiceFile, getVoiceFileList, deleteVoiceFile, type VoiceUploadResponse } from '@/utils/voiceUpload'

// 脚本路径项接口
interface ScriptPathItem {
  id: string; // 唯一标识
  name: string; // 显示名称
  path: string; // 脚本路径
  isRunning: boolean; // 是否正在运行
  pid?: number; // 进程ID
}

// 音色设置状态接口
interface VoiceSettingsState {
  // 状态
  voiceList: string[]; // 可用音色列表
  isLoading: boolean; // 是否正在加载
  isUploading: boolean; // 是否正在上传
  comfyUIServer: string; // ComfyUI服务器地址（文本转音频）
  comfyUIVideoServer: string; // ComfyUI服务器地址（音频转视频）
  comfyUIBasePath: string; // ComfyUI基础路径（脚本路径）
  heygemServerUrl: string; // HeyGem服务器地址
  digitalHumanScriptPath: string; // 数字人服务器脚本路径
  autoTuningEnabled: boolean; // 模型自动微调开关
  rtmpServerUrl: string; // RTMP推流服务器地址
  rtmpServerPort: number; // RTMP推流服务器端口
  scriptPaths: ScriptPathItem[]; // 脚本路径列表

  // 方法
  loadVoiceList: () => Promise<void>; // 加载音色列表
  uploadVoice: (file: File) => Promise<VoiceUploadResponse & { converted?: boolean, originalFormat?: string }>; // 上传音色文件
  deleteVoice: (fileName: string) => Promise<boolean>; // 删除音色文件
  refreshVoiceList: () => Promise<void>; // 刷新音色列表
  setComfyUIServer: (server: string) => void; // 设置ComfyUI服务器地址（文本转音频）
  setComfyUIVideoServer: (server: string) => void; // 设置ComfyUI服务器地址（音频转视频）
  setComfyUIBasePath: (path: string) => void; // 设置ComfyUI基础路径
  setHeygemServerUrl: (url: string) => void; // 设置HeyGem服务器地址
  setDigitalHumanScriptPath: (path: string) => void; // 设置数字人服务器脚本路径
  setAutoTuningEnabled: (enabled: boolean) => void; // 设置自动微调开关
  setRtmpServerUrl: (url: string) => void; // 设置RTMP推流服务器地址
  setRtmpServerPort: (port: number) => void; // 设置RTMP推流服务器端口
  // 脚本路径管理
  addScriptPath: (name: string, path: string) => void; // 添加脚本路径
  removeScriptPath: (id: string) => void; // 移除脚本路径
  updateScriptPath: (id: string, name: string, path: string) => void; // 更新脚本路径
  updateScriptPathStatus: (id: string, isRunning: boolean, pid?: number) => void; // 更新脚本路径状态
}

export const useVoiceSettings = create<VoiceSettingsState>()(
  persist(
    (set, get) => ({
      // 初始状态
      voiceList: ['默认音色.wav'], // 默认音色
      isLoading: false,
      isUploading: false,
      comfyUIServer: 'http://127.0.0.1:8188', // 默认ComfyUI服务器地址（文本转音频）
      comfyUIVideoServer: 'http://127.0.0.1:8188', // 默认ComfyUI服务器地址（音频转视频）
      comfyUIBasePath: 'D:\\SD2\\ComfyUI', // 默认ComfyUI基础路径
      heygemServerUrl: 'http://localhost:8383', // 默认HeyGem服务器地址
      digitalHumanScriptPath: 'D:\\SD2\\heygemService\\start.bat', // 默认数字人服务器脚本路径
      autoTuningEnabled: false, // 默认关闭自动微调
      rtmpServerUrl: 'rtmp://localhost:1935/live/stream', // 默认RTMP推流服务器地址
      rtmpServerPort: 1935, // 默认RTMP推流服务器端口
      scriptPaths: [ // 默认脚本路径列表
        {
          id: 'voice-clone',
          name: '声音克隆',
          path: 'D:\\SD2\\python_4.ps1',
          isRunning: false
        },
        {
          id: 'lip-sync',
          name: '口型同步',
          path: 'D:\\SD2\\python_4_1.ps1',
          isRunning: false
        }
      ],

      // 加载音色列表
      loadVoiceList: async () => {
        set({ isLoading: true });

        try {
          // 从服务器获取音色文件列表
          const serverVoices = await getVoiceFileList();
          const currentState = get();

          // 合并服务器音色和本地持久化的音色列表
          // 使用 Set 去重，保持服务器音色的优先级
          const mergedVoices = Array.from(new Set([
            ...serverVoices,
            ...currentState.voiceList.filter(voice => !serverVoices.includes(voice))
          ]));

          console.log('合并后的音色列表:', mergedVoices);
          set({ voiceList: mergedVoices, isLoading: false });
        } catch (error) {
          console.error('加载音色列表失败:', error);
          set({ isLoading: false });
        }
      },

      // 上传音色文件
      uploadVoice: async (file: File) => {
        set({ isUploading: true });

        try {
          const result = await uploadVoiceFile(file);
          const fileName = result.name;
          const currentState = get();

          // 添加到音色列表
          const newVoiceList = currentState.voiceList.includes(fileName)
            ? currentState.voiceList
            : [...currentState.voiceList, fileName];

          set({ voiceList: newVoiceList, isUploading: false });

          // 返回完整的结果信息，包含转换状态
          return result;
        } catch (error) {
          set({ isUploading: false });
          throw error;
        }
      },

      // 删除音色文件
      deleteVoice: async (fileName: string) => {
        try {
          const success = await deleteVoiceFile(fileName);

          if (success) {
            const currentState = get();
            const newVoiceList = currentState.voiceList.filter(voice => voice !== fileName);
            set({ voiceList: newVoiceList });
          }

          return success;
        } catch (error) {
          console.error('删除音色文件失败:', error);
          return false;
        }
      },

      // 刷新音色列表
      refreshVoiceList: async () => {
        set({ isLoading: true });

        try {
          // 从服务器重新获取音色文件列表
          const serverVoices = await getVoiceFileList();
          const currentState = get();

          // 合并服务器音色和本地持久化的音色列表
          const mergedVoices = Array.from(new Set([
            ...serverVoices,
            ...currentState.voiceList.filter(voice => !serverVoices.includes(voice))
          ]));

          console.log('刷新后的音色列表:', mergedVoices);
          set({ voiceList: mergedVoices, isLoading: false });
        } catch (error) {
          console.error('刷新音色列表失败:', error);
          set({ isLoading: false });
        }
      },

      // 设置ComfyUI服务器地址（文本转音频）
      setComfyUIServer: (server: string) => {
        set({ comfyUIServer: server });
      },

      // 设置ComfyUI服务器地址（音频转视频）
      setComfyUIVideoServer: (server: string) => {
        set({ comfyUIVideoServer: server });
      },

      // 设置ComfyUI基础路径
      setComfyUIBasePath: (path: string) => {
        set({ comfyUIBasePath: path });
      },

      // 设置HeyGem服务器地址
      setHeygemServerUrl: (url: string) => {
        set({ heygemServerUrl: url });
      },

      // 设置数字人服务器脚本路径
      setDigitalHumanScriptPath: (path: string) => {
        set({ digitalHumanScriptPath: path });
      },

      // 设置自动微调开关
      setAutoTuningEnabled: (enabled: boolean) => {
        set({ autoTuningEnabled: enabled });
      },

      // 设置RTMP推流服务器地址
      setRtmpServerUrl: (url: string) => {
        set({ rtmpServerUrl: url });
      },

      // 设置RTMP推流服务器端口
      setRtmpServerPort: (port: number) => {
        set({ rtmpServerPort: port });
      },

      // 脚本路径管理
      addScriptPath: (name: string, path: string) => {
        set(state => ({
          scriptPaths: [
            ...state.scriptPaths,
            {
              id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
              name,
              path,
              isRunning: false
            }
          ]
        }));
      },

      removeScriptPath: (id: string) => {
        set(state => ({
          scriptPaths: state.scriptPaths.filter(item => item.id !== id)
        }));
      },

      updateScriptPath: (id: string, name: string, path: string) => {
        set(state => ({
          scriptPaths: state.scriptPaths.map(item =>
            item.id === id ? { ...item, name, path } : item
          )
        }));
      },

      updateScriptPathStatus: (id: string, isRunning: boolean, pid?: number) => {
        set(state => ({
          scriptPaths: state.scriptPaths.map(item =>
            item.id === id ? { ...item, isRunning, pid } : item
          )
        }));
      },
    }),
    {
      name: 'voice-settings-storage',
      partialize: (state: VoiceSettingsState) => ({
        voiceList: state.voiceList,
        comfyUIServer: state.comfyUIServer,
        comfyUIVideoServer: state.comfyUIVideoServer,
        comfyUIBasePath: state.comfyUIBasePath,
        heygemServerUrl: state.heygemServerUrl,
        digitalHumanScriptPath: state.digitalHumanScriptPath,
        autoTuningEnabled: state.autoTuningEnabled,
        rtmpServerUrl: state.rtmpServerUrl,
        rtmpServerPort: state.rtmpServerPort,
        scriptPaths: state.scriptPaths,
      }),
    }
  )
)

// 导出便捷的选择器 hooks
export const useVoiceList = () => useVoiceSettings(state => state.voiceList)
export const useVoiceLoading = () => useVoiceSettings(state => state.isLoading)
export const useVoiceUploading = () => useVoiceSettings(state => state.isUploading)
export const useComfyUIServer = () => useVoiceSettings(state => state.comfyUIServer)
export const useComfyUIVideoServer = () => useVoiceSettings(state => state.comfyUIVideoServer)
export const useComfyUIBasePath = () => useVoiceSettings(state => state.comfyUIBasePath)
export const useHeygemServerUrl = () => useVoiceSettings(state => state.heygemServerUrl)
export const useDigitalHumanScriptPath = () => useVoiceSettings(state => state.digitalHumanScriptPath)
export const useAutoTuningEnabled = () => useVoiceSettings(state => state.autoTuningEnabled)
export const useRtmpServerUrl = () => useVoiceSettings(state => state.rtmpServerUrl)
export const useRtmpServerPort = () => useVoiceSettings(state => state.rtmpServerPort)
export const useScriptPaths = () => useVoiceSettings(state => state.scriptPaths)
export const useVoiceActions = () => {
  const loadVoiceList = useVoiceSettings(state => state.loadVoiceList)
  const uploadVoice = useVoiceSettings(state => state.uploadVoice)
  const deleteVoice = useVoiceSettings(state => state.deleteVoice)
  const refreshVoiceList = useVoiceSettings(state => state.refreshVoiceList)
  const setComfyUIServer = useVoiceSettings(state => state.setComfyUIServer)
  const setComfyUIVideoServer = useVoiceSettings(state => state.setComfyUIVideoServer)
  const setHeygemServerUrl = useVoiceSettings(state => state.setHeygemServerUrl)
  const setDigitalHumanScriptPath = useVoiceSettings(state => state.setDigitalHumanScriptPath)
  const setAutoTuningEnabled = useVoiceSettings(state => state.setAutoTuningEnabled)
  const setRtmpServerUrl = useVoiceSettings(state => state.setRtmpServerUrl)
  const setRtmpServerPort = useVoiceSettings(state => state.setRtmpServerPort)
  const addScriptPath = useVoiceSettings(state => state.addScriptPath)
  const removeScriptPath = useVoiceSettings(state => state.removeScriptPath)
  const updateScriptPath = useVoiceSettings(state => state.updateScriptPath)
  const updateScriptPathStatus = useVoiceSettings(state => state.updateScriptPathStatus)

  return {
    loadVoiceList,
    uploadVoice,
    deleteVoice,
    refreshVoiceList,
    setComfyUIServer,
    setComfyUIVideoServer,
    setHeygemServerUrl,
    setDigitalHumanScriptPath,
    setAutoTuningEnabled,
    setRtmpServerUrl,
    setRtmpServerPort,
    addScriptPath,
    removeScriptPath,
    updateScriptPath,
    updateScriptPathStatus,
  }
}

// 导出脚本路径相关的类型
export type { ScriptPathItem }
