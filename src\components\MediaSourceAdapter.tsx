import React, { useRef, useImperativeHandle, forwardRef } from 'react'
import MediaSourceSeamlessPlayer, { 
  VideoItem, 
  MediaSourceSeamlessPlayerProps, 
  MediaSourceSeamlessPlayerRef 
} from './MediaSourceSeamlessPlayer'

// 重新导出类型
export type { MediaSourceSeamlessPlayerRef }

/**
 * MediaSource流拼接播放器适配器
 * 提供与现有OBS播放器兼容的接口
 */
export const MediaSourceAdapter = forwardRef<MediaSourceSeamlessPlayerRef, MediaSourceSeamlessPlayerProps>((props, ref) => {
  const playerRef = useRef<MediaSourceSeamlessPlayerRef>(null)

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    playVideo: async (video: VideoItem) => {
      if (playerRef.current) {
        return await playerRef.current.playVideo(video)
      }
    },
    addToQueue: (video: VideoItem) => {
      if (playerRef.current) {
        playerRef.current.addToQueue(video)
      }
    },
    setQueue: async (videos: VideoItem[]) => {
      if (playerRef.current) {
        return await playerRef.current.setQueue(videos)
      }
    },
    playNext: async () => {
      if (playerRef.current) {
        return await playerRef.current.playNext()
      }
      return false
    },
    stop: () => {
      if (playerRef.current) {
        playerRef.current.stop()
      }
    },
    getState: () => {
      if (playerRef.current) {
        return playerRef.current.getState()
      }
      return null
    },
    getCurrentText: () => {
      if (playerRef.current) {
        return playerRef.current.getCurrentText()
      }
      return ''
    },
    isPlaying: () => {
      if (playerRef.current) {
        return playerRef.current.isPlaying()
      }
      return false
    }
  }), [])

  return <MediaSourceSeamlessPlayer ref={playerRef} {...props} />
})

MediaSourceAdapter.displayName = 'MediaSourceAdapter'

export default MediaSourceAdapter
