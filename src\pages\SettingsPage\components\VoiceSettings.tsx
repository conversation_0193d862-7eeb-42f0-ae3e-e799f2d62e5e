import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'

import { useToast } from '@/hooks/useToast'
import { useVoiceActions, useVoiceList, useVoiceLoading, useAutoTuningEnabled } from '@/hooks/useVoiceSettings'
import { useAutoVoice } from '@/hooks/useAutoVoice'
import { useAudioPreview } from '@/hooks/useAudioPreview'
import { getAudioFormat, isSupportedAudioFormat, getComfyUIServer } from '@/utils/voiceUpload'
import { Upload, Trash2, RefreshCw, Volume2, FileAudio, X, CheckCircle, Clock, AlertCircle, RotateCw, CheckSquare, Square, MinusSquare, Play, Pause, Download } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'


// 上传队列项接口
interface UploadQueueItem {
  id: string
  file: File
  status: 'pending' | 'converting' | 'uploading' | 'completed' | 'failed'
  progress?: number
  error?: string
  fileName?: string
  originalFormat?: string
  converted?: boolean
}

const VoiceSettings = () => {
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [uploadQueue, setUploadQueue] = useState<UploadQueueItem[]>([])
  const [isProcessingQueue, setIsProcessingQueue] = useState(false)
  const [activeVoiceTab, setActiveVoiceTab] = useState<'host' | 'assistant'>('host')
  const [isDragOver, setIsDragOver] = useState(false)

  // 音色设置相关状态
  const voiceList = useVoiceList()
  const isLoading = useVoiceLoading()
  const autoTuningEnabled = useAutoTuningEnabled()
  const { loadVoiceList, uploadVoice, deleteVoice, refreshVoiceList, setAutoTuningEnabled } = useVoiceActions()

  // 当前选中的音色列表 - 分别为主播和助理
  const {
    selectedVoices,
    hostSelectedVoices,
    assistantSelectedVoices,
    setSelectedVoices,
    setHostSelectedVoices,
    setAssistantSelectedVoices,
    toggleVoiceSelection,
    toggleHostVoiceSelection,
    toggleAssistantVoiceSelection
  } = useAutoVoice()

  // 音频预览功能
  const { previewStates, togglePreview } = useAudioPreview()

  // 初始化加载音色列表
  useEffect(() => {
    // 只有在音色列表为空或只有默认音色时才从服务器加载
    // 这样可以避免覆盖已持久化的音色列表
    if (voiceList.length <= 1) {
      console.log('音色列表为空或只有默认音色，从服务器加载音色列表');
      loadVoiceList();
    } else {
      console.log('已有持久化的音色列表，跳过服务器加载:', voiceList);
    }
  }, []) // 移除依赖，只在组件挂载时执行一次

  // 处理文件选择（支持多选）
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length > 0) {
      // 验证文件格式并创建上传队列项
      const queueItems: UploadQueueItem[] = files.map(file => {
        const originalFormat = getAudioFormat(file)
        const isSupported = isSupportedAudioFormat(file)

        if (!isSupported) {
          toast.error(`不支持的音频格式: ${file.name}`)
          return null
        }

        return {
          id: `${Date.now()}-${Math.random()}`,
          file,
          status: 'pending' as const,
          originalFormat,
          converted: false
        }
      }).filter(Boolean) as UploadQueueItem[]

      setUploadQueue(queueItems)

      // 显示格式转换提示
      const nonWavFiles = queueItems.filter(item => item.originalFormat !== 'WAV')
      if (nonWavFiles.length > 0) {
        toast.success(`${nonWavFiles.length} 个文件将自动转换为WAV格式`)
      }
    }
  }

  // 拖拽处理函数
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      // 验证文件格式并创建上传队列项
      const queueItems: UploadQueueItem[] = files.map(file => {
        const originalFormat = getAudioFormat(file)
        const isSupported = isSupportedAudioFormat(file)

        if (!isSupported) {
          toast.error(`不支持的音频格式: ${file.name}`)
          return null
        }

        return {
          id: `${Date.now()}-${Math.random()}`,
          file,
          status: 'pending' as const,
          originalFormat,
          converted: false
        }
      }).filter(Boolean) as UploadQueueItem[]

      if (queueItems.length > 0) {
        setUploadQueue(prev => [...prev, ...queueItems])
        toast.success(`已添加 ${queueItems.length} 个文件到上传队列`)

        // 显示格式转换提示
        const nonWavFiles = queueItems.filter(item => item.originalFormat !== 'WAV')
        if (nonWavFiles.length > 0) {
          toast.success(`${nonWavFiles.length} 个文件将自动转换为WAV格式`)
        }
      }
    }
  }

  // 处理单个文件上传
  const uploadSingleFile = async (queueItem: UploadQueueItem): Promise<void> => {
    try {
      // 如果需要转换格式，先显示转换状态
      if (queueItem.originalFormat !== 'WAV') {
        setUploadQueue(prev => prev.map(item =>
          item.id === queueItem.id
            ? { ...item, status: 'converting', progress: 0 }
            : item
        ))
      }

      // 更新状态为上传中
      setUploadQueue(prev => prev.map(item =>
        item.id === queueItem.id
          ? { ...item, status: 'uploading', progress: 50 }
          : item
      ))

      const result = await uploadVoice(queueItem.file)
      const fileName = typeof result === 'string' ? result : result.name

      // 自动将新上传的音色添加到选中列表
      if (!selectedVoices.includes(fileName)) {
        setSelectedVoices([...selectedVoices, fileName])
      }

      // 更新状态为完成
      setUploadQueue(prev => prev.map(item =>
        item.id === queueItem.id
          ? {
            ...item,
            status: 'completed',
            progress: 100,
            fileName,
            converted: typeof result === 'object' ? result.converted : false
          }
          : item
      ))

      const successMessage = typeof result === 'object' && result.converted
        ? `音色文件 "${fileName}" 上传成功 (已从${result.originalFormat}转换为WAV)`
        : `音色文件 "${fileName}" 上传成功`

      toast.success(successMessage)
    } catch (error) {
      console.error('上传音色文件失败:', error)
      const errorMessage = error instanceof Error ? error.message : '上传失败'

      // 更新状态为失败
      setUploadQueue(prev => prev.map(item =>
        item.id === queueItem.id
          ? { ...item, status: 'failed', error: errorMessage }
          : item
      ))

      toast.error(`${queueItem.file.name}: ${errorMessage}`)
    }
  }

  // 处理批量上传
  const handleBatchUpload = async () => {
    if (uploadQueue.length === 0) {
      toast.error('请先选择音色文件')
      return
    }

    setIsProcessingQueue(true)

    try {
      // 逐个上传文件
      for (const queueItem of uploadQueue) {
        if (queueItem.status === 'pending') {
          await uploadSingleFile(queueItem)
        }
      }

      toast.success(`批量上传完成，共处理 ${uploadQueue.length} 个文件`)
    } finally {
      setIsProcessingQueue(false)
    }
  }

  // 清空上传队列
  const clearUploadQueue = () => {
    setUploadQueue([])
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // 移除队列中的单个文件
  const removeFromQueue = (id: string) => {
    setUploadQueue(prev => prev.filter(item => item.id !== id))
  }

  // 处理删除音色
  const handleDeleteVoice = async (fileName: string) => {
    if (fileName === '默认音色.wav') {
      toast.error('默认音色不能删除')
      return
    }

    try {
      const success = await deleteVoice(fileName)
      if (success) {
        toast.success(`音色文件 "${fileName}" 删除成功`)
        // 如果删除的是当前选中的音色之一，从选中列表中移除
        if (selectedVoices.includes(fileName)) {
          const newSelectedVoices = selectedVoices.filter(v => v !== fileName)
          // 如果删除后没有选中的音色，则选择默认音色
          if (newSelectedVoices.length === 0) {
            setSelectedVoices(['默认音色.wav'])
          } else {
            setSelectedVoices(newSelectedVoices)
          }
        }
      } else {
        toast.error('删除失败')
      }
    } catch (error) {
      console.error('删除音色文件失败:', error)
      toast.error('删除失败')
    }
  }

  // 处理刷新音色列表
  const handleRefresh = async () => {
    try {
      await refreshVoiceList()
      toast.success('音色列表已刷新')
    } catch (error) {
      console.error('刷新音色列表失败:', error)
      toast.error('刷新失败')
    }
  }

  // 获取当前活动标签页的选中音色列表
  const getCurrentSelectedVoices = () => {
    return activeVoiceTab === 'host' ? hostSelectedVoices : assistantSelectedVoices
  }

  // 获取当前活动标签页的切换函数
  const getCurrentToggleFunction = () => {
    return activeVoiceTab === 'host' ? toggleHostVoiceSelection : toggleAssistantVoiceSelection
  }

  // 获取当前活动标签页的设置函数
  const getCurrentSetFunction = () => {
    return activeVoiceTab === 'host' ? setHostSelectedVoices : setAssistantSelectedVoices
  }

  // 处理音色选择切换
  const handleVoiceToggle = (voice: string) => {
    const currentSelectedVoices = getCurrentSelectedVoices()
    const toggleFunction = getCurrentToggleFunction()

    toggleFunction(voice)
    const isSelected = currentSelectedVoices.includes(voice)
    const speakerType = activeVoiceTab === 'host' ? '主播' : '助理'
    if (isSelected) {
      toast.success(`已取消选择${speakerType}音色: ${voice}`)
    } else {
      toast.success(`已选择${speakerType}音色: ${voice}`)
    }
  }

  // 批量选择所有音色
  const handleSelectAll = () => {
    const currentSelectedVoices = getCurrentSelectedVoices()
    const setFunction = getCurrentSetFunction()
    const allVoices = voiceList.filter(voice => !currentSelectedVoices.includes(voice))
    if (allVoices.length > 0) {
      setFunction([...currentSelectedVoices, ...allVoices])
      const speakerType = activeVoiceTab === 'host' ? '主播' : '助理'
      toast.success(`已选择所有${speakerType}音色 (${voiceList.length} 个)`)
    }
  }

  // 反选音色
  const handleInvertSelection = () => {
    const currentSelectedVoices = getCurrentSelectedVoices()
    const setFunction = getCurrentSetFunction()
    const unselectedVoices = voiceList.filter(voice => !currentSelectedVoices.includes(voice))
    const newSelectedVoices = unselectedVoices.length > 0 ? unselectedVoices : ['默认音色.wav']
    setFunction(newSelectedVoices)
    const speakerType = activeVoiceTab === 'host' ? '主播' : '助理'
    toast.success(`已反选${speakerType}音色 (${newSelectedVoices.length} 个已选择)`)
  }

  // 取消所有选择（保留默认音色）
  const handleDeselectAll = () => {
    const setFunction = getCurrentSetFunction()
    setFunction(['默认音色.wav'])
    const speakerType = activeVoiceTab === 'host' ? '主播' : '助理'
    toast.success(`已取消所有${speakerType}选择，保留默认音色`)
  }

  // 生成音色文件的URL
  const getVoiceFileUrl = (voiceFileName: string): string => {
    const serverUrl = getComfyUIServer()
    return `${serverUrl}/view?filename=${encodeURIComponent(voiceFileName)}&type=input&subfolder=`
  }

  // 处理音色试听
  const handleVoicePreview = async (voiceFileName: string) => {
    if (voiceFileName === '默认音色.wav') {
      toast.error('默认音色无法试听')
      return
    }

    try {
      const audioUrl = getVoiceFileUrl(voiceFileName)
      await togglePreview(voiceFileName, audioUrl)
    } catch (error) {
      console.error('音色试听失败:', error)
      toast.error('音色试听失败')
    }
  }

  // 下载音色文件
  const handleVoiceDownload = async (voiceFileName: string) => {
    if (voiceFileName === '默认音色.wav') {
      toast.error('默认音色无法下载')
      return
    }

    try {
      const audioUrl = getVoiceFileUrl(voiceFileName)
      const response = await fetch(audioUrl)

      if (!response.ok) {
        throw new Error(`下载失败: ${response.status} ${response.statusText}`)
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = voiceFileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success(`音色文件 "${voiceFileName}" 下载成功`)
    } catch (error) {
      console.error('下载音色文件失败:', error)
      toast.error('下载音色文件失败')
    }
  }

  return (
    <div className="space-y-6">


      {/* 音色选择和管理 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            音色设置
          </CardTitle>
          <CardDescription>
            管理TTS音色文件，上传自定义音色或选择已有音色
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">

          {/* 模型自动微调设置 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="text-sm font-medium">声音模型自动微调</Label>
                <p className="text-xs text-muted-foreground">
                  随机调整生成参数以降低被识别为AI的风险
                </p>
              </div>
              <Switch
                checked={autoTuningEnabled}
                onCheckedChange={setAutoTuningEnabled}
              />
            </div>
            <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-lg">
              <p className="font-medium mb-1">微调参数包括：</p>
              <ul className="list-disc pl-4 space-y-0.5">
                <li>top_k (25-35)</li>
                <li>top_p (0.75-0.85)</li>
                <li>temperature (0.95-1.05)</li>
                <li>num_beams (2-4)</li>
                <li>max_mel_tokens (750-820)</li>
                <li>max_text_tokens_per_sentence (85-100)</li>
                <li>sentences_bucket_max_size (3-5)</li>
              </ul>
              <p className="mt-2 text-xs">
                参数调整幅度较小，确保生成质量的一致性
              </p>
            </div>
          </div>

          {/* 上传新音色 */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">批量上传音色</Label>
              <p className="text-sm text-muted-foreground">
                支持多种音频格式（WAV, MP3, FLAC, OGG, AAC, M4A, WebM），文件大小不超过 50MB，可同时选择多个文件
              </p>
              <div className="text-xs text-muted-foreground bg-blue-50 dark:bg-blue-950/20 p-2 rounded-lg border border-blue-200 dark:border-blue-800">
                <p className="font-medium text-blue-700 dark:text-blue-300 mb-1">🔄 自动格式转换</p>
                <p className="text-blue-600 dark:text-blue-400">
                  非WAV格式的音频文件将自动转换为WAV格式后上传，确保最佳兼容性
                </p>
              </div>
            </div>

            {/* 拖拽上传区域 */}
            <div
              className={`
                relative border-2 border-dashed rounded-md p-4 text-center transition-all duration-200 cursor-pointer
                ${isDragOver
                  ? 'border-primary bg-primary/5 scale-[1.01]'
                  : 'border-muted-foreground/25 hover:border-muted-foreground/50 hover:bg-muted/50'
                }
                ${isProcessingQueue ? 'opacity-50 cursor-not-allowed' : ''}
              `}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onClick={() => !isProcessingQueue && fileInputRef.current?.click()}
            >
              <div className="flex items-center gap-3">
                <div className={`
                  p-2 rounded-full transition-colors duration-200 flex-shrink-0
                  ${isDragOver ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}
                `}>
                  <Upload className="h-4 w-4" />
                </div>

                <div className="flex-1 text-left">
                  <p className="text-sm font-medium">
                    {isDragOver ? '松开上传' : '拖拽或点击上传音色文件'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    支持 WAV、MP3、FLAC、OGG、AAC、M4A、WebM 等格式，最大 50MB
                  </p>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  disabled={isProcessingQueue}
                  className="flex items-center gap-1 flex-shrink-0"
                  onClick={(e) => {
                    e.stopPropagation()
                    fileInputRef.current?.click()
                  }}
                >
                  <Upload className="h-3 w-3" />
                  选择
                </Button>
              </div>

              {/* 拖拽覆盖层 */}
              {isDragOver && (
                <div className="absolute inset-0 bg-primary/10 rounded-md flex items-center justify-center">
                  <div className="text-primary font-medium text-sm">
                    松开上传文件
                  </div>
                </div>
              )}
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept=".wav,.mp3,.flac,.ogg,.aac,.m4a,.webm,audio/wav,audio/wave,audio/x-wav,audio/mp3,audio/mpeg,audio/flac,audio/ogg,audio/aac,audio/m4a,audio/webm"
              onChange={handleFileSelect}
              className="hidden"
              multiple
            />

            {/* 操作按钮 */}
            <div className="flex gap-2">
              <Button
                onClick={handleBatchUpload}
                disabled={uploadQueue.length === 0 || isProcessingQueue}
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                {isProcessingQueue ? '上传中...' : '批量上传'}
              </Button>
              {uploadQueue.length > 0 && (
                <Button
                  variant="outline"
                  onClick={clearUploadQueue}
                  disabled={isProcessingQueue}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  清空
                </Button>
              )}
            </div>

            {/* 上传队列显示 */}
            {uploadQueue.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  上传队列 ({uploadQueue.length} 个文件)
                </Label>
                <div className="max-h-48 overflow-y-auto space-y-2 border rounded-md p-3">
                  {uploadQueue.map(item => (
                    <div key={item.id} className="flex items-center justify-between p-2 border rounded-lg">
                      <div className="flex items-center gap-2 flex-1">
                        {item.status === 'pending' && <Clock className="h-4 w-4 text-muted-foreground" />}
                        {item.status === 'converting' && <RotateCw className="h-4 w-4 text-orange-500 animate-spin" />}
                        {item.status === 'uploading' && <Upload className="h-4 w-4 text-blue-500 animate-pulse" />}
                        {item.status === 'completed' && <CheckCircle className="h-4 w-4 text-green-500" />}
                        {item.status === 'failed' && <AlertCircle className="h-4 w-4 text-red-500" />}

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <div className="text-sm font-medium truncate">{item.file.name}</div>
                            {item.originalFormat && item.originalFormat !== 'WAV' && (
                              <span className="text-xs bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 px-1.5 py-0.5 rounded">
                                {item.originalFormat}→WAV
                              </span>
                            )}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {(item.file.size / 1024 / 1024).toFixed(2)} MB
                            {item.status === 'converting' && ' - 正在转换格式...'}
                            {item.status === 'uploading' && ' - 正在上传...'}
                            {item.status === 'completed' && item.fileName && ` → ${item.fileName}`}
                            {item.status === 'completed' && item.converted && ' (已转换)'}
                            {item.status === 'failed' && item.error && ` - ${item.error}`}
                          </div>
                        </div>
                      </div>

                      {item.status === 'pending' && !isProcessingQueue && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFromQueue(item.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 音色选择和管理 - 移动到底部 */}
          <Tabs value={activeVoiceTab} onValueChange={(value) => setActiveVoiceTab(value as 'host' | 'assistant')}>
            <div className="flex items-center justify-between mb-4">
              <TabsList className="grid w-[400px] grid-cols-2">
                <TabsTrigger value="host" className="flex items-center gap-2">
                  主播音色 ({hostSelectedVoices.length} 个已选择)
                </TabsTrigger>
                <TabsTrigger value="assistant" className="flex items-center gap-2">
                  助理音色 ({assistantSelectedVoices.length} 个已选择)
                </TabsTrigger>
              </TabsList>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
            </div>

            <TabsContent value={activeVoiceTab} className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">
                  选择{activeVoiceTab === 'host' ? '主播' : '助理'}音色 ({getCurrentSelectedVoices().length} 个已选择)
                </Label>
              </div>

              {/* 批量操作按钮 */}
              <div className="flex items-center gap-2 flex-wrap">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  disabled={isLoading || voiceList.length === 0 || getCurrentSelectedVoices().length === voiceList.length}
                  className="flex items-center gap-1"
                >
                  <CheckSquare className="h-3 w-3" />
                  全选
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleInvertSelection}
                  disabled={isLoading || voiceList.length === 0}
                  className="flex items-center gap-1"
                >
                  <MinusSquare className="h-3 w-3" />
                  反选
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDeselectAll}
                  disabled={isLoading || getCurrentSelectedVoices().length <= 1}
                  className="flex items-center gap-1"
                >
                  <Square className="h-3 w-3" />
                  取消全选
                </Button>
              </div>

              {/* 音色列表 - 高度自适应，无滚动 */}
              <div className="space-y-2 border rounded-md p-3">
                {voiceList.length > 0 ? (
                  voiceList.map(voice => (
                    <div key={voice} className="group flex items-center justify-between p-2 border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="flex items-center space-x-2 flex-1">
                        <input
                          type="checkbox"
                          id={`voice-${activeVoiceTab}-${voice}`}
                          checked={getCurrentSelectedVoices().includes(voice)}
                          onChange={() => handleVoiceToggle(voice)}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <label
                          htmlFor={`voice-${activeVoiceTab}-${voice}`}
                          className="flex items-center gap-2 text-sm cursor-pointer flex-1"
                        >
                          <FileAudio className="h-4 w-4 text-muted-foreground" />
                          {voice}
                        </label>
                        {getCurrentSelectedVoices().includes(voice) && (
                          <span className="text-xs bg-primary text-primary-foreground px-2 py-1 rounded">
                            已选择
                          </span>
                        )}
                        {voice === '默认音色.wav' && (
                          <span className="text-xs bg-secondary text-secondary-foreground px-2 py-1 rounded">
                            默认
                          </span>
                        )}
                      </div>

                      <div className="flex items-center gap-1">
                        {/* 试听按钮 */}
                        {voice !== '默认音色.wav' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleVoicePreview(voice)}
                            className="opacity-0 group-hover:opacity-100 transition-opacity"
                            title={previewStates[voice]?.isPlaying ? "停止试听" : "试听音色"}
                          >
                            {previewStates[voice]?.isPlaying ? (
                              <Pause className="h-3 w-3" />
                            ) : (
                              <Play className="h-3 w-3" />
                            )}
                          </Button>
                        )}

                        {/* 下载按钮 */}
                        {voice !== '默认音色.wav' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleVoiceDownload(voice)}
                            className="opacity-0 group-hover:opacity-100 transition-opacity"
                            title="下载音色文件"
                          >
                            <Download className="h-3 w-3" />
                          </Button>
                        )}

                        {/* 删除按钮 */}
                        {voice !== '默认音色.wav' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-destructive hover:text-destructive opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => handleDeleteVoice(voice)}
                            title="删除音色文件"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))
                ) : !isLoading ? (
                  <div className="text-center text-muted-foreground py-8">
                    暂无音色文件，请上传音色文件
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    加载中...
                  </div>
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                生成语音时将从选中的{activeVoiceTab === 'host' ? '主播' : '助理'}音色中随机选择一个使用
              </p>
            </TabsContent>
          </Tabs>

        </CardContent>
      </Card>
    </div>
  )
}

export default VoiceSettings
