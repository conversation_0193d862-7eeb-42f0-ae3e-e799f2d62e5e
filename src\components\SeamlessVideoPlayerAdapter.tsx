import React, { useRef, useImperativeHandle, forwardRef } from 'react'
import SeamlessVideoPlayer, { VideoItem, SeamlessVideoPlayerProps, SeamlessVideoPlayerRef } from './SeamlessVideoPlayer'

// 重新导出类型
export type { SeamlessVideoPlayerRef }

/**
 * 无缝视频播放器适配器
 * 提供与现有OBS播放器兼容的接口
 */
export const SeamlessVideoPlayerAdapter = forwardRef<SeamlessVideoPlayerRef, SeamlessVideoPlayerProps>((props, ref) => {
  const playerRef = useRef<SeamlessVideoPlayerRef>(null)

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    playVideo: async (video: VideoItem) => {
      if (playerRef.current) {
        return await playerRef.current.playVideo(video)
      }
    },
    addToQueue: (video: VideoItem) => {
      if (playerRef.current) {
        playerRef.current.addToQueue(video)
      }
    },
    setQueue: (videos: VideoItem[]) => {
      if (playerRef.current) {
        playerRef.current.setQueue(videos)
      }
    },
    playNext: async () => {
      if (playerRef.current) {
        return await playerRef.current.playNext()
      }
      return false
    },
    stop: () => {
      if (playerRef.current) {
        playerRef.current.stop()
      }
    },
    getState: () => {
      if (playerRef.current) {
        return playerRef.current.getState()
      }
      return null
    },
    getCurrentText: () => {
      if (playerRef.current) {
        return playerRef.current.getCurrentText()
      }
      return ''
    },
    isPlaying: () => {
      if (playerRef.current) {
        return playerRef.current.isPlaying()
      }
      return false
    }
  }), [])

  return <SeamlessVideoPlayer ref={playerRef} {...props} />
})

SeamlessVideoPlayerAdapter.displayName = 'SeamlessVideoPlayerAdapter'

export default SeamlessVideoPlayerAdapter
