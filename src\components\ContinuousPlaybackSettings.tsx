import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/useToast'
import {
  Play,
  Pause,
  RotateCcw,
  Settings,
  Clock,
  Video,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface ContinuousPlaybackConfig {
  enabled: boolean
  seamlessTransition: boolean
}

const DEFAULT_CONFIG: ContinuousPlaybackConfig = {
  enabled: true,
  seamlessTransition: true
}

export function ContinuousPlaybackSettings() {
  const { toast } = useToast()
  const [config, setConfig] = useState<ContinuousPlaybackConfig>(DEFAULT_CONFIG)
  const [status, setStatus] = useState<any>(null)
  const [ffmpegAvailable, setFFmpegAvailable] = useState<boolean | null>(null)

  // 从localStorage加载配置
  useEffect(() => {
    try {
      const saved = localStorage.getItem('continuous-playback-config')
      if (saved) {
        const parsedConfig = JSON.parse(saved)
        setConfig({ ...DEFAULT_CONFIG, ...parsedConfig })
      }
    } catch (error) {
      console.error('加载连续播放配置失败:', error)
    }

    // 检查FFmpeg是否可用
    checkFFmpegAvailability()
  }, [])

  // 检查FFmpeg是否可用
  const checkFFmpegAvailability = async () => {
    try {
      const { IPC_CHANNELS } = await import('../../shared/ipcChannels')
      const available = await (window as any).ipcRenderer?.invoke(IPC_CHANNELS.video.checkFFmpeg)
      setFFmpegAvailable(available)
    } catch (error) {
      console.error('检查FFmpeg失败:', error)
      setFFmpegAvailable(false)
    }
  }

  // 保存配置到localStorage
  const saveConfig = (newConfig: ContinuousPlaybackConfig) => {
    try {
      localStorage.setItem('continuous-playback-config', JSON.stringify(newConfig))
      setConfig(newConfig)

      // 更新全局配置
      updateGlobalConfig(newConfig)

      toast.success('连续播放配置已保存')
    } catch (error) {
      console.error('保存连续播放配置失败:', error)
      toast.error('保存配置失败')
    }
  }

  // 更新全局配置
  const updateGlobalConfig = async (newConfig: ContinuousPlaybackConfig) => {
    try {
      const { updateContinuousPlaybackConfig } = await import('@/utils/continuousDigitalHuman')
      updateContinuousPlaybackConfig(newConfig)
    } catch (error) {
      console.error('更新全局配置失败:', error)
    }
  }

  // 获取状态信息
  const refreshStatus = async () => {
    try {
      const { getContinuousPlaybackStatus } = await import('@/utils/continuousDigitalHuman')
      const currentStatus = getContinuousPlaybackStatus()
      setStatus(currentStatus)
    } catch (error) {
      console.error('获取状态失败:', error)
    }
  }

  // 重置播放状态
  const resetPlayback = async () => {
    try {
      const { resetContinuousPlayback } = await import('@/utils/continuousDigitalHuman')
      resetContinuousPlayback()
      await refreshStatus()
      toast.success('播放状态已重置')
    } catch (error) {
      console.error('重置播放状态失败:', error)
      toast.error('重置失败')
    }
  }

  // 处理配置变更
  const handleConfigChange = (key: keyof ContinuousPlaybackConfig, value: any) => {
    const newConfig = { ...config, [key]: value }
    saveConfig(newConfig)
  }

  // 格式化时长显示
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Video className="h-5 w-5" />
          连续播放设置
        </CardTitle>
        <CardDescription>
          配置数字人视频的连续播放功能，实现画面无缝衔接
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 基础设置 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="continuous-enabled">启用连续播放</Label>
              <p className="text-sm text-muted-foreground">
                根据音频时长智能切割形象视频，确保画面连续
              </p>
            </div>
            <Switch
              id="continuous-enabled"
              checked={config.enabled}
              onCheckedChange={(checked) => handleConfigChange('enabled', checked)}
            />
          </div>

          <Separator />

          {config.enabled && (
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="seamless-transition">无缝切换</Label>
                <p className="text-sm text-muted-foreground">
                  启用高级无缝切换技术
                </p>
              </div>
              <Switch
                id="seamless-transition"
                checked={config.seamlessTransition}
                onCheckedChange={(checked) => handleConfigChange('seamlessTransition', checked)}
              />
            </div>
          )}
        </div>

        {/* 状态信息 */}
        {config.enabled && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">播放状态</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshStatus}
                  className="flex items-center gap-2"
                >
                  <Info className="h-4 w-4" />
                  刷新状态
                </Button>
              </div>

              {status && (
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="space-y-1">
                    <Label>拼接总时长</Label>
                    <Badge variant="secondary">
                      <Clock className="h-3 w-3 mr-1" />
                      {formatDuration(status.concatenatedDuration)}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <Label>当前偏移</Label>
                    <Badge variant="secondary">
                      <Play className="h-3 w-3 mr-1" />
                      {formatDuration(status.currentOffset)}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <Label>剩余时长</Label>
                    <Badge variant="secondary">
                      <Pause className="h-3 w-3 mr-1" />
                      {formatDuration(status.remainingDuration)}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <Label>操作</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={resetPlayback}
                      className="flex items-center gap-1"
                    >
                      <RotateCcw className="h-3 w-3" />
                      重置
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </>
        )}

        {/* FFmpeg状态检查 */}
        {ffmpegAvailable !== null && (
          <div className={`p-4 rounded-lg ${ffmpegAvailable ? 'bg-green-50 border border-green-200' : 'bg-orange-50 border border-orange-200'}`}>
            <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
              {ffmpegAvailable ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-orange-600" />
              )}
              FFmpeg状态
            </h4>
            {ffmpegAvailable ? (
              <p className="text-xs text-green-700">
                FFmpeg已安装，支持视频切割功能
              </p>
            ) : (
              <div className="text-xs text-orange-700 space-y-1">
                <p>FFmpeg未安装或不可用，连续播放功能将受限</p>
                <p>请安装FFmpeg以获得完整的视频切割功能</p>
              </div>
            )}
          </div>
        )}

        {/* 说明信息 */}
        <div className="bg-muted/50 p-4 rounded-lg">
          <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
            <Info className="h-4 w-4" />
            功能说明
          </h4>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• 根据音频时长智能切割形象视频片段</li>
            <li>• 确保数字人画面在多个语音间连续播放</li>
            <li>• 自动处理视频循环，无需手动配置</li>
            <li>• 需要安装FFmpeg以支持视频切割功能</li>
            <li>• 简化处理逻辑，动态适应音频长度</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
