import { useRef, useEffect, useState, forwardRef, useImperativeHandle } from 'react'

interface FLVPlayerProps {
  src?: string
  autoPlay?: boolean
  muted?: boolean
  volume?: number
  className?: string
  onError?: (error: Error) => void
  onLoadStart?: () => void
  onCanPlay?: () => void
}

export interface FLVPlayerRef {
  play: () => Promise<void>
  pause: () => void
  setVolume: (volume: number) => void
  setMuted: (muted: boolean) => void
}

/**
 * FLV播放器组件
 * 支持HTTP-FLV直播流播放
 */
export const FLVPlayer = forwardRef<FLVPlayerRef, FLVPlayerProps>(({
  src,
  autoPlay = true,
  muted = false,
  volume = 1,
  className = '',
  onError,
  onLoadStart,
  onCanPlay
}, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    play: async () => {
      if (videoRef.current) {
        try {
          await videoRef.current.play()
        } catch (error) {
          console.error('播放失败:', error)
          throw error
        }
      }
    },
    pause: () => {
      if (videoRef.current) {
        videoRef.current.pause()
      }
    },
    setVolume: (newVolume: number) => {
      if (videoRef.current) {
        videoRef.current.volume = Math.max(0, Math.min(1, newVolume))
      }
    },
    setMuted: (newMuted: boolean) => {
      if (videoRef.current) {
        videoRef.current.muted = newMuted
      }
    }
  }), [])

  // 处理视频源变化
  useEffect(() => {
    if (!videoRef.current || !src) {
      return
    }

    const video = videoRef.current
    setIsLoading(true)
    setError(null)

    // 设置视频源
    video.src = src

    // 事件处理器
    const handleLoadStart = () => {
      setIsLoading(true)
      onLoadStart?.()
    }

    const handleCanPlay = () => {
      setIsLoading(false)
      onCanPlay?.()
      
      // 自动播放
      if (autoPlay) {
        video.play().catch(error => {
          console.error('自动播放失败:', error)
          setError('自动播放失败')
          onError?.(error)
        })
      }
    }

    const handleError = (event: Event) => {
      setIsLoading(false)
      const errorMsg = '视频加载失败'
      setError(errorMsg)
      console.error('FLV播放器错误:', event)
      onError?.(new Error(errorMsg))
    }

    const handleWaiting = () => {
      setIsLoading(true)
    }

    const handlePlaying = () => {
      setIsLoading(false)
    }

    // 添加事件监听器
    video.addEventListener('loadstart', handleLoadStart)
    video.addEventListener('canplay', handleCanPlay)
    video.addEventListener('error', handleError)
    video.addEventListener('waiting', handleWaiting)
    video.addEventListener('playing', handlePlaying)

    // 清理函数
    return () => {
      video.removeEventListener('loadstart', handleLoadStart)
      video.removeEventListener('canplay', handleCanPlay)
      video.removeEventListener('error', handleError)
      video.removeEventListener('waiting', handleWaiting)
      video.removeEventListener('playing', handlePlaying)
    }
  }, [src, autoPlay, onError, onLoadStart, onCanPlay])

  // 处理音量和静音变化
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.volume = Math.max(0, Math.min(1, volume))
    }
  }, [volume])

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.muted = muted
    }
  }, [muted])

  return (
    <div className={`relative w-full h-full ${className}`}>
      <video
        ref={videoRef}
        className="w-full h-full object-contain bg-black"
        autoPlay={autoPlay}
        muted={muted}
        playsInline
        controls={false}
      />
      
      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="text-white text-sm">加载中...</div>
        </div>
      )}
      
      {/* 错误状态 */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
          <div className="text-red-400 text-sm text-center">
            <div>{error}</div>
            <div className="text-xs mt-2 text-gray-400">
              请检查推流服务器是否正常运行
            </div>
          </div>
        </div>
      )}
      
      {/* 无源状态 */}
      {!src && !error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black">
          <div className="text-gray-400 text-sm text-center">
            <div>等待推流...</div>
            <div className="text-xs mt-2">
              请启动推流服务器并开始推流
            </div>
          </div>
        </div>
      )}
    </div>
  )
})

FLVPlayer.displayName = 'FLVPlayer'
