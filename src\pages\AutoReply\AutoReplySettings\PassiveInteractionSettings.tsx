import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { useAutoReply } from '@/hooks/useAutoReply'
import { useAIChatStore } from '@/hooks/useAIChat'
import { useToast } from '@/hooks/useToast'
import { pick } from 'lodash-es'
import { Sparkles, Loader2, Settings, ChevronDown, ChevronRight, Trash2 } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import ReplyMessageManager from './ReplyMessageManager'
import type { EventMessageType, SimpleEventReplyMessage } from '@/hooks/useAutoReply'


// 被动互动类型配置
const passiveInteractionTypes = [
  {
    id: 'room_enter' as EventMessageType,
    name: '进入直播间',
    description: '用户进入直播间时播放语音',
    defaultMessages: [
      '欢迎{用户名}来到直播间！',
      '{用户名}欢迎你！',
      '感谢{用户名}的到来！'
    ],
  },
  {
    id: 'room_like' as EventMessageType,
    name: '点赞',
    description: '用户点赞时播放语音',
    defaultMessages: [
      '感谢{用户名}的点赞！',
      '{用户名}的点赞收到了！',
      '谢谢{用户名}的支持！'
    ],
  },
  {
    id: 'room_follow' as EventMessageType,
    name: '关注',
    description: '用户关注时播放语音',
    defaultMessages: [
      '感谢{用户名}的关注！',
      '欢迎{用户名}关注我们！',
      '{用户名}关注成功！'
    ],
  },
  {
    id: 'subscribe_merchant_brand_vip' as EventMessageType,
    name: '加入品牌会员',
    description: '用户加入品牌会员时播放语音',
    defaultMessages: [
      '欢迎{用户名}加入品牌会员！',
      '感谢{用户名}成为品牌会员！',
      '{用户名}会员权益已激活！'
    ],
  },
  {
    id: 'live_order' as EventMessageType,
    name: '下单',
    description: '用户下单时播放语音',
    defaultMessages: [
      '感谢{用户名}的下单！',
      '{用户名}下单成功！',
      '谢谢{用户名}的购买！'
    ],
  },
  {
    id: 'ecom_fansclub_participate' as EventMessageType,
    name: '参与粉丝团活动',
    description: '用户参与粉丝团活动时播放语音',
    defaultMessages: [
      '感谢{用户名}参与粉丝团活动！',
      '{用户名}粉丝团活动参与成功！',
      '欢迎{用户名}加入粉丝团！'
    ],
  },
]

// 默认AI泛化提示词
const DEFAULT_GENERALIZATION_PROMPT = `你是一个专业的直播被动互动语音泛化助手。你的任务是将给定的被动互动语音进行多样化改写，保持原意的同时增加表达的丰富性。

请遵循以下原则：
1. 保持原语音的核心意思和目的不变
2. 使用不同的表达方式、语气和词汇
3. 适合直播场景，语言要亲切自然
4. 每个变体都应该是完整的、可直接使用的语音内容
5. 保持{用户名}变量不变
6. 生成5-8个不同的变体
7. 语音内容要简短有力，适合快速播放

请对以下被动互动语音进行泛化改写：
{original_content}

请直接返回变体列表，每行一个变体，不需要编号或其他格式。`

const PassiveInteractionSettings = () => {
  const {
    config,
    updatePassiveInteractionContents,
    updatePassiveInteractionEnabled,
    updatePassiveInteractionOptions,
  } = useAutoReply()

  const aiStore = useAIChatStore()
  const { toast } = useToast()

  // AI泛化相关状态
  const [generalizingTypes, setGeneralizingTypes] = useState<Set<EventMessageType>>(new Set())
  const [showPromptDialog, setShowPromptDialog] = useState(false)
  const [generalizationPrompt, setGeneralizationPrompt] = useState(() => {
    return localStorage.getItem('passive-interaction-generalization-prompt') || DEFAULT_GENERALIZATION_PROMPT
  })
  const [tempPrompt, setTempPrompt] = useState(generalizationPrompt)

  // 展开/收起状态管理
  const [expandedTypes, setExpandedTypes] = useState<Set<EventMessageType>>(() => {
    const saved = localStorage.getItem('passive-interaction-expanded-types')
    if (saved) {
      try {
        return new Set(JSON.parse(saved))
      } catch {
        return new Set()
      }
    }
    return new Set()
  })

  const passiveInteractionReplies = pick(config.passiveInteraction || {
    room_enter: { enable: false, messages: [], options: { intervalMinSeconds: 30, intervalMaxSeconds: 60 } },
    room_like: { enable: false, messages: [], options: { intervalMinSeconds: 30, intervalMaxSeconds: 60 } },
    room_follow: { enable: false, messages: [], options: { intervalMinSeconds: 30, intervalMaxSeconds: 60 } },
    subscribe_merchant_brand_vip: { enable: false, messages: [], options: { intervalMinSeconds: 30, intervalMaxSeconds: 60 } },
    live_order: { enable: false, messages: [], options: { onlyReplyPaid: false, intervalMinSeconds: 30, intervalMaxSeconds: 60 } },
    ecom_fansclub_participate: { enable: false, messages: [], options: { intervalMinSeconds: 30, intervalMaxSeconds: 60 } },
  }, [
    'room_enter',
    'room_like',
    'room_follow',
    'subscribe_merchant_brand_vip',
    'live_order',
    'ecom_fansclub_participate',
  ])

  // AI泛化处理函数
  const handleGeneralize = async (messageType: EventMessageType) => {
    const currentMessages = passiveInteractionReplies[messageType]?.messages || []
    if (currentMessages.length === 0) {
      toast.error('请先添加至少1条语音消息才能进行AI泛化')
      return
    }

    const { provider } = aiStore.config
    const apiKey = aiStore.apiKeys[provider]
    const customBaseURL = aiStore.customBaseURL

    if (!apiKey) {
      toast.error('请先配置AI API密钥')
      return
    }

    setGeneralizingTypes(prev => new Set([...prev, messageType]))

    try {
      // 使用第一条消息进行泛化
      const firstMessage = currentMessages[0]
      const originalContent = typeof firstMessage === 'string' ? firstMessage : firstMessage.content
      const prompt = generalizationPrompt.replace('{original_content}', originalContent)

      const messages = [
        {
          role: 'user' as const,
          content: prompt,
        },
      ]

      const response = await window.ipcRenderer.invoke(IPC_CHANNELS.tasks.aiChat.normalChat, {
        messages,
        apiKey,
        provider: aiStore.config.provider,
        model: aiStore.config.model,
        customBaseURL: aiStore.config.provider === 'custom' ? customBaseURL : undefined,
      })

      if (!response) {
        throw new Error('AI服务返回空结果')
      }

      // 解析AI返回的结果，按行分割
      const generatedContents = response
        .split('\n')
        .map((line: string) => line.trim())
        .filter((line: string) => line.length > 0 && !line.match(/^\d+\./)) // 过滤空行和编号
        .slice(0, 8) // 最多取8个变体

      if (generatedContents.length > 0) {
        // 添加到现有消息列表
        const newMessages = [...currentMessages, ...generatedContents]
        updatePassiveInteractionContents(messageType, newMessages)
        toast.success(`成功生成 ${generatedContents.length} 条语音变体`)
      } else {
        toast.error('AI生成的内容为空')
      }
    } catch (error) {
      console.error('AI泛化失败:', error)
      toast.error('AI泛化失败，请检查AI服务配置')
    } finally {
      setGeneralizingTypes(prev => {
        const newSet = new Set(prev)
        newSet.delete(messageType)
        return newSet
      })
    }
  }

  // 提示词设置相关函数
  const handleOpenPromptDialog = () => {
    setTempPrompt(generalizationPrompt)
    setShowPromptDialog(true)
  }

  const handleSavePrompt = () => {
    setGeneralizationPrompt(tempPrompt)
    localStorage.setItem('passive-interaction-generalization-prompt', tempPrompt)
    setShowPromptDialog(false)
    toast.success('AI泛化提示词已保存')
  }

  const handleCancelPrompt = () => {
    setTempPrompt(generalizationPrompt)
    setShowPromptDialog(false)
  }

  // 展开/收起切换
  const toggleExpanded = (type: EventMessageType) => {
    setExpandedTypes(prev => {
      const newSet = new Set(prev)
      if (newSet.has(type)) {
        newSet.delete(type)
      } else {
        newSet.add(type)
      }
      // 保存到本地存储
      localStorage.setItem('passive-interaction-expanded-types', JSON.stringify([...newSet]))
      return newSet
    })
  }

  // 被动互动开关
  const handleInteractionChange = (type: EventMessageType, checked: boolean) => {
    updatePassiveInteractionEnabled(type, checked)

    // 如果启用且没有消息，则添加默认消息
    if (checked) {
      const currentMessages = passiveInteractionReplies[type]?.messages || []
      if (currentMessages.length === 0) {
        const typeConfig = passiveInteractionTypes.find(t => t.id === type)
        if (typeConfig) {
          updatePassiveInteractionContents(type, typeConfig.defaultMessages)
        }
      }
      // 启用时自动展开
      setExpandedTypes(prev => {
        const newSet = new Set(prev)
        newSet.add(type)
        localStorage.setItem('passive-interaction-expanded-types', JSON.stringify([...newSet]))
        return newSet
      })
    }
  }

  // 处理被动互动消息内容
  const handleMessageAdd = (
    type: EventMessageType,
    message: SimpleEventReplyMessage,
  ) => {
    const currentMessages = passiveInteractionReplies[type]?.messages || []
    updatePassiveInteractionContents(type, [...currentMessages, message])
  }

  const handleOptionsChange = (
    type: EventMessageType,
    options: Record<string, boolean | number>,
  ) => {
    updatePassiveInteractionOptions(type, options)
  }

  // 处理间隔时间变化
  const handleIntervalChange = (type: EventMessageType, minInterval: number, maxInterval: number) => {
    const currentOptions = passiveInteractionReplies[type]?.options || {}
    handleOptionsChange(type, {
      ...currentOptions,
      intervalMinSeconds: minInterval,
      intervalMaxSeconds: maxInterval
    })
  }

  const handleMessageRemove = (type: EventMessageType, index: number) => {
    const currentMessages = passiveInteractionReplies[type]?.messages || []
    const newMessages = currentMessages.filter((_, i) => i !== index)
    updatePassiveInteractionContents(type, newMessages)
  }

  // 清空消息
  const handleClearMessages = (type: EventMessageType) => {
    updatePassiveInteractionContents(type, [])
    toast.success('消息已清空')
  }

  return (
    <div className="space-y-4">
      {/* 页面标题和全局AI泛化设置 */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h3 className="text-lg font-medium">直播间事件互动</h3>
          <p className="text-sm text-muted-foreground">
            对非评论的消息类型进行语音回应，回应内容将添加到语音输出列表中播放
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleOpenPromptDialog}
          title="设置AI泛化提示词"
        >
          <Settings className="h-4 w-4 mr-2" />
          AI泛化设置
        </Button>
      </div>

      <Separator />

      <div className="space-y-6">
        {passiveInteractionTypes.map(type => (
          <div key={type.id} className="space-y-2">
            <div className="flex items-center justify-between">
              <div
                className={`flex items-center gap-2 flex-1 ${passiveInteractionReplies[type.id]?.enable
                  ? 'cursor-pointer hover:bg-muted/50 rounded-md p-1 -m-1 transition-colors'
                  : ''
                  }`}
                onClick={passiveInteractionReplies[type.id]?.enable ? () => toggleExpanded(type.id) : undefined}
              >
                {passiveInteractionReplies[type.id]?.enable && (
                  <div className="flex-shrink-0">
                    {expandedTypes.has(type.id) ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </div>
                )}
                <div className="space-y-0.5 flex-1">
                  <h4 className="text-sm font-medium">
                    {type.name}
                  </h4>
                  <p className="text-xs text-muted-foreground">
                    {type.description}
                  </p>
                </div>
              </div>
              <Switch
                checked={passiveInteractionReplies[type.id]?.enable || false}
                onCheckedChange={checked =>
                  handleInteractionChange(type.id, checked)
                }
              />
            </div>

            {passiveInteractionReplies[type.id]?.enable && expandedTypes.has(type.id) && (
              <Card className="border-dashed">
                <CardContent className="pt-4">
                  <div className="space-y-4">
                    {/* 标题和AI泛化按钮 */}
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium">语音互动</h5>
                        <p className="text-xs text-muted-foreground">
                          系统将从以下消息中随机选择一条转换为语音播放，可使用{'{用户名}'}变量
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleClearMessages(type.id)}
                          title="清空所有消息"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleGeneralize(type.id)}
                          disabled={generalizingTypes.has(type.id)}
                          title="使用第一条消息进行AI泛化"
                        >
                          {generalizingTypes.has(type.id) ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Sparkles className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>

                    <ReplyMessageManager
                      title=""
                      description=""
                      messages={
                        passiveInteractionReplies[type.id]?.messages || type.defaultMessages
                      }
                      onAdd={message =>
                        handleMessageAdd(type.id, message)
                      }
                      onRemove={index =>
                        handleMessageRemove(type.id, index)
                      }
                      placeholder={`例如：${type.defaultMessages[0]}`}
                      msgType={type.id}
                    />

                    {/* 互动频率设置 */}
                    <Separator className="mt-4" />
                    <div className="flex justify-between items-center pt-4 text-sm">
                      <div className="flex flex-col">
                        <span>互动频率控制</span>
                        <span className="text-muted-foreground">
                          设置随机间隔时间范围（秒），避免频繁触发被动互动
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Label className="text-sm">间隔：</Label>
                        <Input
                          type="number"
                          min="0"
                          max="3600"
                          value={
                            (passiveInteractionReplies[type.id]?.options?.intervalMinSeconds as number) || 30
                          }
                          onChange={e => {
                            const minValue = parseInt(e.target.value) || 0
                            const maxValue = (passiveInteractionReplies[type.id]?.options?.intervalMaxSeconds as number) || 60
                            handleIntervalChange(type.id, minValue, Math.max(minValue, maxValue))
                          }}
                          className="w-16 text-center"
                          placeholder="最小"
                        />
                        <span className="text-sm text-muted-foreground">-</span>
                        <Input
                          type="number"
                          min="0"
                          max="3600"
                          value={
                            (passiveInteractionReplies[type.id]?.options?.intervalMaxSeconds as number) || 60
                          }
                          onChange={e => {
                            const maxValue = parseInt(e.target.value) || 0
                            const minValue = (passiveInteractionReplies[type.id]?.options?.intervalMinSeconds as number) || 30
                            handleIntervalChange(type.id, Math.min(minValue, maxValue), maxValue)
                          }}
                          className="w-16 text-center"
                          placeholder="最大"
                        />
                        <span className="text-sm text-muted-foreground">秒</span>
                      </div>
                    </div>
                  </div>
                  {
                    // 单独处理已下单、已支付的语音回应
                    type.id === 'live_order' && (
                      <>
                        <Separator className="mt-4" />
                        <div className="flex justify-between items-center pt-4 text-sm">
                          <div className="flex flex-col">
                            <span>仅在已支付时播放语音</span>
                            <span className="text-muted-foreground">
                              用户订单具有<strong>已下单</strong>和
                              <strong>已支付</strong>两种状态
                            </span>
                          </div>
                          <Switch
                            checked={
                              (passiveInteractionReplies[type.id]?.options?.onlyReplyPaid as boolean) || false
                            }
                            onCheckedChange={e =>
                              handleOptionsChange(type.id, {
                                onlyReplyPaid: e,
                              })
                            }
                          />
                        </div>
                      </>
                    )
                  }
                </CardContent>
              </Card>
            )}
          </div>
        ))}
      </div>



      {/* AI泛化提示词设置对话框 */}
      <Dialog open={showPromptDialog} onOpenChange={setShowPromptDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>被动互动AI泛化提示词设置</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Textarea
              placeholder="输入完整的AI泛化提示词..."
              value={tempPrompt}
              onChange={e => setTempPrompt(e.target.value)}
              className="text-sm min-h-[300px]"
            />
            <p className="text-xs text-muted-foreground">
              完整的AI泛化提示词，使用 <code>{'{original_content}'}</code> 作为原始内容的占位符。此设置将应用于所有被动互动类型的AI泛化。
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelPrompt}>
              取消
            </Button>
            <Button onClick={handleSavePrompt}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default PassiveInteractionSettings
